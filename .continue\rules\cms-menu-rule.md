# Lemon Web AI Coding Rules

This document provides comprehensive guidelines for AI assistants to understand and code effectively in the Lemon Web Next.js project.

## Project Architecture Overview

This is a Next.js 14+ application using the App Router pattern with TypeScript, Tailwind CSS, and shadcn/ui components following Atomic Design principles.

## Core Patterns

### 1. Page Structure Pattern

Based on `/src/app/admin/user-management/page.tsx`:

```typescript
import UserManagement from "@/components/organisms/admin/user-management";
import React from "react";

const UserManagementPage = () => {
  return <UserManagement />;
};

export default UserManagementPage;
```

**Key Rules:**
- Pages are thin wrappers that import and render organism components
- Use absolute imports with `@/` alias for components
- Always import React explicitly
- Use PascalCase for component names
- Export as default
- Keep pages minimal - business logic belongs in organisms

### 2. Component Hierarchy (Atomic Design)

**Atoms** (`src/components/atoms/`):
- Smallest UI elements that extend shadcn/ui components
- Example: `BaseButton` extends `Button` with project styling
- Use `React.forwardRef` for proper ref handling
- Apply custom styling with `cn()` utility

**Molecules** (`src/components/molecules/`):
- Combine atoms into functional components
- Feature-specific grouping (e.g., `admin/user-management/`)
- Examples: `title.tsx`, `table.tsx`, `search.tsx`, `filter.tsx`

**Organisms** (`src/components/organisms/`):
- Complete page sections combining molecules
- Handle layout and spacing between components
- Main entry point for page imports

### 3. Directory Structure Rules

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/
│   │   └── user-management/
│   │       └── page.tsx    # Page component
│   └── layout.tsx
├── components/
│   ├── atoms/              # Base UI components
│   ├── molecules/          # Feature components
│   │   └── admin/
│   │       └── user-management/
│   │           ├── title.tsx
│   │           ├── table.tsx
│   │           └── search.tsx
│   ├── organisms/          # Page sections
│   │   └── admin/
│   │       └── user-management/
│   │           └── index.tsx
│   └── ui/                 # shadcn/ui components
├── hooks/                  # Custom React hooks
├── lib/                    # Utilities (utils.ts)
├── services/               # API and data layer
└── interfaces/             # TypeScript types
```

### 4. Import/Export Conventions

**Import Order:**
1. React imports
2. Third-party libraries
3. Internal components (atoms → molecules → organisms)
4. Utilities and types

**Import Patterns:**
```typescript
// React
import React from "react";

// Third-party
import { ColumnDef } from "@tanstack/react-table";

// Internal components
import { BaseButton } from "@/components/atoms/button";
import UserManagementTitle from "./title";  // Relative for same feature
import UserManagement from "@/components/organisms/admin/user-management"; // Absolute

// Utilities
import { cn } from "@/lib/utils";
```

**Export Rules:**
- Default exports for main components
- Named exports for utilities and multiple exports
- Use descriptive component names matching file structure

### 5. File Naming Conventions

- **Directories**: `kebab-case` (e.g., `user-management`)
- **Components**: `PascalCase` for main files, `lowercase` for feature files
- **Pages**: `page.tsx` in route directories
- **Layouts**: `layout.tsx` in route directories
- **Component files**: Descriptive names (`title.tsx`, `table.tsx`)

### 6. Styling Guidelines

**Tailwind CSS Patterns:**
- Use project color palette: `#F7941E` (primary), `#FFA733` (hover), `#3C3C3C` (text)
- Consistent spacing: `gap-4`, `gap-2`, `p-3`
- Layout patterns: `flex`, `flex-col`, `justify-between`, `items-center`
- Background: `bg-white` for cards

**Class Merging:**
```typescript
className={cn(
  "base-classes",
  condition && "conditional-classes",
  className // Allow prop override
)}
```

### 7. TypeScript Patterns

**Component Props:**
```typescript
type ComponentProps = React.ComponentPropsWithoutRef<typeof BaseComponent>;

const Component = React.forwardRef<HTMLElement, ComponentProps>(
  ({ className, ...props }, ref) => {
    // Implementation
  }
);
```

**Data Types:**
```typescript
export type Payment = {
  id: string;
  amount: number;
  status: "pending" | "processing" | "success" | "failed";
  email: string;
};

export const columns: ColumnDef<Payment>[] = [
  // Column definitions
];
```

### 8. Component Creation Templates

**Page Component:**
```typescript
import FeatureComponent from "@/components/organisms/feature/component";
import React from "react";

const FeaturePage = () => {
  return <FeatureComponent />;
};

export default FeaturePage;
```

**Organism Component:**
```typescript
import FeatureTitle from "@/components/molecules/feature/title";
import FeatureTable from "@/components/molecules/feature/table";
import React from "react";

const FeatureComponent = () => {
  return (
    <div className="flex flex-col gap-4">
      <FeatureTitle />
      <FeatureTable />
    </div>
  );
};

export default FeatureComponent;
```

**Molecule Component:**
```typescript
import { BaseButton } from "@/components/atoms/button";
import React from "react";

const FeatureTitle = () => {
  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Feature Title
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseButton className="h-9 w-1/2">Action 1</BaseButton>
        <BaseButton className="h-9 w-1/2" variant="default">
          Action 2
        </BaseButton>
      </div>
    </div>
  );
};

export default FeatureTitle;
```

**Atom Component:**
```typescript
"use client";

import * as React from "react";
import { BaseComponent } from "@/components/ui/base-component";
import { cn } from "@/lib/utils";

type CustomComponentProps = React.ComponentPropsWithoutRef<typeof BaseComponent>;

const CustomComponent = React.forwardRef<HTMLElement, CustomComponentProps>(
  ({ className, ...props }, ref) => {
    return (
      <BaseComponent
        ref={ref}
        className={cn(
          "custom-styling",
          "hover:cursor-pointer",
          className
        )}
        {...props}
      />
    );
  }
);

CustomComponent.displayName = "CustomComponent";

export { CustomComponent };
```

## CMS Menu Creation Workflow

### Complete CMS Menu Creation Process

When creating a new CMS menu (e.g., "position-management"), follow this exact pattern based on the user-management structure:

#### 1. Create TypeScript Interface

**File**: `src/interfaces/admin/{feature-name}/list.ts`

```typescript
export interface I{FeatureName} {
  id: number;
  // Add your specific columns here
  // Example for position-management:
  position_name: string;
  status: boolean;
  description: string;
  last_updated: string;
  updated_by: string;
}
```

#### 2. Create Page Component

**File**: `src/app/admin/{feature-name}/page.tsx`

```typescript
import {FeatureName} from "@/components/organisms/admin/{feature-name}";
import React from "react";

const {FeatureName}Page = () => {
  return <{FeatureName} />;
};

export default {FeatureName}Page;
```

#### 3. Create Organism Component

**File**: `src/components/organisms/admin/{feature-name}/index.tsx`

```typescript
import {FeatureName}Table from "@/components/molecules/admin/{feature-name}/table";
import {FeatureName}Title from "@/components/molecules/admin/{feature-name}/title";
import React from "react";

const {FeatureName} = () => {
  return (
    <div className="flex flex-col gap-4">
      <{FeatureName}Title />
      <{FeatureName}Table />
    </div>
  );
};

export default {FeatureName};
```

#### 4. Create Title Molecule

**File**: `src/components/molecules/admin/{feature-name}/title.tsx`

```typescript
import { BaseButton } from "@/components/atoms/button";
import React from "react";

const {FeatureName}Title = () => {
  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        {Feature Display Name}
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseButton className="h-9 w-1/2">Export Data</BaseButton>
        <BaseButton className="h-9 w-1/2" variant="default">
          Add New
        </BaseButton>
      </div>
    </div>
  );
};

export default {FeatureName}Title;
```

#### 5. Create Table Molecule

**File**: `src/components/molecules/admin/{feature-name}/table.tsx`

```typescript
"use client";

import React from "react";
import {FeatureName}TableHeader from "./table-header";
import { DataTable } from "../../global/table";
import { ColumnDef } from "@tanstack/react-table";
import { I{FeatureName} } from "@/interfaces/admin/{feature-name}/list";
import dayjs from "dayjs";
import { BaseSwitch } from "@/components/atoms/switch";
import { Check, Pencil, Trash2 } from "lucide-react";
import { cn } from "@/utils/common";
import { BaseButton } from "@/components/atoms/button";

export const columns: ColumnDef<I{FeatureName}>[] = [
  // Define your columns based on the interface
  // Example for position-management:
  {
    accessorKey: "position_name",
    header: "Position Name",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell({ row }) {
      return (
        <BaseSwitch
          checked={row.original.status}
          className={cn(
            "data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500"
          )}
        />
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "last_updated",
    header: "Last Updated",
  },
  {
    accessorKey: "updated_by",
    header: "Updated By",
  },
  {
    accessorKey: "id",
    header: "Action",
    cell({ row }) {
      return (
        <div className="flex gap-2">
          <BaseButton size="sm" variant="outline">
            <Pencil className="h-4 w-4" />
          </BaseButton>
          <BaseButton size="sm" variant="outline">
            <Trash2 className="h-4 w-4" />
          </BaseButton>
        </div>
      );
    },
  },
];

const exampleData: I{FeatureName}[] = [
  {
    id: 1,
    position_name: "Manager",
    status: true,
    description: "Management position",
    last_updated: dayjs().format("DD MMM YYYY HH:mm"),
    updated_by: "AdminUser",
  },
  {
    id: 2,
    position_name: "Developer",
    status: true,
    description: "Development position",
    last_updated: dayjs().format("DD MMM YYYY HH:mm"),
    updated_by: "AdminUser",
  },
];

const {FeatureName}Table = () => {
  return (
    <div className="flex flex-col gap-4">
      <{FeatureName}TableHeader />
      <DataTable columns={columns} data={exampleData} />
    </div>
  );
};

export default {FeatureName}Table;
```

#### 6. Create Table Header Molecule

**File**: `src/components/molecules/admin/{feature-name}/table-header.tsx`

```typescript
import {FeatureName}Filter from "./filter";
import {FeatureName}Search from "./search";
import React from "react";

const {FeatureName}TableHeader = () => {
  return (
    <div className="flex justify-between gap-4">
      <{FeatureName}Search />
      <{FeatureName}Filter />
    </div>
  );
};

export default {FeatureName}TableHeader;
```

#### 7. Create Search and Filter Molecules

**File**: `src/components/molecules/admin/{feature-name}/search.tsx`

```typescript
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { Search } from "lucide-react";
import React from "react";

const {FeatureName}Search = () => {
  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <div>
        <BaseSelect>
          <BaseSelectTrigger className="w-32 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            <BaseSelectItem value="name">Name</BaseSelectItem>
            <BaseSelectItem value="status">Status</BaseSelectItem>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput className="border-none h-12 focus-visible:border-none focus-visible:ring-0" />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default {FeatureName}Search;
```

**File**: `src/components/molecules/admin/{feature-name}/filter.tsx`

```typescript
import { BaseButton } from "@/components/atoms/button";
import React from "react";

const {FeatureName}Filter = () => {
  return (
    <div className="flex gap-2">
      <BaseButton variant="outline" size="sm">
        All Status
      </BaseButton>
      <BaseButton variant="outline" size="sm">
        Active
      </BaseButton>
      <BaseButton variant="outline" size="sm">
        Inactive
      </BaseButton>
    </div>
  );
};

export default {FeatureName}Filter;
```

### Example: Creating Position Management

For a command like "create new CMS menu named position-management with table columns Position Name, Status, Description, Action button":

1. **Interface**: `src/interfaces/admin/position-management/list.ts`
2. **Page**: `src/app/admin/position-management/page.tsx`
3. **Organism**: `src/components/organisms/admin/position-management/index.tsx`
4. **Molecules**: 
   - `src/components/molecules/admin/position-management/title.tsx`
   - `src/components/molecules/admin/position-management/table.tsx`
   - `src/components/molecules/admin/position-management/table-header.tsx`
   - `src/components/molecules/admin/position-management/search.tsx`
   - `src/components/molecules/admin/position-management/filter.tsx`

### Naming Conventions for CMS Menus

- **URL**: `/admin/{feature-name}` (kebab-case)
- **Component Names**: `{FeatureName}` (PascalCase)
- **Interface Names**: `I{FeatureName}` (PascalCase with I prefix)
- **File Names**: `{feature-name}` (kebab-case)
- **Directory Structure**: `admin/{feature-name}/`

## Development Workflow

1. **Create Interface**: Define TypeScript interface in `interfaces/admin/{feature-name}/list.ts`
2. **Create Route**: Add `page.tsx` in `app/admin/{feature-name}/`
3. **Create Organism**: Build main component in `organisms/admin/{feature-name}/`
4. **Create Molecules**: Build all required molecules (title, table, search, filter, table-header)
5. **Define Columns**: Create table columns based on interface properties
6. **Add Sample Data**: Include example data for testing
7. **Style**: Use consistent Tailwind patterns from user-management

## Key Principles

- **Separation of Concerns**: Pages → Organisms → Molecules → Atoms
- **Reusability**: Build components that can be reused across features
- **Consistency**: Follow established patterns and naming conventions
- **Type Safety**: Use TypeScript for all components and data structures
- **Performance**: Use React best practices (forwardRef, proper exports)
- **Maintainability**: Clear file structure and component hierarchy

This structure ensures scalable, maintainable code that follows React and Next.js best practices while maintaining consistency across the application.