# General Rule - Page Development Guidelines

## Overview
Panduan umum untuk AI dalam membantu pengembangan halaman (page slicing) menggunakan komponen-komponen yang sudah tersedia dalam struktur folder yang telah dibuat.

### CRITICAL RULE: 100% IDENTICAL REPLICATION

Ketika user memberikan **referensi page**, AI WAJIB membuat page yang **100% IDENTIK** dengan page referensi tersebut. Ini bukan hanya mengikuti pola umum, tetapi **EXACT REPLICATION** dari:
- Struktur komponen yang sama persis
- Import statements yang identik
- Logic dan state management yang sama
- Styling dan layout yang identik
- Props dan interface yang sama
- Semua detail implementasi

**PENTING: ORGANISM DAN MOLECULES BARU:**
Ketika melakukan replication page, AI HARUS:
- ✅ **BUAT ORGANISM BARU** dengan nama sesuai page baru (contoh: DashboardComp.tsx)
- ✅ **BUAT MOLECULES BARU** jika ada molecules yang di-import di organism referensi
- ✅ **COPY 100% CONTENT** dari organism dan molecules referensi ke yang baru
- ✅ **GANTI NAMA COMPONENT** di organism dan molecules baru sesuai naming convention
- ✅ **UPDATE IMPORT STATEMENTS** di organism baru untuk menggunakan molecules baru
- ❌ **JANGAN GUNAKAN** organism atau molecules yang sama dengan referensi

## Component Architecture Reference

### 1. Atomic Design Structure
Proyek ini menggunakan atomic design pattern dengan struktur:
- **Atoms** (`src/components/atoms/`) - Komponen dasar terkecil
- **Molecules** (`src/components/molecules/`) - Kombinasi atoms
- **Organisms** (`src/components/organisms/`) - Kombinasi molecules dan atoms
- **UI** (`src/components/ui/`) - Komponen UI library (shadcn/ui)

### 2. Page Development Process

#### A. Analisis Komponen yang Tersedia
Sebelum membuat page baru, AI harus:
1. **Cek folder `src/components/atoms/`** - untuk komponen dasar seperti buttons, inputs, icons
2. **Cek folder `src/components/molecules/`** - untuk komponen gabungan seperti forms, cards, navigation
3. **Cek folder `src/components/organisms/`** - untuk komponen kompleks seperti headers, sidebars, sections
4. **Cek folder `src/components/ui/`** - untuk komponen UI library yang sudah tersedia

#### B. Referensi Page yang Sudah Ada
Untuk konsistensi, AI harus:
1. **Analisis struktur page di `src/app/`** - melihat pola yang sudah ada
2. **Trace komponen yang digunakan** - dari page → organisms → molecules → atoms
3. **Ikuti pola import dan penggunaan** yang sudah established

#### C. Interface dan Type Reference
Selalu cek folder `src/interfaces/` untuk:
1. **Type definitions** yang sudah ada
2. **Interface patterns** yang harus diikuti
3. **Data structures** yang sudah didefinisikan

### 3. Development Guidelines

#### A. Reuse Before Create
- **Prioritaskan penggunaan komponen yang sudah ada**
- Jika komponen serupa sudah ada, gunakan atau extend daripada membuat baru
- Cek index files untuk melihat komponen yang sudah di-export

#### B. Consistent Patterns
- **Ikuti pola penamaan** yang sudah ada
- **Gunakan struktur folder** yang konsisten
- **Maintain import/export patterns** yang sudah established

#### C. Component Composition
- **Bangun dari atoms ke molecules ke organisms**
- **Gunakan composition pattern** untuk fleksibilitas
- **Maintain separation of concerns**

### 4. Specific Instructions for AI

#### WAJIB: Analisis Struktur Page yang Sudah Ada
Sebelum membuat page baru, AI HARUS menganalisis pola yang sudah established:

**Pola Page Structure yang Harus Diikuti:**
```typescript
// Pattern dari src/app/*/page.tsx
import [ComponentName] from '@/components/organisms/[FolderName]/[ComponentName]';

export const metadata = {
  title: '[Page Title]',
  description: '[Page Description]',
};

export default function [PageName]() {
  return <[ComponentName] />;
}
```

**Pola Organism Component yang Harus Diikuti:**
```typescript
// Pattern dari src/components/organisms/*/[Name]Comp.tsx
'use client';

import React from 'react';
// Import atoms dari @/components/atoms/
// Import molecules dari @/components/molecules/
// Import UI components dari @/components/ui/
// Import hooks dari @/hooks/
// Import interfaces dari @/interfaces/

const [Name]Comp: React.FC = () => {
  // Component logic here
  return (
    // JSX using atoms, molecules, and UI components
  );
};

export default [Name]Comp;
```

#### Ketika Membuat Page Baru:
1. **WAJIB: Cek existing pages** di `src/app/` untuk melihat pola struktur
2. **WAJIB: Scan organisms** di `src/components/organisms/` untuk referensi
3. **WAJIB: Cek molecules dan atoms** yang sudah tersedia
4. **WAJIB: Follow exact naming pattern**: `[Name]Comp.tsx` untuk organisms
5. **WAJIB: Use established import patterns** seperti yang terlihat di LoginComp
6. **WAJIB: Check interfaces** di `src/interfaces/` untuk type definitions

#### Ketika Membantu Slicing:
1. **Analisis existing organisms** untuk melihat bagaimana mereka menggunakan atoms/molecules
2. **Gunakan BaseInput, BaseButton, BaseLabel** dari atoms seperti di LoginComp
3. **Gunakan molecules** yang sudah ada seperti Toast, Dialog components
4. **Follow established patterns** untuk form handling, validation, state management
5. **Reuse existing hooks** dari `src/hooks/`

### 5. Folder Structure Reference

```
src/
├── components/
│   ├── atoms/          # Basic building blocks
│   ├── molecules/      # Component combinations
│   ├── organisms/      # Complex components
│   └── ui/            # UI library components
├── app/               # Next.js pages and layouts
├── interfaces/        # TypeScript interfaces
├── hooks/            # Custom React hooks
├── services/         # API and external services
├── store/           # State management
├── utils/           # Utility functions
└── assets/          # Static assets
```

### 6. Best Practices

- **Always check existing components first**
- **Maintain atomic design principles**
- **Use TypeScript interfaces consistently**
- **Follow established naming conventions**
- **Prioritize component reusability**
- **Document new components properly**

### 7. Mandatory Checklist untuk AI

#### Sebelum Membuat Page Baru:
- [ ] **Cek `src/app/` folder** - Analisis minimal 2-3 page yang sudah ada
- [ ] **Cek `src/components/organisms/`** - Lihat struktur dan pola yang digunakan
- [ ] **Cek `src/components/molecules/`** - Identifikasi komponen yang bisa digunakan
- [ ] **Cek `src/components/atoms/`** - Pastikan menggunakan BaseInput, BaseButton, dll
- [ ] **Cek `src/interfaces/`** - Gunakan type definitions yang sudah ada
- [ ] **Cek `src/hooks/`** - Gunakan custom hooks yang tersedia

#### Saat Implementasi:
- [ ] **Follow exact naming**: `[Name]Comp.tsx` untuk organisms
- [ ] **Use 'use client'** directive jika diperlukan
- [ ] **Import pattern**: Atoms → Molecules → UI → Hooks → Interfaces
- [ ] **Metadata pattern**: Selalu include title dan description
- [ ] **Export pattern**: Default export dengan nama yang konsisten

### 8. Contoh Konkret Implementasi

**Jika diminta membuat page "Profile":**

1. **Cek existing**: Lihat `homepage/page.tsx`, `login/page.tsx`
2. **Buat structure**:
   ```
   src/app/profile/page.tsx
   src/components/organisms/Profile/ProfileComp.tsx
   ```
3. **Follow pattern**:
   ```typescript
   // src/app/profile/page.tsx
   import ProfileComp from '@/components/organisms/Profile/ProfileComp';
   
   export const metadata = {
     title: 'Profile',
     description: 'User profile page.',
   };
   
   export default function ProfilePage() {
     return <ProfileComp />;
   }
   ```

4. **Organism implementation**:
   ```typescript
   // src/components/organisms/Profile/ProfileComp.tsx
   'use client';
   
   import React from 'react';
   import { BaseInput } from '@/components/atoms/input';
   import { BaseButton } from '@/components/atoms/button';
   // ... other imports following LoginComp pattern
   
   const ProfileComp: React.FC = () => {
     // Implementation using existing atoms/molecules
     return (
       // JSX structure
     );
   };
   
   export default ProfileComp;
   ```

### 9. CRITICAL: Referensi Wajib

**AI HARUS selalu merujuk ke file-file ini:**
- `src/app/login/page.tsx` - untuk pola page structure
- `src/components/organisms/Login/LoginComp.tsx` - untuk pola organism complex
- `src/components/organisms/Homepage/HomepageComp.tsx` - untuk pola organism simple
- `src/components/atoms/` - untuk komponen dasar yang WAJIB digunakan
- `src/components/molecules/` - untuk komponen gabungan yang sudah tersedia

### 10. MANDATORY: 100% IDENTICAL REPLICATION PROCESS

**KETIKA USER MEMBERIKAN REFERENSI PAGE, AI WAJIB:**

#### **EXACT REPLICATION RULE:**
- ✅ **COPY 100% IDENTIK** - Bukan adaptasi, bukan modifikasi, tetapi EXACT COPY
- ✅ **SEMUA DETAIL** - Termasuk comments, spacing, variable names
- ✅ **LOGIC YANG SAMA** - State management, event handlers, validations
- ✅ **STYLING IDENTIK** - CSS classes, inline styles, responsive behavior
- ✅ **PROPS INTERFACE SAMA** - Type definitions, default values, optional props

**SEBELUM membuat page baru, AI WAJIB melakukan COMPLETE REPLICATION TRACING:**

#### **Step 1: Analisis Page Referensi**
```
1. view_files: src/app/[reference-page]/page.tsx (WAJIB)
2. Identifikasi organism component yang diimport
3. Catat exact import path dan naming convention
```

#### **Step 2: Trace Organism Component**
```
1. view_files: src/components/organisms/[FolderName]/[ComponentName].tsx (WAJIB)
2. Analisis semua import statements dalam organism
3. Catat semua molecules dan atoms yang digunakan
4. Perhatikan case sensitivity dan variasi penamaan folder
```

#### **Step 3: Trace Molecules Components**
```
1. list_dir: src/components/molecules/ (WAJIB)
2. Untuk setiap molecule yang diimport di organism:
   - view_files: src/components/molecules/[exact-folder-name]/[ComponentName].tsx
   - Catat atoms yang digunakan dalam molecule
   - Perhatikan penamaan folder yang mungkin berbeda (camelCase, kebab-case, PascalCase)
```

#### **Step 4: Trace Atoms Components**
```
1. list_dir: src/components/atoms/ (WAJIB)
2. Untuk setiap atom yang diimport:
   - view_files: src/components/atoms/[exact-folder-name]/[ComponentName].tsx
   - Catat props interface dan usage pattern
   - Perhatikan variasi penamaan: input vs Input, button vs Button, dll
```

#### **Step 5: Verifikasi Import Paths**
```
1. Pastikan semua import paths case-sensitive sesuai dengan struktur folder aktual
2. Cek apakah ada alias import (@/components vs src/components)
3. Verifikasi export patterns (default export vs named export)
```

**CRITICAL: Case Sensitivity dan Penamaan**
- Folder names bisa berbeda: `input` vs `Input`, `button` vs `Button`
- Component names bisa berbeda: `BaseInput` vs `InputBase` vs `Input`
- WAJIB cek actual folder structure dengan list_dir
- WAJIB cek actual component names dengan view_files

**COPY EXACT PATTERN dari referensi:**
- Import statements HARUS sama persis dengan pola di organism referensi
- Struktur folder HARUS mengikuti pola yang ada
- Naming convention HARUS konsisten dengan existing files
- Case sensitivity HARUS diperhatikan

**GUNAKAN EXISTING COMPONENTS berdasarkan tracing:**
- Gunakan atoms yang sudah di-trace dari organism referensi
- Gunakan molecules yang sudah di-trace dari organism referensi
- Jangan buat komponen baru jika sudah ada yang serupa dalam tracing

### 11. TEMPLATE WAJIB untuk Page Baru

**Struktur yang HARUS diikuti:**

```typescript
// src/app/[page-name]/page.tsx - TEMPLATE WAJIB
import [PageName]Comp from '@/components/organisms/[PageName]/[PageName]Comp';

export const metadata = {
  title: '[Page Title]',
  description: '[Page Description]',
};

export default function [PageName]Page() {
  return <[PageName]Comp />;
}
```

```typescript
// src/components/organisms/[PageName]/[PageName]Comp.tsx - TEMPLATE WAJIB
'use client';

import React from 'react';
// WAJIB: Import atoms yang sudah ada
import { BaseInput } from '@/components/atoms/input';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
// WAJIB: Import molecules yang sudah ada
import { Toast } from '@/components/molecules/toast';
// Import lainnya sesuai kebutuhan

const [PageName]Comp: React.FC = () => {
  return (
    <div>
      {/* Gunakan komponen atoms dan molecules yang sudah ada */}
    </div>
  );
};

export default [PageName]Comp;
```

### 12. CONTOH KONKRET: 100% IDENTICAL REPLICATION

**Scenario: User berkata "Buat page Dashboard yang sama dengan page Login"**

#### **EXACT REPLICATION WORKFLOW:**

#### **Step-by-Step EXACT REPLICATION:**

1. **BACA REFERENSI PAGE** dengan `view_files`:
   ```
   view_files: src/app/login/page.tsx (FULL FILE)
   ```

2. **BACA ORGANISM REFERENSI** dengan `view_files`:
   ```
   view_files: src/components/organisms/Login/LoginComp.tsx (FULL FILE)
   ```

3. **IDENTIFIKASI MOLECULES YANG DI-IMPORT** di LoginComp.tsx:
   ```typescript
   // Contoh molecules yang ditemukan:
   import { OtpMethodDialog } from '@/components/molecules/otp-method-dialog';
   import { OtpInput } from '@/components/molecules/otp-input';
   import { Toast } from '@/components/molecules/toast';
   ```

4. **BUAT MOLECULES BARU** untuk page baru:
   ```
   // COPY molecules referensi ke molecules baru:
   view_files: src/components/molecules/otp-method-dialog/OtpMethodDialog.tsx
   write_to_file: src/components/molecules/dashboard-otp-method-dialog/DashboardOtpMethodDialog.tsx
   
   view_files: src/components/molecules/otp-input/OtpInput.tsx
   write_to_file: src/components/molecules/dashboard-otp-input/DashboardOtpInput.tsx
   
   view_files: src/components/molecules/toast/Toast.tsx
   write_to_file: src/components/molecules/dashboard-toast/DashboardToast.tsx
   ```

5. **BUAT ORGANISM BARU** dengan molecules baru:
   ```typescript
   // COPY 100% IDENTIK dari LoginComp.tsx ke DashboardComp.tsx
   // TAPI UPDATE IMPORT STATEMENTS:
   
   // DARI:
   import { OtpMethodDialog } from '@/components/molecules/otp-method-dialog';
   import { OtpInput } from '@/components/molecules/otp-input';
   import { Toast } from '@/components/molecules/toast';
   
   // MENJADI:
   import { DashboardOtpMethodDialog } from '@/components/molecules/dashboard-otp-method-dialog';
   import { DashboardOtpInput } from '@/components/molecules/dashboard-otp-input';
   import { DashboardToast } from '@/components/molecules/dashboard-toast';
   ```

6. **GANTI NAMA COMPONENT** di organism:
   ```typescript
   // DARI:
   const LoginComp: React.FC = () => {
   export default LoginComp;
   
   // MENJADI:
   const DashboardComp: React.FC = () => {
   export default DashboardComp;
   ```

7. **UPDATE JSX** untuk menggunakan molecules baru:
   ```typescript
   // DARI:
   <OtpMethodDialog />
   <OtpInput />
   <Toast />
   
   // MENJADI:
   <DashboardOtpMethodDialog />
   <DashboardOtpInput />
   <DashboardToast />
   ```

#### **Hasil EXACT REPLICATION untuk Dashboard:**
```typescript
// src/app/dashboard/page.tsx - EXACT COPY dari login/page.tsx
import DashboardComp from '@/components/organisms/Dashboard/DashboardComp';

export const metadata = {
  title: 'Dashboard', // HANYA INI yang berubah
  description: 'User dashboard page.', // HANYA INI yang berubah
};

export default function DashboardPage() { // HANYA nama function yang berubah
  return <DashboardComp />; // HANYA nama component yang berubah
}

// src/components/organisms/Dashboard/DashboardComp.tsx
// EXACT COPY 100% dari LoginComp.tsx - TAPI GUNAKAN MOLECULES BARU
'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
// ATOMS TETAP SAMA - TIDAK PERLU DIBUAT BARU
import { BaseInput } from '@/components/atoms/input';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
// MOLECULES BARU - DIBUAT KHUSUS UNTUK DASHBOARD
import { DashboardToast } from '@/components/molecules/dashboard-toast';
import { DashboardOtpMethodDialog } from '@/components/molecules/dashboard-otp-method-dialog';
import { DashboardOtpInput } from '@/components/molecules/dashboard-otp-input';
// ... SEMUA IMPORT LAINNYA EXACT SAME

// EXACT SAME VALIDATION SCHEMAS
const dashboardSchema = yup.object().shape({ // Nama schema bisa disesuaikan
  // ... EXACT SAME VALIDATION CONTENT
});

const DashboardComp: React.FC = () => {
  // EXACT SAME STATE MANAGEMENT
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  // ... SEMUA STATE EXACT SAME
  
  // EXACT SAME FORM SETUP
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(dashboardSchema)
  });
  
  // EXACT SAME EVENT HANDLERS
  const onSubmit = (data: any) => {
    // ... EXACT SAME LOGIC
  };
  
  // EXACT SAME JSX STRUCTURE - GUNAKAN MOLECULES BARU
  return (
    <div className="exact-same-classes">
      {/* EXACT SAME JSX CONTENT - TAPI COMPONENT NAMES BARU */}
      <BaseInput {...register('username')} />
      <BaseButton onClick={handleSubmit(onSubmit)}>Submit</BaseButton>
      <DashboardToast message="Success" /> {/* BUKAN Toast */}
      <DashboardOtpMethodDialog isOpen={true} /> {/* BUKAN OtpMethodDialog */}
      <DashboardOtpInput value="" onChange={() => {}} /> {/* BUKAN OtpInput */}
      {/* ... SEMUA JSX EXACT SAME STRUCTURE */}
    </div>
  );
};

export default DashboardComp;
```

**PENTING:**
- ✅ **ATOMS TETAP SAMA** - Tidak perlu dibuat baru (BaseInput, BaseButton, dll)
- ✅ **MOLECULES DIBUAT BARU** - Setiap page punya molecules sendiri
- ✅ **LOGIC 100% IDENTIK** - State, handlers, validation exact same
- ✅ **STRUCTURE 100% IDENTIK** - JSX hierarchy exact same
- ✅ **HANYA NAMA COMPONENT** yang berubah di molecules dan organism

### 13. LARANGAN MUTLAK - EXACT REPLICATION

**KETIKA USER MEMBERIKAN REFERENSI PAGE, AI DILARANG KERAS:**
- ❌ **MODIFIKASI APAPUN** dari referensi page (kecuali nama component)
- ❌ **ADAPTASI atau INTERPRETASI** - harus EXACT COPY
- ❌ **MENAMBAH atau MENGURANGI** fitur dari referensi
- ❌ **MENGUBAH LOGIC** state management atau event handlers
- ❌ **MENGUBAH STYLING** atau CSS classes
- ❌ **MENGUBAH VALIDATION** schemas atau rules
- ❌ **MENGUBAH JSX STRUCTURE** atau component hierarchy
- ❌ **MENGHILANGKAN COMMENTS** atau formatting
- ❌ **MENGGUNAKAN MOLECULES YANG SAMA** dengan referensi
- ❌ **MENGGUNAKAN ORGANISM YANG SAMA** dengan referensi
- ❌ **SKIP PEMBUATAN MOLECULES BARU** untuk page baru

**WAJIB DILAKUKAN UNTUK EXACT REPLICATION:**
- ✅ **COPY 100% IDENTIK** semua content dari referensi
- ✅ **PRESERVE SEMUA DETAIL** termasuk spacing, comments, formatting
- ✅ **BUAT ORGANISM BARU** dengan nama sesuai page baru
- ✅ **BUAT MOLECULES BARU** untuk setiap molecules yang di-import di organism referensi
- ✅ **COPY SEMUA STATE MANAGEMENT** useState, useEffect, custom hooks
- ✅ **COPY SEMUA EVENT HANDLERS** dan business logic
- ✅ **COPY SEMUA VALIDATION** schemas dan error handling
- ✅ **COPY SEMUA STYLING** classes, inline styles, responsive behavior
- ✅ **COPY SEMUA JSX STRUCTURE** dengan exact same hierarchy
- ✅ **UPDATE IMPORT STATEMENTS** untuk menggunakan molecules baru
- ✅ **UPDATE JSX COMPONENTS** untuk menggunakan molecules baru
- ✅ **HANYA GANTI NAMA COMPONENT** dan metadata (title, description)
- ✅ **VERIFY DEPENDENCIES** tersedia sebelum implementasi

### 14. CRITICAL SUCCESS FACTORS - EXACT REPLICATION

**KETIKA USER MEMBERIKAN REFERENSI PAGE, AI HARUS:**
1. **EXACT COPY EVERYTHING** - Copy 100% identik semua content dari referensi
2. **PRESERVE ALL DETAILS** - Maintain spacing, comments, formatting, indentation
3. **CREATE NEW ORGANISM** - Buat organism baru dengan nama sesuai page baru
4. **CREATE NEW MOLECULES** - Buat molecules baru untuk setiap molecules di organism referensi
5. **IDENTICAL LOGIC** - Copy semua state management, hooks, event handlers
6. **IDENTICAL STYLING** - Copy semua CSS classes, inline styles, responsive behavior
7. **IDENTICAL VALIDATION** - Copy semua schemas, rules, error handling
8. **IDENTICAL JSX STRUCTURE** - Copy exact same component hierarchy dan structure
9. **UPDATE IMPORTS** - Update import statements untuk menggunakan molecules baru
10. **UPDATE JSX USAGE** - Update JSX untuk menggunakan molecules baru
11. **ONLY CHANGE NAMES** - Hanya ganti nama component dan metadata

**HASIL AKHIR HARUS:**
- ✅ **100% IDENTIK** dengan referensi page (kecuali nama)
- ✅ **ZERO MODIFICATIONS** dari logic atau styling original
- ✅ **EXACT SAME BEHAVIOR** seperti referensi page
- ✅ **EXACT SAME DEPENDENCIES** dan import statements
- ✅ **EXACT SAME USER EXPERIENCE** dan functionality
- ✅ **READY TO RUN** tanpa error atau missing dependencies

---

## 🚨 FINAL WARNING: EXACT REPLICATION MANDATORY

**JIKA USER MEMBERIKAN REFERENSI PAGE:**
- Page yang dibuat HARUS 100% identik dengan referensi
- ORGANISM dan MOLECULES HARUS dibuat baru (tidak boleh menggunakan yang sama)
- TIDAK ADA toleransi untuk modifikasi, adaptasi, atau interpretasi
- HANYA nama component dan metadata yang boleh berubah
- SEMUA logic, styling, validation HARUS exact same
- ATOMS boleh menggunakan yang sudah ada (tidak perlu dibuat baru)
- AI yang melanggar aturan ini dianggap GAGAL TOTAL

**REMEMBER:**
- **EXACT COPY LOGIC = SUCCESS**
- **NEW ORGANISM + NEW MOLECULES = MANDATORY**
- **ANY MODIFICATION = FAILURE**

Dengan mengikuti aturan tracing ini secara KETAT, AI akan menghasilkan page yang 100% konsisten dengan pola yang sudah established dalam proyek.