# Multi-stage Dockerfile for Next.js application
# Stage 1: Builder
FROM node:20-alpine AS builder
WORKDIR /app

# Install build dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    libc6-compat \
    vips-dev

# Copy package files and source code
COPY --chown=node:node ./ .

# Install dependencies including optional ones for sharp
RUN npm install --include=optional --no-interactive

# Install platform-specific binaries for Alpine Linux
RUN npm install --cpu=x64 --os=linux --libc=musl sharp
RUN npm install --cpu=x64 --os=linux --libc=musl lightningcss
RUN npm install --cpu=x64 --os=linux --libc=musl @tailwindcss/oxide

# Disable Next.js telemetry and build the application
ENV NEXT_TELEMETRY_DISABLED=1
RUN npm run build

# Stage 2: Runner
FROM node:20-alpine AS runner

# Create non-root user for security
RUN addgroup -S nextjs && adduser -S appuser -G nextjs
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    vips

# Set environment variables
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"
ENV SHARP_IGNORE_GLOBAL_LIBVIPS 1

# Copy built application with correct permissions
COPY --chown=appuser:nextjs --from=builder /app/.next ./.next
COPY --chown=appuser:nextjs --from=builder /app/public ./public
COPY --chown=appuser:nextjs --from=builder /app/package.json ./package.json
COPY --chown=appuser:nextjs --from=builder /app/package-lock.json ./package-lock.json
COPY --chown=appuser:nextjs --from=builder /app/node_modules ./node_modules

# Ensure proper ownership
RUN chown -R appuser:nextjs /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "run", "start"]