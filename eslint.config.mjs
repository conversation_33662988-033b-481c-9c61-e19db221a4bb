import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

// const eslintConfig = [
//   ...compat.extends("next/core-web-vitals", "next/typescript"),
// ];
const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Disable all rules
      ...Object.fromEntries(
        Object.keys(require('@eslint/js').configs.all.rules).map(rule => [rule, 'off'])
      )
    }
  }
];
// const eslintConfig = [
//   ...compat.config({
//     extends: ['next'],
//     rules: {
//       '@typescript-eslint/no-unused-vars': 'off',
//       '@typescript-eslint/no-explicit-any': 'off',
//       'react-hooks/exhaustive-deps': 'off',
//       '@next/next/no-img-element': 'off',
//       'react/no-unescaped-entities': 'off',
//       '@next/next/no-page-custom-font': 'off',
//       'prefer-const': 'off',
//       'no-unused-vars': 'off',
//     }
//   }),
// ]

export default eslintConfig;
