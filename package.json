{"name": "lemon_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@mfa-client/frontend": "^1.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-visually-hidden": "^1.2.3", "@ramonak/react-progress-bar": "^5.4.0", "@tanstack/react-query": "^5.87.1", "@tanstack/react-query-devtools": "^5.87.1", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^6.1.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.540.0", "next": "15.4.6", "otplib": "^12.0.1", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-gauge-component": "^1.2.64", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-otp-input": "^3.1.1", "react-paginate": "^8.3.0", "react-qr-code": "^2.0.18", "react-router-dom": "^7.8.1", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "use-debounce": "^10.0.6", "yup": "^1.7.0", "zod": "^4.0.17", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.86.0", "@types/crypto-js": "^4.2.0", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "husky": "^9.1.7", "tw-animate-css": "^1.3.7", "typescript": "^5"}}