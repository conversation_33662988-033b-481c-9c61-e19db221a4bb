import { BaseSidebarProvider } from "@/components/atoms/sidebar";
import LearnerFooter from "@/components/molecules/learner/layout/footer";
import LearnerNavbar from "@/components/molecules/learner/layout/navbar";
import LearnerBaseSidebar from "@/components/molecules/learner/layout/sidebar";
import React from "react";

const LearnerLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="min-h-dvh flex flex-col overflow-x-hidden">
      {/* Navbar */}
      <LearnerNavbar />

      <BaseSidebarProvider className="md:p-6 md:pt-0">
        <div className="flex flex-row gap-5 flex-1 bg-[white] w-full">
          {/* Sidebar */}
          <LearnerBaseSidebar />

          {/* Main + Footer */}
          <div className="flex flex-col flex-1 overflow-hidden">
            <main className="flex-1 overflow-y-auto p-4 md:p-0 w-full">
              {children}
            </main>
            <LearnerFooter />
          </div>
        </div>
      </BaseSidebarProvider>
    </div>
  );
};

export default LearnerLayout;
