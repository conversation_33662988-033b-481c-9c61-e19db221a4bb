import { BaseSidebarProvider } from "@/components/atoms/sidebar";
import AdminNavbar from "@/components/molecules/admin/layout/navbar";
import AdminSidebar from "@/components/molecules/admin/layout/sidebar";
import React from "react";

const AdminLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="max-h-dvh overflow-hidden">
      <AdminNavbar />
      <BaseSidebarProvider>
        <AdminSidebar />
        <main className="flex-1 overflow-y-auto px-4 py-6 bg-[#F5F5F5] w-10px max-h-[calc(100vh-64px)]">
          {children}
        </main>
      </BaseSidebarProvider>
    </div>
  );
};

export default AdminLayout;
