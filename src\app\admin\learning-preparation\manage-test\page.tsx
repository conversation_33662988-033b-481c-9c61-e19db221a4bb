/**
 * Admin Dashboard Page
 *
 * This is a Next.js 13+ App Router page component.
 *
 * Key Concepts:
 * - This file represents the /admin route
 * - Only composition happens here - no business logic
 * - Import organisms and compose them into a complete page
 * - Use server components by default, add 'use client' only when needed
 *
 * Atomic Design Pattern:
 * - Import organisms from components/organisms
 * - Organisms contain molecules and atoms
 * - Keep this page focused on layout and composition
 */

import { Metadata } from 'next';
// import { Navbar } from '@/components/organisms'
// import { Sidebar } from '@/components/organisms'

// Metadata for SEO and page info
export const metadata: Metadata = {
  title: 'Manage Test | Lemon App',
  description: 'Administrative dashboard for managing the Lemon application',
};

/**
 * Admin Page Component
 *
 * This is a Server Component by default in App Router.
 * It handles the admin dashboard layout and composition.
 */
export default function ManageTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 
        TODO: Uncomment when organisms are created
        <Navbar />
        
        <div className="flex">
          <Sidebar />
          <main className="flex-1 p-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">
              Admin Dashboard
            </h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              // Dashboard widgets will go here
              // Import and compose organisms like:
              // <StatsOverview />
              // <RecentActivity />
              // <UserManagement />
            </div>
          </main>
        </div>
      */}

      {/* Temporary content until organisms are created */}
      <div className="p-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Manage Test</h1>
        <p className="text-gray-600">
          This is the admin page. Organisms will be composed here once created.
        </p>
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            🏗️ Template Structure Guide
          </h2>
          <ul className="text-blue-800 space-y-1">
            <li>
              • This page composes organisms into a complete admin interface
            </li>
            <li>• Import Navbar, Sidebar, and other organisms when ready</li>
            <li>• Keep business logic in services layer</li>
            <li>• Use server components for better performance</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * Development Notes:
 *
 * 1. Server vs Client Components:
 *    - This is a server component (no 'use client')
 *    - Add 'use client' only if you need:
 *      - Event handlers (onClick, onChange)
 *      - State (useState, useReducer)
 *      - Effects (useEffect)
 *      - Browser APIs
 *
 * 2. Data Fetching:
 *    - Use async/await directly in server components
 *    - For client components, use TanStack Query (in services/mutation)
 *
 * 3. Styling:
 *    - Using Tailwind CSS classes
 *    - Consider creating design tokens in assets/styles
 *
 * 4. Composition Pattern:
 *    - Import organisms from components/organisms
 *    - Each organism handles its own data fetching and state
 *    - This page only handles layout and composition
 */
