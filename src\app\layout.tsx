/**
 * Root Layout Component
 *
 * This is the root layout for the entire Next.js 13+ App Router application.
 * It wraps all pages and provides global structure, fonts, and providers.
 *
 * Key Concepts:
 * - Wraps all pages in the application
 * - Defines global HTML structure
 * - Sets up fonts, providers, and global styles
 * - Only re-renders when necessary (layout persistence)
 *
 * Atomic Design Pattern:
 * - This is the composition root
 * - Global organisms like Navbar can be added here
 * - Providers for state management go here
 * - Global error boundaries and analytics
 */

import type { Metadata } from 'next';
import {
  Inter,
  JetBrains_Mono,
  IBM_Plex_Sans,
  DM_Sans,
  Pixelify_Sans,
} from 'next/font/google';
import './globals.css';
import HotToastProvider from '@/components/molecules/toast/hot-toast-provider';
import QueryProvider from './provider';

// Import global providers when ready
// import { Providers } from '@/components/providers'
// import { Toaster } from '@/components/atoms'
// import { ErrorBoundary } from '@/components/molecules'

// Font configuration
const inter = Inter({
  variable: '--font-inter-sans',
  subsets: ['latin'],
  display: 'swap', // Optimize font loading
});

const jetbrainsMono = JetBrains_Mono({
  variable: '--font-jetbrains-mono',
  subsets: ['latin'],
  display: 'swap', // Optimize font loading
});

const ibmPlexSans = IBM_Plex_Sans({
  variable: '--font-ibm-plex',
  subsets: ['latin'],
  display: 'swap', // Optimize font loading
});

const dmSans = DM_Sans({
  variable: '--font-dm-sans',
  subsets: ['latin'],
  display: 'swap', // Optimize font loading
});

const pixelify = Pixelify_Sans({
  variable: '--font-pixelify-sans',
  subsets: ['latin'],
  weight: ['400', '500', '700'],
});

// Global metadata for the application
export const metadata: Metadata = {
  title: {
    default: 'Lemon App',
    template: '%s | Lemon App', // Page title template
  },
  description: 'A modern Next.js application with atomic design structure',
  keywords: ['nextjs', 'react', 'typescript', 'atomic design'],
  authors: [{ name: 'Lemon App Team' }],
  creator: 'Lemon App',
  publisher: 'Lemon App',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://lemon-app.com',
    siteName: 'Lemon App',
    title: 'Lemon App',
    description: 'A modern Next.js application with atomic design structure',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Lemon App',
    description: 'A modern Next.js application with atomic design structure',
    creator: '@lemonapp',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
};

/**
 * Root Layout Component
 *
 * This layout wraps all pages and provides:
 * - Global HTML structure
 * - Font loading and CSS variables
 * - Global providers (state, theme, etc.)
 * - Global components (toasts, modals)
 * - Error boundaries
 */

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
    >
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
      <body
        className={`${ibmPlexSans.variable} ${dmSans.variable} ${pixelify.variable} ${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <HotToastProvider />
        {/* 
          TODO: Add global providers when store is set up
          <Providers>
          <ErrorBoundary>
          {children}
          <Toaster />
          </ErrorBoundary>
          </Providers>
          */}
        {/* Temporary structure until providers are created */}
        <QueryProvider>
          <div id="root">{children}</div>
        </QueryProvider>

        {/* Global portal containers */}
        <div id="modal-root" />
        <div id="tooltip-root" />
        <div id="toast-root" />

        {/* Development indicator */}
        {process.env.NODE_ENV === 'development' && (
          <div className="fixed bottom-4 right-4 bg-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-xs font-medium z-50">
            🚧 DEV MODE
          </div>
        )}
      </body>
    </html>
  );
}

/**
 * Development Notes:
 *
 * 1. Layout Persistence:
 *    - This layout persists across route changes
 *    - State in this layout is preserved during navigation
 *    - Only re-renders when props change
 *
 * 2. Global Providers:
 *    - Add state management providers (Redux/Zustand)
 *    - Theme providers for dark/light mode
 *    - Authentication context
 *    - TanStack Query client
 *
 * 3. Global Components:
 *    - Toast notifications (utils/message)
 *    - Modal containers
 *    - Loading indicators
 *    - Error boundaries
 *
 * 4. Performance:
 *    - Font optimization with display: 'swap'
 *    - Suppress hydration warnings for client-only content
 *    - Portal containers for modals and tooltips
 *
 * 5. SEO & Metadata:
 *    - Comprehensive metadata configuration
 *    - Open Graph and Twitter cards
 *    - Structured data (add JSON-LD when needed)
 *
 * Example Provider Setup:
 * ```tsx
 * import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
 * import { ThemeProvider } from 'next-themes'
 * import { AuthProvider } from '@/store/auth'
 *
 * export function Providers({ children }: { children: React.ReactNode }) {
 *   return (
 *     <QueryClientProvider client={queryClient}>
 *       <ThemeProvider attribute="class" defaultTheme="system">
 *         <AuthProvider>
 *           {children}
 *         </AuthProvider>
 *       </ThemeProvider>
 *     </QueryClientProvider>
 *   )
 * }
 * ```
 */
