/**
 * Global Loading Component
 * 
 * This is a special Next.js 13+ App Router file that shows loading UI
 * while page components are being rendered or data is being fetched.
 * 
 * Key Concepts:
 * - Automatically shown during navigation and data fetching
 * - Can be overridden by specific route segments
 * - Should use atoms/molecules for consistent loading patterns
 * - Keep it lightweight and accessible
 * 
 * Atomic Design Pattern:
 * - Use atoms like Spinner, LoadingDots
 * - Compose with molecules like LoadingCard
 * - Maintain consistent loading experience
 */

// import { Spinner } from '@/components/atoms'
// import { LoadingCard } from '@/components/molecules'

/**
 * Global Loading Component
 * 
 * This component is automatically displayed by Next.js during:
 * - Route transitions
 * - Server component rendering
 * - Data fetching in server components
 */
export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center space-y-4">
        {/* 
          TODO: Replace with Spinner atom when created
          <Spinner size="lg" color="primary" />
        */}
        
        {/* Temporary loading spinner */}
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        
        {/* Loading text */}
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-gray-700">
            Loading...
          </h2>
          <p className="text-gray-500">
            Please wait while we prepare your content
          </p>
        </div>
        
        {/* Loading dots animation */}
        <div className="flex justify-center space-x-1">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  )
}

/**
 * Development Notes:
 * 
 * 1. Next.js App Router Loading:
 *    - This file is automatically used during navigation
 *    - Shows while server components are rendering
 *    - Can be nested (each route segment can have its own loading.tsx)
 * 
 * 2. Loading States:
 *    - Global loading (this file)
 *    - Route-specific loading (in route folders)
 *    - Component-level loading (in organisms/molecules)
 *    - Data loading (TanStack Query loading states)
 * 
 * 3. Accessibility:
 *    - Include proper ARIA labels
 *    - Announce loading state to screen readers
 *    - Provide meaningful loading messages
 * 
 * 4. Performance:
 *    - Keep loading components lightweight
 *    - Avoid heavy animations that block rendering
 *    - Consider skeleton screens for better UX
 * 
 * 5. Customization:
 *    - Create route-specific loading.tsx files
 *    - Use different loading patterns for different content types
 *    - Match loading UI to the expected content layout
 * 
 * Example Usage:
 * 
 * Route-specific loading:
 * src/app/admin/loading.tsx - Loading for admin routes
 * src/app/login/loading.tsx - Loading for login page
 * 
 * Component loading:
 * <Suspense fallback={<LoadingCard />}>
 *   <AsyncComponent />
 * </Suspense>
 */