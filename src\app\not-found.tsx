/**
 * Global Not Found (404) Component
 * 
 * This is a special Next.js 13+ App Router file that handles 404 errors
 * when a route is not found or when notFound() is called.
 * 
 * Key Concepts:
 * - Automatically shown for non-existent routes
 * - Can be triggered programmatically with notFound()
 * - Should provide helpful navigation options
 * - Use atoms/molecules for consistent error UI
 * 
 * Atomic Design Pattern:
 * - Use atoms like <PERSON>ton for navigation
 * - Compose with molecules like ErrorCard
 * - Maintain consistent error experience
 */

import Link from 'next/link'
// import { Button } from '@/components/atoms'
// import { ErrorCard } from '@/components/molecules'

/**
 * Global Not Found Component
 * 
 * This component is automatically displayed when:
 * - A route doesn't exist
 * - notFound() function is called in a server component
 * - Dynamic routes don't match any valid parameters
 */
export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="text-center space-y-6 max-w-md">
        {/* 404 Visual */}
        <div className="space-y-4">
          <div className="text-8xl font-bold text-gray-300">
            404
          </div>
          <div className="text-6xl">🍋</div>
        </div>
        
        {/* Error Message */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">
            Page Not Found
          </h1>
          <p className="text-gray-600">
            Sorry, we couldn't find the page you're looking for.
            The page might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>
        
        {/* Navigation Options */}
        <div className="space-y-4">
          {/* 
            TODO: Replace with Button atoms when created
            <Button variant="primary" size="lg" asChild>
              <Link href="/">Go Home</Link>
            </Button>
            
            <Button variant="secondary" size="lg" asChild>
              <Link href="/admin">Go to Dashboard</Link>
            </Button>
          */}
          
          {/* Temporary buttons until atoms are created */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              ← Go Back
            </Link>
            
            {/* <Link
              href="/admin"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              📊 Dashboard
            </Link> */}
          </div>
          
          {/* Back Button */}
          {/* <button
            onClick={() => window.history.back()}
            className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
          >
            ← Go Back
          </button> */}
        </div>
        
        {/* Help Section */}
        <div className="pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team or check our documentation.
          </p>
          <div className="mt-2 space-x-4">
            <Link 
              href="/help" 
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Help Center
            </Link>
            <Link 
              href="/contact" 
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Contact Support
            </Link>
          </div>
        </div>
        
        {/* Template Guide */}
        <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg text-left">
          <h3 className="text-lg font-semibold text-red-900 mb-2">
            🏗️ Template Structure Guide
          </h3>
          <ul className="text-red-800 space-y-1 text-sm">
            <li>• This 404 page will use Button atoms for navigation</li>
            <li>• Consider creating ErrorCard molecule for reusable error UI</li>
            <li>• Add proper error tracking in services/satellite</li>
            <li>• Use utils/message for error notifications</li>
            <li>• Implement proper SEO metadata for error pages</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

/**
 * Development Notes:
 * 
 * 1. Next.js App Router 404 Handling:
 *    - This file handles all 404 errors globally
 *    - Can be overridden by route-specific not-found.tsx files
 *    - Triggered automatically for non-existent routes
 *    - Can be triggered programmatically with notFound()
 * 
 * 2. Error Tracking:
 *    - Consider adding error tracking (services/satellite)
 *    - Log 404 errors for analytics
 *    - Track user behavior on error pages
 * 
 * 3. SEO Considerations:
 *    - Return proper 404 HTTP status
 *    - Include relevant meta tags
 *    - Provide sitemap links
 *    - Help search engines understand the error
 * 
 * 4. User Experience:
 *    - Provide clear error message
 *    - Offer helpful navigation options
 *    - Include search functionality
 *    - Suggest related content
 * 
 * 5. Accessibility:
 *    - Proper heading structure
 *    - Focus management
 *    - Screen reader friendly
 *    - Keyboard navigation
 * 
 * Example Usage:
 * 
 * Programmatic 404:
 * ```tsx
 * import { notFound } from 'next/navigation'
 * 
 * export default async function UserPage({ params }: { params: { id: string } }) {
 *   const user = await getUser(params.id)
 *   
 *   if (!user) {
 *     notFound() // Triggers this not-found.tsx
 *   }
 *   
 *   return <UserProfile user={user} />
 * }
 * ```
 * 
 * Route-specific 404:
 * src/app/admin/not-found.tsx - 404 for admin routes
 * src/app/blog/not-found.tsx - 404 for blog routes
 */