/**
 * Home Page Component
 *
 * This page redirects users to the login page.
 * All traffic to the root URL (/) will be redirected to /login.
 */

import { redirect } from 'next/navigation';

/**
 * Home Page Component
 *
 * Redirects to login page when accessing root URL
 */
export default function Home() {
  redirect('/login');
}

/**
 * Development Notes:
 *
 * 1. Server Component:
 *    - This is a server component (no 'use client')
 *    - Renders on the server for better SEO and performance
 *    - Can fetch data directly with async/await
 *
 * 2. Composition Pattern:
 *    - Replace sections with organisms when created
 *    - Hero → components/organisms/Hero
 *    - FeatureGrid → components/organisms/FeatureGrid
 *    - Footer → components/organisms/Footer
 *
 * 3. Data Fetching:
 *    - Add async data fetching for dynamic content
 *    - Use services/api for data calls
 *    - Consider ISR (Incremental Static Regeneration)
 *
 * 4. SEO Optimization:
 *    - Add structured data (JSON-LD)
 *    - Optimize images with next/image
 *    - Add proper meta tags in layout
 *
 * 5. Performance:
 *    - Use next/image for optimized images
 *    - Implement lazy loading for below-fold content
 *    - Consider code splitting for large components
 *
 * Example Organism Usage:
 * ```tsx
 * import { Hero, FeatureGrid, Footer } from '@/components/organisms'
 *
 * export default function Home() {
 *   return (
 *     <>
 *       <Hero
 *         title="Welcome to Lemon App"
 *         subtitle="Modern Next.js with Atomic Design"
 *         ctaButtons={[
 *           { text: "Get Started", href: "/admin", variant: "primary" },
 *           { text: "Sign In", href: "/login", variant: "secondary" }
 *         ]}
 *       />
 *       <FeatureGrid features={features} />
 *       <Footer />
 *     </>
 *   )
 * }
 * ```
 */
