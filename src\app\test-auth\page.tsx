import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { api } from "@/services/satellite";
import Test<PERSON>uthClient from "./test-auth-client";

interface LoginResponse {
  access_token?: string;
  refresh_token?: string;
  user?: any;
}

// Server Actions
async function loginAction(formData: FormData) {
  "use server";

  const username = formData.get("username") as string;
  const password = formData.get("password") as string;

  try {
    const response = await api.post("/int/test-auth/login", {
      username,
      password,
    });

    // Handle cookies from API response
    const setCookieHeader = response.headers["set-cookie"];
    const cookieStore = await cookies();
    console.log("response", response.data.accessToken);
    cookieStore.set("access_token", response.data.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: 7 * 24 * 60 * 60,
    });

    if (setCookieHeader) {
      console.log("Setting cookies:", setCookieHeader);
      setCookieHeader.forEach((cookie: string) => {
        const [nameValue, ...attributes] = cookie.split(";");
        const [name, value] = nameValue.split("=");

        const options: any = {};
        attributes.forEach((attr) => {
          const [key, val] = attr.trim().split("=");
          if (key.toLowerCase() === "httponly") options.httpOnly = true;
          if (key.toLowerCase() === "secure") options.secure = false;
          if (key.toLowerCase() === "samesite") options.sameSite = "lax";
          if (key.toLowerCase() === "path") options.path = val;
          if (key.toLowerCase() === "max-age") options.maxAge = parseInt(val);
        });

        // Set cookie using Next.js cookies API
        cookieStore.set(name.trim(), value, options);
      });
    }

    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
}

async function logoutAction() {
  "use server";

  try {
    const cookieStore = await cookies();
    const accessTokenCookie = cookieStore.get("access_token");
    const refreshTokenCookie = cookieStore.get("refresh_token");
    let accessToken = accessTokenCookie?.value;
    const refreshToken = refreshTokenCookie?.value;

    // Send refresh token as cookie in headers

    // TODO: For future development - add fallback to localStorage and Redux
    // if (!accessToken && typeof window !== 'undefined') {
    //   // Try localStorage
    //   accessToken = localStorage.getItem('access_token');
    //
    //   // Try Redux store
    //   // const state = store.getState();
    //   // accessToken = state.auth?.accessToken;
    // }

    const headers: Record<string, string> = {
      Cookie: `refresh_token=${refreshToken || ""}`,
    };
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }
    console.log(headers);

    const response = await api.get("/int/test-auth/logout", {
      headers,
    });

    // Clear cookies
    cookieStore.getAll().forEach((cookie) => {
      cookieStore.delete(cookie.name);
    });

    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
}

async function refreshTokenAction() {
  "use server";

  try {
    // Get refresh token from cookies
    const cookieStore = await cookies();
    const refreshTokenCookie = cookieStore.get("refresh_token");
    const refreshToken = refreshTokenCookie?.value;

    // Send refresh token as cookie in headers
    const response = await api.post(
      "/int/test-auth/refresh",
      {},
      {
        headers: {
          Cookie: `refresh_token=${refreshToken || ""}`,
        },
      }
    );

    // Handle new cookies
    const setCookieHeader = response.headers["set-cookie"];

    if (setCookieHeader) {
      setCookieHeader.forEach((cookie: string) => {
        const [nameValue, ...attributes] = cookie.split(";");
        const [name, value] = nameValue.split("=");

        const options: any = {};
        attributes.forEach((attr) => {
          const [key, val] = attr.trim().split("=");
          if (key.toLowerCase() === "httponly") options.httpOnly = true;
          if (key.toLowerCase() === "secure") options.secure = false;
          if (key.toLowerCase() === "samesite") options.sameSite = "lax";
          if (key.toLowerCase() === "path") options.path = val;
          if (key.toLowerCase() === "max-age") options.maxAge = parseInt(val);
        });

        cookieStore.set(name.trim(), value, options);
      });
    }

    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
}

async function testProtectedAction() {
  "use server";

  try {
    // Get access token from cookies
    const cookieStore = await cookies();
    const accessTokenCookie = cookieStore.get("access_token");
    let accessToken = accessTokenCookie?.value;

    // TODO: For future development - add fallback to localStorage and Redux
    // if (!accessToken && typeof window !== 'undefined') {
    //   // Try localStorage
    //   accessToken = localStorage.getItem('access_token');
    //
    //   // Try Redux store
    //   // const state = store.getState();
    //   // accessToken = state.auth?.accessToken;
    // }

    const headers: Record<string, string> = {};
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await api.get("/int/test-auth/me", {
      headers,
    });

    return {
      success: true,
      data: response.data,
      status: response.status,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data,
    };
  }
}

export default async function TestAuthPage() {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();
  const cookieString = allCookies
    .map((cookie) => `${cookie.name}=${cookie.value}`)
    .join("; ");

  return (
    <TestAuthClient
      loginAction={loginAction}
      logoutAction={logoutAction}
      refreshTokenAction={refreshTokenAction}
      testProtectedAction={testProtectedAction}
      initialCookies={cookieString}
    />
  );
}
