'use client';

import React, { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { encryptAES } from '@/utils/common/encryption';

interface TestAuthClientProps {
  loginAction: (formData: FormData) => Promise<any>;
  logoutAction: () => Promise<any>;
  refreshTokenAction: () => Promise<any>;
  testProtectedAction: () => Promise<any>;
  initialCookies: string;
}

export default function TestAuthClient({
  loginAction,
  logoutAction,
  refreshTokenAction,
  testProtectedAction,
  initialCookies
}: TestAuthClientProps) {
  const [username, setUsername] = useState('111111');
  const [password, setPassword] = useState('P@ssw0rd123');
  const [response, setResponse] = useState<string>('');
  const [clientCookies, setClientCookies] = useState<string>('');
  const [serverCookies, setServerCookies] = useState<string>(initialCookies);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const updateCookieDisplay = () => {
    setClientCookies(document.cookie || 'No client-side cookies found');
  };

  // Update server cookies when initialCookies prop changes
  React.useEffect(() => {
    setServerCookies(initialCookies);
  }, [initialCookies]);

  const handleLogin = async () => {
    startTransition(async () => {
      const formData = new FormData();
      const encryptedPassword = encryptAES(password);
      formData.append('username', username);
      formData.append('password', encryptedPassword);
      
      const result = await loginAction(formData);
      
      setResponse(JSON.stringify({
        success: result.success,
        status: result.status,
        data: result.data,
        error: result.error
      }, null, 2));
      
      updateCookieDisplay();
      router.refresh(); // Refresh to get updated server-side cookies
    });
  };

  const handleLogout = async () => {
    startTransition(async () => {
      const result = await logoutAction();
      
      setResponse(JSON.stringify({
        success: result.success,
        status: result.status,
        data: result.data,
        error: result.error,
        message: result.success ? 'Logout successful' : 'Logout failed'
      }, null, 2));
      
      updateCookieDisplay();
      router.refresh();
    });
  };

  const handleRefreshToken = async () => {
    startTransition(async () => {
      const result = await refreshTokenAction();
      
      setResponse(JSON.stringify({
        success: result.success,
        status: result.status,
        data: result.data,
        error: result.error
      }, null, 2));
      
      updateCookieDisplay();
      router.refresh();
    });
  };

  const handleTestProtected = async () => {
    startTransition(async () => {
      const result = await testProtectedAction();
      
      setResponse(JSON.stringify({
        success: result.success,
        status: result.status,
        data: result.data,
        error: result.error,
        message: result.success ? 'Protected route accessed successfully' : 'Failed to access protected route'
      }, null, 2));
    });
  };

  const clearCookies = () => {
    // Clear all cookies for current domain
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
    });
    updateCookieDisplay();
    router.refresh(); // Refresh to update server-side cookies
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Authentication Test Page (Server-Side)</h1>
        
        {/* Login Form */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Login Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Username (Email or NPK)</label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL> or NPK123"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="password123"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={handleLogin}
              disabled={isPending}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
            >
              {isPending ? 'Loading...' : 'Login'}
            </button>
            <button
              onClick={handleLogout}
              disabled={isPending}
              className="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
            >
              Logout
            </button>
            <button
              onClick={handleRefreshToken}
              disabled={isPending}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
            >
              Refresh Token
            </button>
            <button
              onClick={handleTestProtected}
              disabled={isPending}
              className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
            >
              Test Protected
            </button>
          </div>
        </div>

        {/* Cookie Management */}
        {/* <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Cookie Management</h2>
          <div className="flex gap-4 mb-4">
            <button
              onClick={updateCookieDisplay}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              Refresh Cookies
            </button>
            <button
              onClick={clearCookies}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              Clear All Cookies
            </button>
          </div>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
              <h3 className="font-medium mb-2 text-blue-800">Server-Side Cookies (HttpOnly + Accessible):</h3>
              <pre className="text-sm text-blue-700 whitespace-pre-wrap">{serverCookies || 'No server-side cookies found'}</pre>
            </div>
            <div className="bg-green-50 p-4 rounded-md border border-green-200">
              <h3 className="font-medium mb-2 text-green-800">Client-Side Accessible Cookies:</h3>
              <pre className="text-sm text-green-700 whitespace-pre-wrap">{clientCookies || 'Click "Refresh Cookies" to view client-side cookies'}</pre>
            </div>
          </div>
        </div> */}

        {/* Response Display */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">API Response</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-md overflow-auto max-h-96">
            <pre className="text-sm whitespace-pre-wrap">{response || 'No response yet. Click a test button above.'}</pre>
          </div>
        </div>

        {/* Debug Info */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium mb-2">Mode:</h3>
              <code className="bg-gray-100 px-2 py-1 rounded">Server-Side Rendering</code>
            </div>
            <div>
              <h3 className="font-medium mb-2">CORS:</h3>
              <code className="bg-gray-100 px-2 py-1 rounded">Not applicable (server-side)</code>
            </div>
            <div>
              <h3 className="font-medium mb-2">Current Domain:</h3>
              <code className="bg-gray-100 px-2 py-1 rounded">{typeof window !== 'undefined' ? window.location.origin : 'N/A'}</code>
            </div>
            {/* <div>
              <h3 className="font-medium mb-2">Server Cookies:</h3>
              <code className="bg-gray-100 px-2 py-1 rounded text-xs">{serverCookies || 'None'}</code>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
}