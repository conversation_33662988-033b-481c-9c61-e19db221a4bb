import React from 'react';

type IconArrowProps = {
  color?: string;
  size?: number;
  direction?: 'left' | 'right' | 'top' | 'bottom';
};

export const IconArrow: React.FC<IconArrowProps> = ({
  color = '#F7941E',
  size = 20,
  direction = 'right',
}) => {
  // Mapping arah ke transform
  const rotationMap: Record<
    NonNullable<IconArrowProps['direction']>,
    string
  > = {
    right: 'rotate(0)',
    left: 'rotate(180)',
    top: 'rotate(-90)',
    bottom: 'rotate(90)',
  };

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: rotationMap[direction] }}
    >
      <path
        d="M16.9716 9.99999C16.9716 10.414 16.6354 10.7503 16.2213 10.7503H5.62163L9.7685 14.7065C10.0693 14.9927 10.08 15.4694 9.79389 15.7672C9.50777 16.067 9.03327 16.0777 8.73327 15.7926L3.23327 10.5426C3.08397 10.4031 3 10.2062 3 9.99999C3 9.79374 3.08397 9.59999 3.23241 9.45624L8.73241 4.20624C9.031 3.92218 9.506 3.9328 9.7935 4.2328C10.0796 4.52968 10.0689 5.00624 9.76811 5.29343L5.62124 9.24967H16.2494C16.6622 9.24999 16.9716 9.58749 16.9716 9.99999Z"
        fill={color}
      />
    </svg>
  );
};
