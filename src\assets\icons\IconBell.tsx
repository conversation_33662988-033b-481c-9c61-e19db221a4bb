import React from 'react';

type IconBellProps = {
  color?: string;
  size?: number;
};

export const IconBell: React.FC<IconBellProps> = ({
  color = '#F7941E',
  size = 28,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8646_5515)">
        <path
          d="M15.3992 4.1998V4.98205C18.5711 5.48562 20.9992 8.23355 20.9992 11.5498V13.0111C20.9992 14.9973 21.6774 16.9267 22.9155 18.4798L23.5674 19.2936C23.8211 19.6086 23.8692 20.0417 23.6942 20.4048C23.5192 20.7679 23.1517 20.9998 22.7492 20.9998H5.24923C4.84541 20.9998 4.4777 20.7679 4.30287 20.4048C4.128 20.0417 4.17718 19.6086 4.42935 19.2936L5.08166 18.4798C6.32285 16.9267 6.99923 14.9973 6.99923 13.0111V11.5498C6.99923 8.23355 9.42735 5.48562 12.5992 4.98205V4.1998C12.5992 3.42674 13.2249 2.7998 13.9992 2.7998C14.7736 2.7998 15.3992 3.42674 15.3992 4.1998ZM13.6492 6.9998C11.138 6.9998 9.09923 9.03856 9.09923 11.5498V13.0111C9.09923 15.1067 8.49198 17.1498 7.36279 18.8998H20.6361C19.5074 17.1498 18.8992 15.1067 18.8992 13.0111V11.5498C18.8992 9.03856 16.8605 6.9998 14.3492 6.9998H13.6492ZM16.7992 22.3998C16.7992 23.1042 16.5061 23.8567 15.9811 24.3817C15.4561 24.9067 14.7036 25.1998 13.9992 25.1998C13.2555 25.1998 12.5424 24.9067 12.0174 24.3817C11.4924 23.8567 11.1992 23.1042 11.1992 22.3998H16.7992Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8646_5515">
          <rect
            width="19.6"
            height="22.4"
            fill="white"
            transform="translate(4.19922 2.7998)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
