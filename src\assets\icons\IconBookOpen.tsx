import React from 'react';

type IconBookOpenProps = {
  color?: string;
  size?: number;
};

export const IconBookOpen: React.FC<IconBookOpenProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8697_2515)">
        <path
          d="M9.6 4.80002V14.2L4.4 13.2V4.86752C4.4 4.37252 4.845 3.99502 5.3325 4.07752L9.6 4.80002ZM4.2425 14.1275L10 15.28L15.7575 14.1275C16.1325 14.0525 16.4 13.725 16.4 13.3425V4.72002L17.0425 4.59252C17.5375 4.49252 18 4.87002 18 5.37502V14.545C18 14.9275 17.73 15.255 17.3575 15.33L10 16.8L2.6425 15.3275C2.27 15.255 2 14.925 2 14.545V5.37502C2 4.87002 2.4625 4.49252 2.9575 4.59252L3.6 4.72002V13.345C3.6 13.7275 3.87 14.055 4.2425 14.13V14.1275ZM10.4 14.2V4.80002L14.6675 4.07752C15.155 3.99502 15.6 4.37252 15.6 4.86752V13.2L10.4 14.2Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8697_2515">
          <rect
            width="16"
            height="12.8"
            fill="white"
            transform="translate(2 4)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
