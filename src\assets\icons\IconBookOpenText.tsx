import React from 'react';

type IconBookOpenTextProps = {
  color?: string;
  size?: number;
};

export const IconBookOpenText: React.FC<IconBookOpenTextProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.125 3.75H12.5C12.0149 3.75 11.5364 3.86295 11.1025 4.07991C10.6685 4.29688 10.2911 4.61189 10 5C9.70892 4.61189 9.33147 4.29688 8.89754 4.07991C8.46362 3.86295 7.98514 3.75 7.5 3.75H1.875C1.70924 3.75 1.55027 3.81585 1.43306 3.93306C1.31585 4.05027 1.25 4.20924 1.25 4.375V15.625C1.25 15.7908 1.31585 15.9497 1.43306 16.0669C1.55027 16.1842 1.70924 16.25 1.875 16.25H7.5C7.99728 16.25 8.47419 16.4475 8.82583 16.7992C9.17746 17.1508 9.375 17.6277 9.375 18.125C9.375 18.2908 9.44085 18.4497 9.55806 18.5669C9.67527 18.6842 9.83424 18.75 10 18.75C10.1658 18.75 10.3247 18.6842 10.4419 18.5669C10.5592 18.4497 10.625 18.2908 10.625 18.125C10.625 17.6277 10.8225 17.1508 11.1742 16.7992C11.5258 16.4475 12.0027 16.25 12.5 16.25H18.125C18.2908 16.25 18.4497 16.1842 18.5669 16.0669C18.6842 15.9497 18.75 15.7908 18.75 15.625V4.375C18.75 4.20924 18.6842 4.05027 18.5669 3.93306C18.4497 3.81585 18.2908 3.75 18.125 3.75ZM7.5 15H2.5V5H7.5C7.99728 5 8.47419 5.19754 8.82583 5.54917C9.17746 5.90081 9.375 6.37772 9.375 6.875V15.625C8.83458 15.2183 8.17633 14.9989 7.5 15ZM17.5 15H12.5C11.8237 14.9989 11.1654 15.2183 10.625 15.625V6.875C10.625 6.37772 10.8225 5.90081 11.1742 5.54917C11.5258 5.19754 12.0027 5 12.5 5H17.5V15ZM12.5 6.875H15.625C15.7908 6.875 15.9497 6.94085 16.0669 7.05806C16.1842 7.17527 16.25 7.33424 16.25 7.5C16.25 7.66576 16.1842 7.82473 16.0669 7.94194C15.9497 8.05915 15.7908 8.125 15.625 8.125H12.5C12.3342 8.125 12.1753 8.05915 12.0581 7.94194C11.9408 7.82473 11.875 7.66576 11.875 7.5C11.875 7.33424 11.9408 7.17527 12.0581 7.05806C12.1753 6.94085 12.3342 6.875 12.5 6.875ZM16.25 10C16.25 10.1658 16.1842 10.3247 16.0669 10.4419C15.9497 10.5592 15.7908 10.625 15.625 10.625H12.5C12.3342 10.625 12.1753 10.5592 12.0581 10.4419C11.9408 10.3247 11.875 10.1658 11.875 10C11.875 9.83424 11.9408 9.67527 12.0581 9.55806C12.1753 9.44085 12.3342 9.375 12.5 9.375H15.625C15.7908 9.375 15.9497 9.44085 16.0669 9.55806C16.1842 9.67527 16.25 9.83424 16.25 10ZM16.25 12.5C16.25 12.6658 16.1842 12.8247 16.0669 12.9419C15.9497 13.0592 15.7908 13.125 15.625 13.125H12.5C12.3342 13.125 12.1753 13.0592 12.0581 12.9419C11.9408 12.8247 11.875 12.6658 11.875 12.5C11.875 12.3342 11.9408 12.1753 12.0581 12.0581C12.1753 11.9408 12.3342 11.875 12.5 11.875H15.625C15.7908 11.875 15.9497 11.9408 16.0669 12.0581C16.1842 12.1753 16.25 12.3342 16.25 12.5Z"
        fill={color}
      />
    </svg>
  );
};
