import React from 'react';

type IconBookOpenUserProps = {
  color?: string;
  size?: number;
};

export const IconBookOpenUser: React.FC<IconBookOpenUserProps> = ({
  color = '#3C3C3C',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.5 4.50063H10C9.61189 4.50063 9.2291 4.59099 8.88197 4.76456C8.53483 4.93813 8.23287 5.19014 8 5.50063C7.76713 5.19014 7.46517 4.93813 7.11803 4.76456C6.7709 4.59099 6.38811 4.50063 6 4.50063H1.5C1.36739 4.50063 1.24021 4.5533 1.14645 4.64707C1.05268 4.74084 1 4.86802 1 5.00063V12.5006C1 12.6332 1.05268 12.7604 1.14645 12.8542C1.24021 12.9479 1.36739 13.0006 1.5 13.0006H6C6.39782 13.0006 6.77936 13.1587 7.06066 13.44C7.34196 13.7213 7.5 14.1028 7.5 14.5006C7.5 14.6332 7.55268 14.7604 7.64645 14.8542C7.74021 14.9479 7.86739 15.0006 8 15.0006C8.13261 15.0006 8.25979 14.9479 8.35355 14.8542C8.44732 14.7604 8.5 14.6332 8.5 14.5006C8.5 14.1028 8.65804 13.7213 8.93934 13.44C9.22064 13.1587 9.60218 13.0006 10 13.0006H14.5C14.6326 13.0006 14.7598 12.9479 14.8536 12.8542C14.9473 12.7604 15 12.6332 15 12.5006V5.00063C15 4.86802 14.9473 4.74084 14.8536 4.64707C14.7598 4.5533 14.6326 4.50063 14.5 4.50063ZM6 12.0006H2V5.50063H6C6.39782 5.50063 6.77936 5.65866 7.06066 5.93996C7.34196 6.22127 7.5 6.6028 7.5 7.00063V12.5006C7.06766 12.1753 6.54106 11.9998 6 12.0006ZM14 12.0006H10C9.45893 11.9998 8.93234 12.1753 8.5 12.5006V7.00063C8.5 6.6028 8.65804 6.22127 8.93934 5.93996C9.22064 5.65866 9.60218 5.50063 10 5.50063H14V12.0006ZM5.6 2.7C5.87944 2.32741 6.24179 2.025 6.65836 1.81672C7.07493 1.60844 7.53426 1.5 8 1.5C8.46574 1.5 8.92507 1.60844 9.34164 1.81672C9.75821 2.025 10.1206 2.32741 10.4 2.7C10.4797 2.806 10.5141 2.93934 10.4955 3.07067C10.4769 3.20201 10.4069 3.32058 10.3009 3.40031C10.1949 3.48004 10.0616 3.5144 9.93026 3.49582C9.79893 3.47724 9.68036 3.40725 9.60063 3.30125C9.41436 3.05262 9.17273 2.8508 8.89489 2.71179C8.61706 2.57279 8.31067 2.50042 8 2.50042C7.68933 2.50042 7.38294 2.57279 7.10511 2.71179C6.82727 2.8508 6.58565 3.05262 6.39937 3.30125C6.3599 3.35374 6.31047 3.39794 6.25391 3.43132C6.19735 3.4647 6.13477 3.48662 6.06974 3.49582C6.0047 3.50502 5.9385 3.50132 5.8749 3.48493C5.8113 3.46854 5.75155 3.43979 5.69906 3.40031C5.64657 3.36083 5.60238 3.3114 5.56899 3.25484C5.53561 3.19828 5.51369 3.1357 5.50449 3.07067C5.49529 3.00564 5.49899 2.93944 5.51538 2.87584C5.53177 2.81224 5.56052 2.75249 5.6 2.7Z"
        fill={color}
      />
    </svg>
  );
};
