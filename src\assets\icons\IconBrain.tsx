import React from 'react';

type IconBrainProps = {
  color?: string;
  size?: number;
};

export const IconBrain: React.FC<IconBrainProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.3751 9.6875C19.3743 8.85963 19.139 8.04892 18.6966 7.34919C18.2542 6.64946 17.6226 6.08932 16.8751 5.73359V5.625C16.8744 4.8197 16.6144 4.03603 16.1337 3.38993C15.6531 2.74384 14.9771 2.26965 14.206 2.03752C13.4349 1.8054 12.6095 1.82767 11.8521 2.10105C11.0946 2.37443 10.4452 2.88439 10.0001 3.55547C9.55495 2.88439 8.9056 2.37443 8.14813 2.10105C7.39065 1.82767 6.56529 1.8054 5.79417 2.03752C5.02305 2.26965 4.34714 2.74384 3.86645 3.38993C3.38576 4.03603 3.12582 4.8197 3.1251 5.625V5.73359C2.37692 6.08835 1.74478 6.64814 1.30214 7.34791C0.859488 8.04768 0.624512 8.8587 0.624512 9.68672C0.624512 10.5147 0.859488 11.3258 1.30214 12.0255C1.74478 12.7253 2.37692 13.2851 3.1251 13.6398V13.75C3.12582 14.5553 3.38576 15.339 3.86645 15.9851C4.34714 16.6312 5.02305 17.1054 5.79417 17.3375C6.56529 17.5696 7.39065 17.5473 8.14813 17.274C8.9056 17.0006 9.55495 16.4906 10.0001 15.8195C10.4452 16.4906 11.0946 17.0006 11.8521 17.274C12.6095 17.5473 13.4349 17.5696 14.206 17.3375C14.9771 17.1054 15.6531 16.6312 16.1337 15.9851C16.6144 15.339 16.8744 14.5553 16.8751 13.75V13.6398C17.6225 13.2844 18.254 12.7245 18.6964 12.0251C19.1389 11.3256 19.3742 10.5151 19.3751 9.6875ZM6.8751 16.25C6.25865 16.2499 5.66397 16.0221 5.20527 15.6102C4.74658 15.1984 4.45621 14.6316 4.38994 14.0188C4.59205 14.0476 4.79594 14.0622 5.0001 14.0625H5.6251C5.79086 14.0625 5.94983 13.9967 6.06704 13.8794C6.18425 13.7622 6.2501 13.6033 6.2501 13.4375C6.2501 13.2717 6.18425 13.1128 6.06704 12.9956C5.94983 12.8783 5.79086 12.8125 5.6251 12.8125H5.0001C4.26225 12.8134 3.5479 12.5531 2.98352 12.0778C2.41913 11.6026 2.04112 10.9429 1.91641 10.2157C1.7917 9.48847 1.92833 8.74057 2.30211 8.10441C2.67589 7.46825 3.26272 6.98487 3.95869 6.73984C4.08047 6.69673 4.18589 6.61694 4.26046 6.51145C4.33503 6.40597 4.37508 6.27996 4.3751 6.15078V5.625C4.3751 4.96196 4.63849 4.32607 5.10733 3.85723C5.57617 3.38839 6.21206 3.125 6.8751 3.125C7.53814 3.125 8.17402 3.38839 8.64286 3.85723C9.1117 4.32607 9.3751 4.96196 9.3751 5.625V10.9578C8.68878 10.3405 7.79817 9.99932 6.8751 10C6.70934 10 6.55037 10.0658 6.43316 10.1831C6.31595 10.3003 6.2501 10.4592 6.2501 10.625C6.2501 10.7908 6.31595 10.9497 6.43316 11.0669C6.55037 11.1842 6.70934 11.25 6.8751 11.25C7.53814 11.25 8.17402 11.5134 8.64286 11.9822C9.1117 12.4511 9.3751 13.087 9.3751 13.75C9.3751 14.413 9.1117 15.0489 8.64286 15.5178C8.17402 15.9866 7.53814 16.25 6.8751 16.25ZM15.0001 12.8125H14.3751C14.2093 12.8125 14.0504 12.8783 13.9332 12.9956C13.8159 13.1128 13.7501 13.2717 13.7501 13.4375C13.7501 13.6033 13.8159 13.7622 13.9332 13.8794C14.0504 13.9967 14.2093 14.0625 14.3751 14.0625H15.0001C15.2043 14.0622 15.4081 14.0476 15.6103 14.0188C15.5583 14.499 15.3684 14.9538 15.0635 15.3284C14.7585 15.703 14.3516 15.9812 13.8919 16.1294C13.4322 16.2776 12.9394 16.2895 12.4731 16.1636C12.0067 16.0377 11.5869 15.7794 11.2643 15.4199C10.9416 15.0605 10.73 14.6153 10.655 14.1381C10.5801 13.6609 10.6449 13.1723 10.8417 12.7312C11.0386 12.2901 11.359 11.9155 11.7642 11.6526C12.1694 11.3898 12.6421 11.2499 13.1251 11.25C13.2909 11.25 13.4498 11.1842 13.567 11.0669C13.6842 10.9497 13.7501 10.7908 13.7501 10.625C13.7501 10.4592 13.6842 10.3003 13.567 10.1831C13.4498 10.0658 13.2909 10 13.1251 10C12.202 9.99932 11.3114 10.3405 10.6251 10.9578V5.625C10.6251 4.96196 10.8885 4.32607 11.3573 3.85723C11.8262 3.38839 12.4621 3.125 13.1251 3.125C13.7881 3.125 14.424 3.38839 14.8929 3.85723C15.3617 4.32607 15.6251 4.96196 15.6251 5.625V6.15078C15.6251 6.27996 15.6652 6.40597 15.7397 6.51145C15.8143 6.61694 15.9197 6.69673 16.0415 6.73984C16.7375 6.98487 17.3243 7.46825 17.6981 8.10441C18.0719 8.74057 18.2085 9.48847 18.0838 10.2157C17.9591 10.9429 17.5811 11.6026 17.0167 12.0778C16.4523 12.5531 15.7379 12.8134 15.0001 12.8125ZM16.2501 8.75C16.2501 8.91576 16.1842 9.07473 16.067 9.19194C15.9498 9.30915 15.7909 9.375 15.6251 9.375H15.3126C14.5667 9.375 13.8513 9.07868 13.3239 8.55124C12.7964 8.02379 12.5001 7.30842 12.5001 6.5625V6.25C12.5001 6.08424 12.5659 5.92527 12.6832 5.80806C12.8004 5.69085 12.9593 5.625 13.1251 5.625C13.2909 5.625 13.4498 5.69085 13.567 5.80806C13.6842 5.92527 13.7501 6.08424 13.7501 6.25V6.5625C13.7501 6.9769 13.9147 7.37433 14.2077 7.66735C14.5008 7.96038 14.8982 8.125 15.3126 8.125H15.6251C15.7909 8.125 15.9498 8.19085 16.067 8.30806C16.1842 8.42527 16.2501 8.58424 16.2501 8.75ZM4.6876 9.375H4.3751C4.20934 9.375 4.05037 9.30915 3.93316 9.19194C3.81595 9.07473 3.7501 8.91576 3.7501 8.75C3.7501 8.58424 3.81595 8.42527 3.93316 8.30806C4.05037 8.19085 4.20934 8.125 4.3751 8.125H4.6876C5.102 8.125 5.49943 7.96038 5.79245 7.66735C6.08548 7.37433 6.2501 6.9769 6.2501 6.5625V6.25C6.2501 6.08424 6.31595 5.92527 6.43316 5.80806C6.55037 5.69085 6.70934 5.625 6.8751 5.625C7.04086 5.625 7.19983 5.69085 7.31704 5.80806C7.43425 5.92527 7.5001 6.08424 7.5001 6.25V6.5625C7.5001 7.30842 7.20378 8.02379 6.67633 8.55124C6.14889 9.07868 5.43352 9.375 4.6876 9.375Z"
        fill={color}
      />
    </svg>
  );
};
