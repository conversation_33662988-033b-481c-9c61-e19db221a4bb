import React from 'react';

type IconCalendarProps = {
  color?: string;
  size?: number;
};

export const IconCalendar: React.FC<IconCalendarProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.25 2.5H14.375V1.875C14.375 1.70924 14.3092 1.55027 14.1919 1.43306C14.0747 1.31585 13.9158 1.25 13.75 1.25C13.5842 1.25 13.4253 1.31585 13.3081 1.43306C13.1908 1.55027 13.125 1.70924 13.125 1.875V2.5H6.875V1.875C6.875 1.70924 6.80915 1.55027 6.69194 1.43306C6.57473 1.31585 6.41576 1.25 6.25 1.25C6.08424 1.25 5.92527 1.31585 5.80806 1.43306C5.69085 1.55027 5.625 1.70924 5.625 1.875V2.5H3.75C3.41848 2.5 3.10054 2.6317 2.86612 2.86612C2.6317 3.10054 2.5 3.41848 2.5 3.75V16.25C2.5 16.5815 2.6317 16.8995 2.86612 17.1339C3.10054 17.3683 3.41848 17.5 3.75 17.5H16.25C16.5815 17.5 16.8995 17.3683 17.1339 17.1339C17.3683 16.8995 17.5 16.5815 17.5 16.25V3.75C17.5 3.41848 17.3683 3.10054 17.1339 2.86612C16.8995 2.6317 16.5815 2.5 16.25 2.5ZM5.625 3.75V4.375C5.625 4.54076 5.69085 4.69973 5.80806 4.81694C5.92527 4.93415 6.08424 5 6.25 5C6.41576 5 6.57473 4.93415 6.69194 4.81694C6.80915 4.69973 6.875 4.54076 6.875 4.375V3.75H13.125V4.375C13.125 4.54076 13.1908 4.69973 13.3081 4.81694C13.4253 4.93415 13.5842 5 13.75 5C13.9158 5 14.0747 4.93415 14.1919 4.81694C14.3092 4.69973 14.375 4.54076 14.375 4.375V3.75H16.25V6.25H3.75V3.75H5.625ZM16.25 16.25H3.75V7.5H16.25V16.25ZM8.75 9.375V14.375C8.75 14.5408 8.68415 14.6997 8.56694 14.8169C8.44973 14.9342 8.29076 15 8.125 15C7.95924 15 7.80027 14.9342 7.68306 14.8169C7.56585 14.6997 7.5 14.5408 7.5 14.375V10.3859L7.15469 10.5594C7.00633 10.6336 6.83459 10.6458 6.67723 10.5933C6.51988 10.5409 6.3898 10.428 6.31562 10.2797C6.24145 10.1313 6.22924 9.95959 6.28169 9.80223C6.33414 9.64488 6.44696 9.5148 6.59531 9.44062L7.84531 8.81562C7.94063 8.76793 8.04657 8.7454 8.15305 8.75018C8.25954 8.75497 8.36303 8.7869 8.45369 8.84296C8.54435 8.89902 8.61916 8.97733 8.67103 9.07045C8.72289 9.16357 8.75008 9.26841 8.75 9.375ZM13.3719 11.7539L11.875 13.75H13.125C13.2908 13.75 13.4497 13.8158 13.5669 13.9331C13.6842 14.0503 13.75 14.2092 13.75 14.375C13.75 14.5408 13.6842 14.6997 13.5669 14.8169C13.4497 14.9342 13.2908 15 13.125 15H10.625C10.5089 15 10.3952 14.9677 10.2964 14.9067C10.1977 14.8456 10.1179 14.7583 10.066 14.6545C10.0141 14.5507 9.9921 14.4345 10.0025 14.3189C10.0129 14.2033 10.0554 14.0929 10.125 14L12.3734 11.0023C12.4246 10.9343 12.4613 10.8564 12.4813 10.7737C12.5012 10.6909 12.5041 10.6049 12.4897 10.521C12.4752 10.4371 12.4438 10.357 12.3973 10.2856C12.3508 10.2143 12.2902 10.1532 12.2192 10.1062C12.1483 10.0591 12.0684 10.027 11.9846 10.0119C11.9008 9.99678 11.8148 9.99895 11.7319 10.0183C11.649 10.0376 11.5709 10.0737 11.5024 10.1243C11.4339 10.1749 11.3764 10.2389 11.3336 10.3125C11.2938 10.3858 11.2397 10.4505 11.1744 10.5026C11.1092 10.5547 11.0343 10.5932 10.9539 10.6158C10.8736 10.6385 10.7895 10.6448 10.7067 10.6344C10.6239 10.624 10.544 10.5972 10.4717 10.5555C10.3995 10.5137 10.3363 10.4579 10.2859 10.3914C10.2356 10.3248 10.1991 10.2488 10.1785 10.1679C10.158 10.087 10.1539 10.0028 10.1664 9.92033C10.179 9.83781 10.2079 9.75865 10.2516 9.6875C10.458 9.33022 10.7766 9.05103 11.1578 8.8932C11.5391 8.73537 11.9618 8.70772 12.3603 8.81453C12.7589 8.92133 13.1111 9.15664 13.3624 9.48397C13.6136 9.8113 13.7499 10.2124 13.75 10.625C13.7513 11.0326 13.6184 11.4293 13.3719 11.7539Z"
        fill={color}
      />
    </svg>
  );
};
