import React from 'react';

type IconCalendarDayOneProps = {
  color?: string;
  size?: number;
};

export const IconCalendarDayOne: React.FC<IconCalendarDayOneProps> = ({
  color = '#B1B1B1',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8462_60097)">
        <path
          d="M9.5 10C9.775 10 10 10.225 10 10.5V13.5C10 13.775 9.775 14 9.5 14H6.5C6.225 14 6 13.775 6 13.5V10.5C6 10.225 6.225 10 6.5 10H9.5ZM7 2C7.41563 2 7.75 2.33594 7.75 2.75V4H12.25V2.75C12.25 2.33594 12.5844 2 13 2C13.4156 2 13.75 2.33594 13.75 2.75V4H15C16.1031 4 17 4.89531 17 6V16C17 17.1031 16.1031 18 15 18H5C3.89531 18 3 17.1031 3 16V6C3 4.89531 3.89531 4 5 4H6.25V2.75C6.25 2.33594 6.58437 2 7 2ZM15.5 8H4.5V16C4.5 16.275 4.72375 16.5 5 16.5H15C15.275 16.5 15.5 16.275 15.5 16V8Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8462_60097">
          <rect
            width="14"
            height="16"
            fill="white"
            transform="translate(3 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
