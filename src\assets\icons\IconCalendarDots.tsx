import React from 'react';

type IconCalendarDotsProps = {
  color?: string;
  size?: number;
};

export const IconCalendarDots: React.FC<IconCalendarDotsProps> = ({
  color = '#717171',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13 2H11.5V1.5C11.5 1.36739 11.4473 1.24021 11.3536 1.14645C11.2598 1.05268 11.1326 1 11 1C10.8674 1 10.7402 1.05268 10.6464 1.14645C10.5527 1.24021 10.5 1.36739 10.5 1.5V2H5.5V1.5C5.5 1.36739 5.44732 1.24021 5.35355 1.14645C5.25979 1.05268 5.13261 1 5 1C4.86739 1 4.74021 1.05268 4.64645 1.14645C4.55268 1.24021 4.5 1.36739 4.5 1.5V2H3C2.73478 2 2.48043 2.10536 2.29289 2.29289C2.10536 2.48043 2 2.73478 2 3V13C2 13.2652 2.10536 13.5196 2.29289 13.7071C2.48043 13.8946 2.73478 14 3 14H13C13.2652 14 13.5196 13.8946 13.7071 13.7071C13.8946 13.5196 14 13.2652 14 13V3C14 2.73478 13.8946 2.48043 13.7071 2.29289C13.5196 2.10536 13.2652 2 13 2ZM4.5 3V3.5C4.5 3.63261 4.55268 3.75979 4.64645 3.85355C4.74021 3.94732 4.86739 4 5 4C5.13261 4 5.25979 3.94732 5.35355 3.85355C5.44732 3.75979 5.5 3.63261 5.5 3.5V3H10.5V3.5C10.5 3.63261 10.5527 3.75979 10.6464 3.85355C10.7402 3.94732 10.8674 4 11 4C11.1326 4 11.2598 3.94732 11.3536 3.85355C11.4473 3.75979 11.5 3.63261 11.5 3.5V3H13V5H3V3H4.5ZM13 13H3V6H13V13ZM8.75 8.25C8.75 8.39834 8.70601 8.54334 8.6236 8.66668C8.54119 8.79001 8.42406 8.88614 8.28701 8.94291C8.14997 8.99967 7.99917 9.01453 7.85368 8.98559C7.7082 8.95665 7.57456 8.88522 7.46967 8.78033C7.36478 8.67544 7.29335 8.5418 7.26441 8.39632C7.23547 8.25083 7.25032 8.10003 7.30709 7.96299C7.36386 7.82594 7.45999 7.70881 7.58332 7.6264C7.70666 7.54399 7.85166 7.5 8 7.5C8.19891 7.5 8.38968 7.57902 8.53033 7.71967C8.67098 7.86032 8.75 8.05109 8.75 8.25ZM11.5 8.25C11.5 8.39834 11.456 8.54334 11.3736 8.66668C11.2912 8.79001 11.1741 8.88614 11.037 8.94291C10.9 8.99967 10.7492 9.01453 10.6037 8.98559C10.4582 8.95665 10.3246 8.88522 10.2197 8.78033C10.1148 8.67544 10.0434 8.5418 10.0144 8.39632C9.98547 8.25083 10.0003 8.10003 10.0571 7.96299C10.1139 7.82594 10.21 7.70881 10.3333 7.6264C10.4567 7.54399 10.6017 7.5 10.75 7.5C10.9489 7.5 11.1397 7.57902 11.2803 7.71967C11.421 7.86032 11.5 8.05109 11.5 8.25ZM6 10.75C6 10.8983 5.95601 11.0433 5.8736 11.1667C5.79119 11.29 5.67406 11.3861 5.53701 11.4429C5.39997 11.4997 5.24917 11.5145 5.10368 11.4856C4.9582 11.4566 4.82456 11.3852 4.71967 11.2803C4.61478 11.1754 4.54335 11.0418 4.51441 10.8963C4.48547 10.7508 4.50032 10.6 4.55709 10.463C4.61386 10.3259 4.70999 10.2088 4.83332 10.1264C4.95666 10.044 5.10166 10 5.25 10C5.44891 10 5.63968 10.079 5.78033 10.2197C5.92098 10.3603 6 10.5511 6 10.75ZM8.75 10.75C8.75 10.8983 8.70601 11.0433 8.6236 11.1667C8.54119 11.29 8.42406 11.3861 8.28701 11.4429C8.14997 11.4997 7.99917 11.5145 7.85368 11.4856C7.7082 11.4566 7.57456 11.3852 7.46967 11.2803C7.36478 11.1754 7.29335 11.0418 7.26441 10.8963C7.23547 10.7508 7.25032 10.6 7.30709 10.463C7.36386 10.3259 7.45999 10.2088 7.58332 10.1264C7.70666 10.044 7.85166 10 8 10C8.19891 10 8.38968 10.079 8.53033 10.2197C8.67098 10.3603 8.75 10.5511 8.75 10.75ZM11.5 10.75C11.5 10.8983 11.456 11.0433 11.3736 11.1667C11.2912 11.29 11.1741 11.3861 11.037 11.4429C10.9 11.4997 10.7492 11.5145 10.6037 11.4856C10.4582 11.4566 10.3246 11.3852 10.2197 11.2803C10.1148 11.1754 10.0434 11.0418 10.0144 10.8963C9.98547 10.7508 10.0003 10.6 10.0571 10.463C10.1139 10.3259 10.21 10.2088 10.3333 10.1264C10.4567 10.044 10.6017 10 10.75 10C10.9489 10 11.1397 10.079 11.2803 10.2197C11.421 10.3603 11.5 10.5511 11.5 10.75Z"
        fill={color}
      />
    </svg>
  );
};
