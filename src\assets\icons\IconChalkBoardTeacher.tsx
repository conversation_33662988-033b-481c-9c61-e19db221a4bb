import React from 'react';

type IconChalkBoardTeacherProps = {
  color?: string;
  size?: number;
};

export const IconChalkBoardTeacher: React.FC<IconChalkBoardTeacherProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.875 3.125H3.125C2.79348 3.125 2.47554 3.2567 2.24112 3.49112C2.0067 3.72554 1.875 4.04348 1.875 4.375V15.625C1.875 15.9565 2.0067 16.2745 2.24112 16.5089C2.47554 16.7433 2.79348 16.875 3.125 16.875H4.17109C4.28938 16.875 4.40525 16.8415 4.50524 16.7783C4.60522 16.7151 4.68522 16.6248 4.73594 16.518C5.03986 15.8763 5.5197 15.3341 6.11965 14.9544C6.71959 14.5748 7.415 14.3732 8.125 14.3732C8.835 14.3732 9.53041 14.5748 10.1304 14.9544C10.7303 15.3341 11.2101 15.8763 11.5141 16.518C11.5648 16.6248 11.6448 16.7151 11.7448 16.7783C11.8448 16.8415 11.9606 16.875 12.0789 16.875H16.875C17.2065 16.875 17.5245 16.7433 17.7589 16.5089C17.9933 16.2745 18.125 15.9565 18.125 15.625V4.375C18.125 4.04348 17.9933 3.72554 17.7589 3.49112C17.5245 3.2567 17.2065 3.125 16.875 3.125ZM6.25 11.25C6.25 10.8792 6.35997 10.5166 6.56599 10.2083C6.77202 9.89996 7.06486 9.65964 7.40747 9.51773C7.75008 9.37581 8.12708 9.33868 8.49079 9.41103C8.85451 9.48337 9.1886 9.66195 9.45083 9.92417C9.71305 10.1864 9.89163 10.5205 9.96397 10.8842C10.0363 11.2479 9.99919 11.6249 9.85727 11.9675C9.71536 12.3101 9.47504 12.603 9.16669 12.809C8.85835 13.015 8.49584 13.125 8.125 13.125C7.62772 13.125 7.15081 12.9275 6.79917 12.5758C6.44754 12.2242 6.25 11.7473 6.25 11.25ZM16.875 15.625H12.4555C11.9335 14.7277 11.1468 14.0138 10.2031 13.5812C10.6762 13.16 11.0102 12.6049 11.1606 11.9895C11.311 11.3741 11.2709 10.7276 11.0455 10.1356C10.8202 9.54352 10.4202 9.03396 9.89859 8.67443C9.37701 8.31489 8.75849 8.12236 8.125 8.12236C7.49151 8.12236 6.87299 8.31489 6.35141 8.67443C5.82984 9.03396 5.42985 9.54352 5.20447 10.1356C4.97909 10.7276 4.93896 11.3741 5.0894 11.9895C5.23985 12.6049 5.57376 13.16 6.04688 13.5812C5.10321 14.0138 4.31646 14.7277 3.79453 15.625H3.125V4.375H16.875V15.625ZM4.375 7.5V6.25C4.375 6.08424 4.44085 5.92527 4.55806 5.80806C4.67527 5.69085 4.83424 5.625 5 5.625H15C15.1658 5.625 15.3247 5.69085 15.4419 5.80806C15.5592 5.92527 15.625 6.08424 15.625 6.25V13.75C15.625 13.9158 15.5592 14.0747 15.4419 14.1919C15.3247 14.3092 15.1658 14.375 15 14.375H13.75C13.5842 14.375 13.4253 14.3092 13.3081 14.1919C13.1908 14.0747 13.125 13.9158 13.125 13.75C13.125 13.5842 13.1908 13.4253 13.3081 13.3081C13.4253 13.1908 13.5842 13.125 13.75 13.125H14.375V6.875H5.625V7.5C5.625 7.66576 5.55915 7.82473 5.44194 7.94194C5.32473 8.05915 5.16576 8.125 5 8.125C4.83424 8.125 4.67527 8.05915 4.55806 7.94194C4.44085 7.82473 4.375 7.66576 4.375 7.5Z"
        fill={color}
      />
    </svg>
  );
};
