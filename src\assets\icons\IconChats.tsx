import React from 'react';

type IconChatsProps = {
  color?: string;
  size?: number;
};

export const IconChats: React.FC<IconChatsProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.875 6.25H14.375V3.75C14.375 3.41848 14.2433 3.10054 14.0089 2.86612C13.7745 2.6317 13.4565 2.5 13.125 2.5H3.125C2.79348 2.5 2.47554 2.6317 2.24112 2.86612C2.0067 3.10054 1.875 3.41848 1.875 3.75V13.75C1.87537 13.8676 1.9089 13.9827 1.97174 14.0821C2.03458 14.1814 2.12418 14.2611 2.23025 14.3118C2.33631 14.3626 2.45455 14.3824 2.57136 14.369C2.68818 14.3555 2.79884 14.3094 2.89062 14.2359L5.625 12.0312V14.375C5.625 14.7065 5.7567 15.0245 5.99112 15.2589C6.22554 15.4933 6.54348 15.625 6.875 15.625H14.1867L17.1094 17.9859C17.22 18.0754 17.3578 18.1244 17.5 18.125C17.6658 18.125 17.8247 18.0592 17.9419 17.9419C18.0592 17.8247 18.125 17.6658 18.125 17.5V7.5C18.125 7.16848 17.9933 6.85054 17.7589 6.61612C17.5245 6.3817 17.2065 6.25 16.875 6.25ZM5.19922 10.7641L3.125 12.4414V3.75H13.125V10.625H5.59219C5.44914 10.625 5.31044 10.6741 5.19922 10.7641ZM16.875 16.1914L14.8008 14.5141C14.6902 14.4246 14.5524 14.3756 14.4102 14.375H6.875V11.875H13.125C13.4565 11.875 13.7745 11.7433 14.0089 11.5089C14.2433 11.2745 14.375 10.9565 14.375 10.625V7.5H16.875V16.1914Z"
        fill={color}
      />
    </svg>
  );
};
