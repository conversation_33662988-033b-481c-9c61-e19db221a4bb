import React from 'react';

type IconChevronProps = {
  color?: string;
  size?: number;
  direction?: 'left' | 'right' | 'top' | 'bottom';
};

export const IconChevron: React.FC<IconChevronProps> = ({
  color = '#767676',
  size = 20,
  direction = 'right',
}) => {
  const rotationMap: Record<
    NonNullable<IconChevronProps['direction']>,
    string
  > = {
    right: 'rotate(-90deg)',
    left: 'rotate(90deg)',
    top: 'rotate(180deg)',
    bottom: 'rotate(0deg)',
  };

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: rotationMap[direction] }}
    >
      <g clipPath="url(#clip0_8837_18574)">
        <path
          d="M12.2125 7.03761L8.41245 10.6151C8.29745 10.7451 8.14745 10.8001 7.99995 10.8001C7.85245 10.8001 7.70295 10.7454 7.58745 10.6361L3.78745 7.03761C3.5467 6.80761 3.5367 6.42761 3.76395 6.18761C3.9922 5.94511 4.37345 5.93761 4.61245 6.16511L7.99995 9.37261L11.3875 6.16261C11.6265 5.93526 12.0062 5.94386 12.236 6.18605C12.4625 6.42761 12.4525 6.80761 12.2125 7.03761Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8837_18574">
          <rect
            width="9.6"
            height="12.8"
            fill="white"
            transform="translate(3.19995 1.59961)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
