import React from 'react';

type IconClockProps = {
  color?: string;
  size?: number;
};

export const IconClock: React.FC<IconClockProps> = ({
  color = '#717171',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.5 1.5C7.21442 1.5 5.95772 1.88122 4.8888 2.59545C3.81988 3.30968 2.98676 4.32484 2.49479 5.51256C2.00282 6.70028 1.87409 8.00721 2.1249 9.26809C2.3757 10.529 2.99477 11.6872 3.90381 12.5962C4.81285 13.5052 5.97104 14.1243 7.23192 14.3751C8.49279 14.6259 9.79973 14.4972 10.9874 14.0052C12.1752 13.5132 13.1903 12.6801 13.9046 11.6112C14.6188 10.5423 15 9.28558 15 8C14.9982 6.27665 14.3128 4.62441 13.0942 3.40582C11.8756 2.18722 10.2234 1.50182 8.5 1.5ZM8.5 13.5C7.41221 13.5 6.34884 13.1774 5.44437 12.5731C4.5399 11.9687 3.83495 11.1098 3.41867 10.1048C3.00238 9.09977 2.89347 7.9939 3.10568 6.927C3.3179 5.86011 3.84173 4.8801 4.61092 4.11091C5.3801 3.34172 6.36011 2.8179 7.42701 2.60568C8.4939 2.39346 9.59977 2.50238 10.6048 2.91866C11.6098 3.33494 12.4687 4.03989 13.0731 4.94436C13.6774 5.84883 14 6.9122 14 8C13.9983 9.45818 13.4184 10.8562 12.3873 11.8873C11.3562 12.9184 9.95819 13.4983 8.5 13.5ZM12.5 8C12.5 8.13261 12.4473 8.25979 12.3536 8.35355C12.2598 8.44732 12.1326 8.5 12 8.5H8.5C8.36739 8.5 8.24022 8.44732 8.14645 8.35355C8.05268 8.25979 8 8.13261 8 8V4.5C8 4.36739 8.05268 4.24021 8.14645 4.14645C8.24022 4.05268 8.36739 4 8.5 4C8.63261 4 8.75979 4.05268 8.85356 4.14645C8.94732 4.24021 9 4.36739 9 4.5V7.5H12C12.1326 7.5 12.2598 7.55268 12.3536 7.64645C12.4473 7.74021 12.5 7.86739 12.5 8Z"
        fill={color}
      />
    </svg>
  );
};
