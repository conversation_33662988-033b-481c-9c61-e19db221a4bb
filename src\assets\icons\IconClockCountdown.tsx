import React from 'react';

type IconClockCountdownProps = {
  color?: string;
  size?: number;
};

export const IconClockCountdown: React.FC<IconClockCountdownProps> = ({
  color = '#3C3C3C',
  size = 14,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.4998 7.541C13.3936 8.77598 12.9369 9.95494 12.1834 10.9392C11.4299 11.9234 10.4109 12.6719 9.24645 13.0967C8.08197 13.5215 6.82039 13.6049 5.61013 13.3371C4.39987 13.0692 3.29128 12.4613 2.41479 11.5848C1.5383 10.7083 0.930374 9.59972 0.66253 8.38946C0.394686 7.1792 0.478069 5.91762 0.902869 4.75314C1.32767 3.58866 2.07621 2.56973 3.06044 1.81622C4.04466 1.06271 5.22362 0.605974 6.4586 0.499746C6.52426 0.494329 6.59034 0.501897 6.65308 0.52202C6.71582 0.542143 6.77397 0.574425 6.82423 0.617024C6.87449 0.659623 6.91587 0.711704 6.946 0.770294C6.97614 0.828884 6.99443 0.892835 6.99985 0.958496C7.00527 1.02416 6.9977 1.09024 6.97757 1.15298C6.95745 1.21571 6.92517 1.27387 6.88257 1.32413C6.83997 1.37439 6.78789 1.41577 6.7293 1.4459C6.67071 1.47603 6.60676 1.49433 6.5411 1.49975C5.49584 1.58955 4.49798 1.97604 3.66492 2.61372C2.83186 3.25141 2.19826 4.11376 1.83868 5.09932C1.47909 6.08488 1.40848 7.15265 1.63515 8.17698C1.86182 9.20131 2.37634 10.1396 3.11818 10.8814C3.86001 11.6233 4.79829 12.1378 5.82262 12.3644C6.84695 12.5911 7.91471 12.5205 8.90027 12.1609C9.88583 11.8013 10.7482 11.1677 11.3859 10.3347C12.0236 9.50162 12.41 8.50375 12.4998 7.4585C12.5108 7.32589 12.574 7.20306 12.6755 7.11702C12.777 7.03099 12.9085 6.98881 13.0411 6.99975C13.1737 7.01069 13.2965 7.07386 13.3826 7.17536C13.4686 7.27686 13.5108 7.40839 13.4998 7.541ZM6.49985 3.49975V6.99975C6.49985 7.13235 6.55253 7.25953 6.6463 7.3533C6.74006 7.44707 6.86724 7.49975 6.99985 7.49975H10.4998C10.6325 7.49975 10.7596 7.44707 10.8534 7.3533C10.9472 7.25953 10.9998 7.13235 10.9998 6.99975C10.9998 6.86714 10.9472 6.73996 10.8534 6.64619C10.7596 6.55242 10.6325 6.49975 10.4998 6.49975H7.49985V3.49975C7.49985 3.36714 7.44717 3.23996 7.3534 3.14619C7.25963 3.05242 7.13246 2.99975 6.99985 2.99975C6.86724 2.99975 6.74006 3.05242 6.6463 3.14619C6.55253 3.23996 6.49985 3.36714 6.49985 3.49975ZM8.99985 1.99975C9.14819 1.99975 9.29319 1.95576 9.41653 1.87335C9.53986 1.79094 9.63599 1.6738 9.69276 1.53676C9.74952 1.39971 9.76438 1.24891 9.73544 1.10343C9.7065 0.957942 9.63507 0.824305 9.53018 0.719416C9.42529 0.614526 9.29165 0.543096 9.14617 0.514157C9.00068 0.485218 8.84988 0.50007 8.71284 0.556836C8.57579 0.613602 8.45866 0.709731 8.37625 0.833068C8.29384 0.956405 8.24985 1.10141 8.24985 1.24975C8.24985 1.44866 8.32887 1.63942 8.46952 1.78008C8.61017 1.92073 8.80094 1.99975 8.99985 1.99975ZM11.2498 3.49975C11.3982 3.49975 11.5432 3.45576 11.6665 3.37335C11.7899 3.29094 11.886 3.1738 11.9428 3.03676C11.9995 2.89971 12.0144 2.74891 11.9854 2.60343C11.9565 2.45794 11.8851 2.3243 11.7802 2.21942C11.6753 2.11453 11.5417 2.0431 11.3962 2.01416C11.2507 1.98522 11.0999 2.00007 10.9628 2.05684C10.8258 2.1136 10.7087 2.20973 10.6262 2.33307C10.5438 2.4564 10.4998 2.60141 10.4998 2.74975C10.4998 2.94866 10.5789 3.13942 10.7195 3.28008C10.8602 3.42073 11.0509 3.49975 11.2498 3.49975ZM12.7498 5.74975C12.8982 5.74975 13.0432 5.70576 13.1665 5.62335C13.2899 5.54094 13.386 5.4238 13.4428 5.28676C13.4995 5.14971 13.5144 4.99891 13.4854 4.85343C13.4565 4.70794 13.3851 4.5743 13.2802 4.46942C13.1753 4.36453 13.0417 4.2931 12.8962 4.26416C12.7507 4.23522 12.5999 4.25007 12.4628 4.30684C12.3258 4.3636 12.2087 4.45973 12.1262 4.58307C12.0438 4.7064 11.9998 4.85141 11.9998 4.99975C11.9998 5.19866 12.0789 5.38942 12.2195 5.53008C12.3602 5.67073 12.5509 5.74975 12.7498 5.74975Z"
        fill={color}
      />
    </svg>
  );
};
