import React from 'react';

type IconExportProps = {
  color?: string;
  size?: number;
};

export const IconExport: React.FC<IconExportProps> = ({
  color = '#3C3C3C',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.5 7.00039V13.0004C13.5 13.2656 13.3946 13.52 13.2071 13.7075C13.0196 13.895 12.7652 14.0004 12.5 14.0004H3.5C3.23478 14.0004 2.98043 13.895 2.79289 13.7075C2.60536 13.52 2.5 13.2656 2.5 13.0004V7.00039C2.5 6.73518 2.60536 6.48082 2.79289 6.29329C2.98043 6.10575 3.23478 6.00039 3.5 6.00039H5C5.13261 6.00039 5.25979 6.05307 5.35355 6.14684C5.44732 6.24061 5.5 6.36778 5.5 6.50039C5.5 6.633 5.44732 6.76018 5.35355 6.85395C5.25979 6.94771 5.13261 7.00039 5 7.00039H3.5V13.0004H12.5V7.00039H11C10.8674 7.00039 10.7402 6.94771 10.6464 6.85395C10.5527 6.76018 10.5 6.633 10.5 6.50039C10.5 6.36778 10.5527 6.24061 10.6464 6.14684C10.7402 6.05307 10.8674 6.00039 11 6.00039H12.5C12.7652 6.00039 13.0196 6.10575 13.2071 6.29329C13.3946 6.48082 13.5 6.73518 13.5 7.00039ZM5.85375 4.35414L7.5 2.70727V8.50039C7.5 8.633 7.55268 8.76018 7.64645 8.85395C7.74021 8.94772 7.86739 9.00039 8 9.00039C8.13261 9.00039 8.25979 8.94772 8.35355 8.85395C8.44732 8.76018 8.5 8.633 8.5 8.50039V2.70727L10.1462 4.35414C10.2401 4.44796 10.3673 4.50067 10.5 4.50067C10.6327 4.50067 10.7599 4.44796 10.8538 4.35414C10.9476 4.26032 11.0003 4.13308 11.0003 4.00039C11.0003 3.86771 10.9476 3.74046 10.8538 3.64664L8.35375 1.14664C8.30731 1.10016 8.25217 1.06328 8.19147 1.03811C8.13077 1.01295 8.06571 1 8 1C7.93429 1 7.86923 1.01295 7.80853 1.03811C7.74783 1.06328 7.69269 1.10016 7.64625 1.14664L5.14625 3.64664C5.05243 3.74046 4.99972 3.86771 4.99972 4.00039C4.99972 4.13308 5.05243 4.26032 5.14625 4.35414C5.24007 4.44796 5.36732 4.50067 5.5 4.50067C5.63268 4.50067 5.75993 4.44796 5.85375 4.35414Z"
        fill={color}
      />
    </svg>
  );
};
