import * as React from "react";
import { SVGProps } from "react";
const IconEye = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={28}
    height={28}
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_9709_5149)">
      <path
        d="M14 7.88892C11.7556 7.88892 9.95837 8.91114 8.65004 10.1278C7.35004 11.3334 6.48059 12.7778 6.06948 13.7695C5.97782 13.9889 5.97782 14.2334 6.06948 14.4528C6.48059 15.4445 7.35004 16.8889 8.65004 18.0945C9.95837 19.3111 11.7556 20.3334 14 20.3334C16.2445 20.3334 18.0417 19.3111 19.35 18.0945C20.65 16.8861 21.5195 15.4445 21.9334 14.4528C22.025 14.2334 22.025 13.9889 21.9334 13.7695C21.5195 12.7778 20.65 11.3334 19.35 10.1278C18.0417 8.91114 16.2445 7.88892 14 7.88892ZM10 14.1111C10 13.0503 10.4215 12.0329 11.1716 11.2827C11.9218 10.5326 12.9392 10.1111 14 10.1111C15.0609 10.1111 16.0783 10.5326 16.8285 11.2827C17.5786 12.0329 18 13.0503 18 14.1111C18 15.172 17.5786 16.1894 16.8285 16.9396C16.0783 17.6897 15.0609 18.1111 14 18.1111C12.9392 18.1111 11.9218 17.6897 11.1716 16.9396C10.4215 16.1894 10 15.172 10 14.1111ZM14 12.3334C14 13.3139 13.2028 14.1111 12.2223 14.1111C12.025 14.1111 11.8361 14.0778 11.6584 14.0195C11.5056 13.9695 11.3278 14.0639 11.3334 14.225C11.3417 14.4167 11.3695 14.6084 11.4223 14.8C11.8028 16.2222 13.2667 17.0667 14.6889 16.6861C16.1111 16.3056 16.9556 14.8417 16.575 13.4195C16.2667 12.2667 15.2473 11.4917 14.1139 11.4445C13.9528 11.4389 13.8584 11.6139 13.9084 11.7695C13.9667 11.9472 14 12.1361 14 12.3334Z"
        fill="#3C3C3C"
      />
    </g>
    <defs>
      <clipPath id="clip0_9709_5149">
        <rect
          width={16}
          height={14.2222}
          fill="white"
          transform="translate(6 7)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default IconEye;
