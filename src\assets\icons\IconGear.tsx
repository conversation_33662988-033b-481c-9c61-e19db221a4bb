import React from 'react';

type IconGearProps = {
  color?: string;
  size?: number;
};

export const IconGear: React.FC<IconGearProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8697_1542)">
        <path
          d="M17.4967 7.20625C17.5967 7.47813 17.5123 7.78125 17.2967 7.975L15.9436 9.20625C15.9779 9.46563 15.9967 9.73125 15.9967 10C15.9967 10.2688 15.9779 10.5344 15.9436 10.7937L17.2967 12.025C17.5123 12.2188 17.5967 12.5219 17.4967 12.7937C17.3592 13.1656 17.1936 13.5219 17.0029 13.8656L16.8561 14.1187C16.6498 14.4625 16.4186 14.7875 16.1654 15.0938C15.9811 15.3188 15.6748 15.3937 15.3998 15.3062L13.6592 14.7531C13.2404 15.075 12.7779 15.3438 12.2842 15.5469L11.8936 17.3313C11.8311 17.6156 11.6123 17.8406 11.3248 17.8875C10.8936 17.9594 10.4498 17.9969 9.99669 17.9969C9.54356 17.9969 9.09981 17.9594 8.66856 17.8875C8.38106 17.8406 8.16231 17.6156 8.09981 17.3313L7.70919 15.5469C7.21544 15.3438 6.75294 15.075 6.33419 14.7531L4.59669 15.3094C4.32169 15.3969 4.01544 15.3188 3.83106 15.0969C3.57794 14.7906 3.34669 14.4656 3.14044 14.1219L2.99356 13.8687C2.80294 13.525 2.63731 13.1687 2.49981 12.7969C2.39981 12.525 2.48419 12.2219 2.69981 12.0281L4.05294 10.7969C4.01856 10.5344 3.99981 10.2688 3.99981 10C3.99981 9.73125 4.01856 9.46563 4.05294 9.20625L2.69981 7.975C2.48419 7.78125 2.39981 7.47813 2.49981 7.20625C2.63731 6.83438 2.80294 6.47813 2.99356 6.13438L3.14044 5.88125C3.34669 5.5375 3.57794 5.2125 3.83106 4.90625C4.01544 4.68125 4.32169 4.60625 4.59669 4.69375L6.33731 5.24688C6.75606 4.925 7.21856 4.65625 7.71231 4.45312L8.10294 2.66875C8.16544 2.38437 8.38419 2.15937 8.67169 2.1125C9.10294 2.0375 9.54669 2 9.99981 2C10.4529 2 10.8967 2.0375 11.3279 2.10938C11.6154 2.15625 11.8342 2.38125 11.8967 2.66562L12.2873 4.45C12.7811 4.65313 13.2436 4.92188 13.6623 5.24375L15.4029 4.69062C15.6779 4.60312 15.9842 4.68125 16.1686 4.90313C16.4217 5.20938 16.6529 5.53437 16.8592 5.87812L17.0061 6.13125C17.1967 6.475 17.3623 6.83125 17.4998 7.20312L17.4967 7.20625ZM9.99981 12.5C10.6629 12.5 11.2987 12.2366 11.7676 11.7678C12.2364 11.2989 12.4998 10.663 12.4998 10C12.4998 9.33696 12.2364 8.70107 11.7676 8.23223C11.2987 7.76339 10.6629 7.5 9.99981 7.5C9.33677 7.5 8.70089 7.76339 8.23205 8.23223C7.7632 8.70107 7.49981 9.33696 7.49981 10C7.49981 10.663 7.7632 11.2989 8.23205 11.7678C8.70089 12.2366 9.33677 12.5 9.99981 12.5Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8697_1542">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(2 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
