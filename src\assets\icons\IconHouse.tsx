import React from 'react';

type IconHouseProps = {
  color?: string;
  size?: number;
};

export const IconHouse: React.FC<IconHouseProps> = ({
  color = '#B1B1B1',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.1336 8.49083L10.8836 2.24083C10.6492 2.00658 10.3314 1.875 10 1.875C9.66863 1.875 9.35081 2.00658 9.11641 2.24083L2.86641 8.49083C2.74978 8.6066 2.65732 8.7444 2.59442 8.89623C2.53151 9.04805 2.49942 9.21086 2.50001 9.3752V16.8752C2.50001 17.041 2.56586 17.1999 2.68307 17.3171C2.80028 17.4344 2.95925 17.5002 3.12501 17.5002H8.12501C8.29077 17.5002 8.44974 17.4344 8.56695 17.3171C8.68416 17.1999 8.75001 17.041 8.75001 16.8752V12.5002H11.25V16.8752C11.25 17.041 11.3159 17.1999 11.4331 17.3171C11.5503 17.4344 11.7092 17.5002 11.875 17.5002H16.875C17.0408 17.5002 17.1997 17.4344 17.317 17.3171C17.4342 17.1999 17.5 17.041 17.5 16.8752V9.3752C17.5006 9.21086 17.4685 9.04805 17.4056 8.89623C17.3427 8.7444 17.2502 8.6066 17.1336 8.49083ZM16.25 16.2502H12.5V11.8752C12.5 11.7094 12.4342 11.5505 12.3169 11.4333C12.1997 11.316 12.0408 11.2502 11.875 11.2502H8.12501C7.95925 11.2502 7.80028 11.316 7.68307 11.4333C7.56586 11.5505 7.50001 11.7094 7.50001 11.8752V16.2502H3.75001V9.3752L10 3.1252L16.25 9.3752V16.2502Z"
        fill={color}
      />
    </svg>
  );
};
