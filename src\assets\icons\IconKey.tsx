import React from 'react';

type IconKeyProps = {
  color?: string;
  size?: number;
};

export const IconKey: React.FC<IconKeyProps> = ({
  color = '#F7941E',
  size = 28,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8492_6066)">
        <path
          d="M13.8505 14.2363L9.53234 18.55L11.5536 20.6063C11.9998 21.0175 11.9998 21.6826 11.5536 22.0544C11.1817 22.5007 10.5167 22.5007 10.1055 22.0544L8.04922 20.0332L6.73409 21.35L8.75359 23.4063C9.19984 23.8176 9.19984 24.4825 8.75359 24.8544C8.38172 25.3007 7.71672 25.3007 7.30678 24.8544L4.50674 22.0544C4.09671 21.6826 4.09671 21.0175 4.50674 20.6063L12.363 12.7488C11.6323 11.7207 11.1992 10.4607 11.1992 9.10005C11.1992 5.62061 14.0211 2.80005 17.4992 2.80005C20.9773 2.80005 23.7992 5.62061 23.7992 9.10005C23.7992 12.5782 20.9773 15.4 17.4992 15.4C16.1386 15.4 14.8786 14.9669 13.8505 14.2363ZM17.4992 13.3C19.818 13.3 21.6992 11.4188 21.6992 9.10005C21.6992 6.78042 19.818 4.90005 17.4992 4.90005C15.1411 4.90005 13.2992 6.78042 13.2992 9.10005C13.2992 11.4188 15.1411 13.3 17.4992 13.3Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8492_6066">
          <rect
            width="19.6"
            height="22.4"
            fill="white"
            transform="translate(4.19922 2.80005)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
