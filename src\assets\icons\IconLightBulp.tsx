import React from 'react';

type IconLightBulpProps = {
  color?: string;
  size?: number;
};

export const IconLightBulp: React.FC<IconLightBulpProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.7501 18.125C13.7501 18.2908 13.6843 18.4498 13.567 18.567C13.4498 18.6842 13.2909 18.75 13.1251 18.75H6.8751C6.70934 18.75 6.55037 18.6842 6.43316 18.567C6.31595 18.4498 6.2501 18.2908 6.2501 18.125C6.2501 17.9593 6.31595 17.8003 6.43316 17.6831C6.55037 17.5659 6.70934 17.5 6.8751 17.5H13.1251C13.2909 17.5 13.4498 17.5659 13.567 17.6831C13.6843 17.8003 13.7501 17.9593 13.7501 18.125ZM16.8751 8.12504C16.8778 9.16695 16.6424 10.1957 16.1869 11.1328C15.7315 12.0699 15.0679 12.8905 14.247 13.5321C14.0935 13.6497 13.9689 13.8009 13.8828 13.9741C13.7967 14.1473 13.7513 14.3379 13.7501 14.5313V15C13.7501 15.3316 13.6184 15.6495 13.384 15.8839C13.1496 16.1183 12.8316 16.25 12.5001 16.25H7.5001C7.16858 16.25 6.85064 16.1183 6.61622 15.8839C6.3818 15.6495 6.2501 15.3316 6.2501 15V14.5313C6.24997 14.3402 6.20603 14.1517 6.12166 13.9802C6.03728 13.8088 5.91472 13.6589 5.76338 13.5422C4.94458 12.9045 4.28155 12.089 3.82447 11.1572C3.3674 10.2254 3.12824 9.20193 3.1251 8.1641C3.10479 4.43989 6.11416 1.33989 9.83448 1.25004C10.7512 1.22795 11.663 1.38946 12.5163 1.72506C13.3696 2.06065 14.1472 2.56356 14.8033 3.20418C15.4593 3.84479 15.9806 4.61017 16.3364 5.45527C16.6922 6.30036 16.8754 7.2081 16.8751 8.12504ZM15.6251 8.12504C15.6253 7.37478 15.4754 6.63205 15.1843 5.94058C14.8932 5.24911 14.4666 4.62287 13.9298 4.09872C13.393 3.57458 12.7568 3.16312 12.0585 2.88856C11.3603 2.61401 10.6142 2.48191 9.86417 2.50004C6.81729 2.57192 4.3587 5.10864 4.3751 8.15551C4.3779 9.00442 4.57377 9.84156 4.94788 10.6036C5.32199 11.3656 5.86454 12.0326 6.53448 12.5539C6.8356 12.788 7.07918 13.0879 7.24655 13.4307C7.41392 13.7734 7.50065 14.1499 7.5001 14.5313V15H9.3751V11.5086L7.05791 9.19223C6.94064 9.07495 6.87475 8.91589 6.87475 8.75004C6.87475 8.58419 6.94064 8.42513 7.05791 8.30785C7.17519 8.19058 7.33425 8.12469 7.5001 8.12469C7.66595 8.12469 7.82501 8.19058 7.94229 8.30785L10.0001 10.3664L12.0579 8.30785C12.116 8.24979 12.1849 8.20372 12.2608 8.1723C12.3367 8.14087 12.418 8.12469 12.5001 8.12469C12.5822 8.12469 12.6635 8.14087 12.7394 8.1723C12.8153 8.20372 12.8842 8.24979 12.9423 8.30785C13.0004 8.36592 13.0464 8.43486 13.0778 8.51073C13.1093 8.5866 13.1255 8.66792 13.1255 8.75004C13.1255 8.83216 13.1093 8.91348 13.0778 8.98935C13.0464 9.06522 13.0004 9.13416 12.9423 9.19223L10.6251 11.5086V15H12.5001V14.5313C12.5008 14.1488 12.5889 13.7714 12.7578 13.4282C12.9266 13.0849 13.1717 12.7848 13.4743 12.5508C14.1463 12.0257 14.6894 11.354 15.0621 10.5869C15.4349 9.81991 15.6274 8.97785 15.6251 8.12504Z"
        fill={color}
      />
    </svg>
  );
};
