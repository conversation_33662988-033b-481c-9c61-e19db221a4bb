import React from 'react';

type IconLockProps = {
  color?: string;
  size?: number;
};

export const IconLock: React.FC<IconLockProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8646_5629)">
        <path
          d="M5.5 8V6.5C5.5 4.01469 7.51562 2 10 2C12.4844 2 14.5 4.01469 14.5 6.5V8H15C16.1031 8 17 8.89687 17 10V16C17 17.1031 16.1031 18 15 18H5C3.89531 18 3 17.1031 3 16V10C3 8.89687 3.89531 8 5 8H5.5ZM7.5 8H12.5V6.5C12.5 5.11937 11.3813 4 10 4C8.61875 4 7.5 5.11937 7.5 6.5V8Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8646_5629">
          <rect
            width="14"
            height="16"
            fill="white"
            transform="translate(3 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
