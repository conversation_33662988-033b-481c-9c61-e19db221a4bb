import * as React from "react";
import { SVGProps } from "react";
const IconMaterialVideo = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_9630_65998)">
      <g clipPath="url(#clip1_9630_65998)">
        <path
          d="M9.5999 3.20001C7.8349 3.20001 6.3999 4.63501 6.3999 6.40001V25.6C6.3999 27.365 7.8349 28.8 9.5999 28.8H22.3999C24.1649 28.8 25.5999 27.365 25.5999 25.6V11.2H19.1999C18.3149 11.2 17.5999 10.485 17.5999 9.60001V3.20001H9.5999ZM19.1999 3.20001V9.60001H25.5999L19.1999 3.20001ZM9.5999 17.6C9.5999 16.715 10.3149 16 11.1999 16H15.9999C16.8849 16 17.5999 16.715 17.5999 17.6V22.4C17.5999 23.285 16.8849 24 15.9999 24H11.1999C10.3149 24 9.5999 23.285 9.5999 22.4V17.6ZM21.4449 23.095L19.1999 21.6V18.4L21.4449 16.905C21.5449 16.84 21.6649 16.8 21.7849 16.8C22.1249 16.8 22.3999 17.075 22.3999 17.415V22.585C22.3999 22.925 22.1249 23.2 21.7849 23.2C21.6649 23.2 21.5449 23.165 21.4449 23.095Z"
          fill="#8C5B86"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_9630_65998">
        <rect width={32} height={32} rx={2} fill="white" />
      </clipPath>
      <clipPath id="clip1_9630_65998">
        <rect
          width={19.2}
          height={25.6}
          fill="white"
          transform="translate(6.3999 3.20001)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default IconMaterialVideo;
