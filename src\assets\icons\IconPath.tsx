import React from 'react';

type IconPathProps = {
  color?: string;
  size?: number;
};

export const IconPath: React.FC<IconPathProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.625 13.125C15.0707 13.1255 14.5323 13.3099 14.0941 13.6492C13.6558 13.9885 13.3425 14.4635 13.2031 15H5.625C4.96196 15 4.32607 14.7366 3.85723 14.2678C3.38839 13.7989 3.125 13.163 3.125 12.5C3.125 11.837 3.38839 11.2011 3.85723 10.7322C4.32607 10.2634 4.96196 10 5.625 10H13.125C13.9538 10 14.7487 9.67076 15.3347 9.08471C15.9208 8.49866 16.25 7.7038 16.25 6.875C16.25 6.0462 15.9208 5.25134 15.3347 4.66529C14.7487 4.07924 13.9538 3.75 13.125 3.75H5.625C5.45924 3.75 5.30027 3.81585 5.18306 3.93306C5.06585 4.05027 5 4.20924 5 4.375C5 4.54076 5.06585 4.69973 5.18306 4.81694C5.30027 4.93415 5.45924 5 5.625 5H13.125C13.6223 5 14.0992 5.19754 14.4508 5.54917C14.8025 5.90081 15 6.37772 15 6.875C15 7.37228 14.8025 7.84919 14.4508 8.20083C14.0992 8.55246 13.6223 8.75 13.125 8.75H5.625C4.63044 8.75 3.67661 9.14509 2.97335 9.84835C2.27009 10.5516 1.875 11.5054 1.875 12.5C1.875 13.4946 2.27009 14.4484 2.97335 15.1517C3.67661 15.8549 4.63044 16.25 5.625 16.25H13.2031C13.32 16.7027 13.5615 17.1135 13.9001 17.4359C14.2388 17.7582 14.661 17.9791 15.1189 18.0735C15.5768 18.1679 16.052 18.132 16.4906 17.9699C16.9291 17.8078 17.3134 17.526 17.5998 17.1564C17.8862 16.7869 18.0632 16.3444 18.1108 15.8793C18.1583 15.4142 18.0745 14.9451 17.8689 14.5252C17.6632 14.1054 17.3439 13.7516 16.9473 13.5041C16.5506 13.2566 16.0925 13.1252 15.625 13.125ZM15.625 16.875C15.3778 16.875 15.1361 16.8017 14.9305 16.6643C14.725 16.527 14.5648 16.3318 14.4701 16.1034C14.3755 15.8749 14.3508 15.6236 14.399 15.3811C14.4472 15.1387 14.5663 14.9159 14.7411 14.7411C14.9159 14.5663 15.1387 14.4472 15.3811 14.399C15.6236 14.3508 15.8749 14.3755 16.1034 14.4701C16.3318 14.5648 16.527 14.725 16.6643 14.9305C16.8017 15.1361 16.875 15.3778 16.875 15.625C16.875 15.9565 16.7433 16.2745 16.5089 16.5089C16.2745 16.7433 15.9565 16.875 15.625 16.875Z"
        fill={color}
      />
    </svg>
  );
};
