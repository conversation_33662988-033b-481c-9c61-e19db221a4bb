import React from 'react';

type IconPenProps = {
  color?: string;
  size?: number;
};

export const IconPen: React.FC<IconPenProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8646_3969)">
        <path
          d="M14.2094 2.60347C14.9906 1.82228 16.2594 1.82228 17.0406 2.60347L18.2719 3.83566C19.0531 4.61659 19.0531 5.88409 18.2719 6.66534L16.7594 8.17784L12.6969 4.11597L14.2094 2.60347ZM16.0531 8.88409L8.76562 16.1685C8.44062 16.4935 8.0375 16.7341 7.59687 16.8622L3.83656 17.9685C3.57343 18.0466 3.28874 17.9747 3.09468 17.7528C2.90061 17.5872 2.82806 17.3028 2.90549 17.0372L4.01156 13.2778C4.14156 12.8372 4.37999 12.4341 4.70531 12.1091L11.9906 4.82284L16.0531 8.88409Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8646_3969">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(2.875 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
