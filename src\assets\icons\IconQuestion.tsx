import React from 'react';

type IconQuestionProps = {
  color?: string;
  size?: number;
};

export const IconQuestion: React.FC<IconQuestionProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8646_5639)">
        <path
          d="M10 2C5.58125 2 2 5.58125 2 10C2 14.4187 5.58125 18 10 18C14.4187 18 18 14.4187 18 10C18 5.58125 14.4187 2 10 2ZM10 14.5C9.4375 14.5 9 14.0625 9 13.5C9 12.9375 9.40938 12.5 10 12.5C10.5344 12.5 11 12.9375 11 13.5C11 14.0625 10.5344 14.5 10 14.5ZM12.1594 10.0625L10.75 10.9375V11C10.75 11.4062 10.4062 11.75 10 11.75C9.59375 11.75 9.25 11.4062 9.25 11V10.5C9.25 10.25 9.375 10 9.625 9.84375L11.4062 8.78125C11.625 8.65625 11.75 8.4375 11.75 8.1875C11.75 7.8125 11.4094 7.5 11.0344 7.5H9.4375C9.03438 7.5 8.75 7.8125 8.75 8.1875C8.75 8.59375 8.40625 8.9375 8 8.9375C7.59375 8.9375 7.25 8.59375 7.25 8.1875C7.25 6.96875 8.21875 6 9.40938 6H11.0063C12.2812 6 13.25 6.96875 13.25 8.1875C13.25 8.9375 12.8438 9.65625 12.1594 10.0625Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8646_5639">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(2 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
