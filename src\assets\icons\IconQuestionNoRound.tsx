import React from 'react';

type IconQuestionNoRoundProps = {
  color?: string;
};

export const IconQuestionNoRound: React.FC<IconQuestionNoRoundProps> = ({
  color = '#F7941E',
}) => {
  return (
    <svg
      width="14"
      height="21"
      viewBox="0 0 14 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 6.5C14 9.61828 11.3236 12.1995 7.875 12.5769V13.5C7.875 13.7321 7.78281 13.9546 7.61872 14.1187C7.45462 14.2828 7.23206 14.375 7 14.375C6.76794 14.375 6.54538 14.2828 6.38128 14.1187C6.21719 13.9546 6.125 13.7321 6.125 13.5V11.75C6.125 11.5179 6.21719 11.2954 6.38128 11.1313C6.54538 10.9672 6.76794 10.875 7 10.875C9.89516 10.875 12.25 8.91281 12.25 6.5C12.25 4.08719 9.89516 2.125 7 2.125C4.10484 2.125 1.75 4.08719 1.75 6.5C1.75 6.73206 1.65781 6.95462 1.49372 7.11872C1.32962 7.28281 1.10706 7.375 0.875 7.375C0.642936 7.375 0.420376 7.28281 0.256282 7.11872C0.0921874 6.95462 0 6.73206 0 6.5C0 3.1225 3.14016 0.375 7 0.375C10.8598 0.375 14 3.1225 14 6.5ZM7 17C6.65388 17 6.31554 17.1026 6.02775 17.2949C5.73997 17.4872 5.51566 17.7605 5.38321 18.0803C5.25076 18.4001 5.2161 18.7519 5.28363 19.0914C5.35115 19.4309 5.51782 19.7427 5.76256 19.9874C6.00731 20.2322 6.31913 20.3988 6.65859 20.4664C6.99806 20.5339 7.34993 20.4992 7.6697 20.3668C7.98947 20.2343 8.26278 20.01 8.45507 19.7222C8.64736 19.4345 8.75 19.0961 8.75 18.75C8.75 18.2859 8.56563 17.8408 8.23744 17.5126C7.90925 17.1844 7.46413 17 7 17Z"
        fill={color}
      />
    </svg>
  );
};
