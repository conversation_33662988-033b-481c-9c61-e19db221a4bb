import React from 'react';

type IconRankingProps = {
  color?: string;
  size?: number;
};

export const IconRanking: React.FC<IconRankingProps> = ({
  color = '#808080',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.78203 8.01016C8.75607 7.93229 8.7457 7.85007 8.75152 7.76819C8.75733 7.68631 8.77922 7.60638 8.81593 7.53296C8.85264 7.45955 8.90345 7.39408 8.96546 7.3403C9.02747 7.28652 9.09947 7.24548 9.17734 7.21953L10.1148 6.90703C10.2088 6.87571 10.3089 6.86717 10.4068 6.8821C10.5047 6.89704 10.5976 6.93503 10.678 6.99294C10.7583 7.05085 10.8237 7.12702 10.8689 7.21518C10.914 7.30334 10.9375 7.40096 10.9375 7.5V10.625C10.9375 10.7908 10.8717 10.9497 10.7544 11.0669C10.6372 11.1842 10.4783 11.25 10.3125 11.25C10.1467 11.25 9.98777 11.1842 9.87056 11.0669C9.75335 10.9497 9.6875 10.7908 9.6875 10.625V8.36719L9.57266 8.40547C9.49479 8.43143 9.41257 8.4418 9.33069 8.43598C9.24881 8.43017 9.16888 8.40828 9.09546 8.37157C9.02205 8.33486 8.95658 8.28405 8.9028 8.22204C8.84902 8.16003 8.80798 8.08803 8.78203 8.01016ZM19.375 16.25C19.375 16.4158 19.3092 16.5747 19.1919 16.6919C19.0747 16.8092 18.9158 16.875 18.75 16.875H1.25C1.08424 16.875 0.925268 16.8092 0.808058 16.6919C0.690848 16.5747 0.625 16.4158 0.625 16.25C0.625 16.0842 0.690848 15.9253 0.808058 15.8081C0.925268 15.6908 1.08424 15.625 1.25 15.625H1.875V8.125C1.875 7.79348 2.0067 7.47554 2.24112 7.24112C2.47554 7.0067 2.79348 6.875 3.125 6.875H6.25V4.375C6.25 4.04348 6.3817 3.72554 6.61612 3.49112C6.85054 3.2567 7.16848 3.125 7.5 3.125H12.5C12.8315 3.125 13.1495 3.2567 13.3839 3.49112C13.6183 3.72554 13.75 4.04348 13.75 4.375V10H16.875C17.2065 10 17.5245 10.1317 17.7589 10.3661C17.9933 10.6005 18.125 10.9185 18.125 11.25V15.625H18.75C18.9158 15.625 19.0747 15.6908 19.1919 15.8081C19.3092 15.9253 19.375 16.0842 19.375 16.25ZM13.75 11.25V15.625H16.875V11.25H13.75ZM7.5 15.625H12.5V4.375H7.5V15.625ZM3.125 15.625H6.25V8.125H3.125V15.625Z"
        fill={color}
      />
    </svg>
  );
};
