import React from 'react';

type IconSearchBoxProps = {
  color?: string;
  size?: number;
};

export const IconSearchBox: React.FC<IconSearchBoxProps> = ({
  color = '#222222',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.2021 12.3746L9.85212 9.02461C10.5771 8.13711 10.9771 7.01961 10.9771 5.79961C10.9771 2.92711 8.64887 0.599609 5.77712 0.599609C2.90537 0.599609 0.599609 2.92786 0.599609 5.79961C0.599609 8.67136 2.92762 10.9996 5.77712 10.9996C6.99687 10.9996 8.11587 10.5769 9.00212 9.87361L12.3521 13.2236C12.4921 13.3421 12.6471 13.3996 12.7996 13.3996C12.9521 13.3996 13.1066 13.341 13.2239 13.2238C13.4571 12.9896 13.4571 12.6096 13.2021 12.3746ZM1.79962 5.79961C1.79962 3.59411 3.59412 1.79961 5.79962 1.79961C8.00512 1.79961 9.79962 3.59411 9.79962 5.79961C9.79962 8.00511 8.00512 9.79961 5.79962 9.79961C3.59412 9.79961 1.79962 8.00461 1.79962 5.79961Z"
        fill={color}
      />
    </svg>
  );
};
