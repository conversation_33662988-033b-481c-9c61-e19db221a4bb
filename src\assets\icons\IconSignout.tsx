import React from 'react';

type IconSignoutProps = {
  color?: string;
  size?: number;
  rotate?: number;
};

export const IconSignout: React.FC<IconSignoutProps> = ({
  color = '#D3483E',
  size = 20,
  rotate = 0,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: `rotate(${rotate}deg)` }}
    >
      <path
        d="M9.5625 15.587C9.5625 15.7362 9.62176 15.8793 9.72725 15.9848C9.83274 16.0903 9.97582 16.1495 10.125 16.1495H14.625C14.7742 16.1495 14.9173 16.0903 15.0227 15.9848C15.1282 15.8793 15.1875 15.7362 15.1875 15.587V3.21204C15.1875 3.06285 15.1282 2.91978 15.0227 2.81429C14.9173 2.7088 14.7742 2.64954 14.625 2.64954H10.125C9.97582 2.64954 9.83274 2.7088 9.72725 2.81429C9.62176 2.91978 9.5625 3.06285 9.5625 3.21204C9.5625 3.36122 9.62176 3.50429 9.72725 3.60978C9.83274 3.71527 9.97582 3.77454 10.125 3.77454H14.0625V15.0245H10.125C9.97582 15.0245 9.83274 15.0838 9.72725 15.1893C9.62176 15.2948 9.5625 15.4379 9.5625 15.587ZM1.85203 9.00157L4.66453 6.18907C4.7432 6.11031 4.84347 6.05667 4.95264 6.03493C5.06181 6.01319 5.17498 6.02433 5.27782 6.06694C5.38066 6.10955 5.46854 6.18171 5.53034 6.2743C5.59214 6.36688 5.62509 6.47572 5.625 6.58704V8.83704H10.125C10.2742 8.83704 10.4173 8.8963 10.5227 9.00179C10.6282 9.10728 10.6875 9.25035 10.6875 9.39954C10.6875 9.54872 10.6282 9.69179 10.5227 9.79728C10.4173 9.90277 10.2742 9.96204 10.125 9.96204H5.625V12.212C5.62509 12.3234 5.59214 12.4322 5.53034 12.5248C5.46854 12.6174 5.38066 12.6895 5.27782 12.7321C5.17498 12.7747 5.06181 12.7859 4.95264 12.7641C4.84347 12.7424 4.7432 12.6888 4.66453 12.61L1.85203 9.79751C1.79973 9.74526 1.75824 9.68323 1.72993 9.61494C1.70163 9.54665 1.68706 9.47346 1.68706 9.39954C1.68706 9.32561 1.70163 9.25242 1.72993 9.18413C1.75824 9.11585 1.79973 9.05381 1.85203 9.00157Z"
        fill={color}
      />
    </svg>
  );
};
