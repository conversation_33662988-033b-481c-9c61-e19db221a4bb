import React from 'react';

type IconSliderProps = {
  color?: string;
  size?: number;
};

export const IconSlider: React.FC<IconSliderProps> = ({
  color = '#3C3C3C',
  size = 14,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8837_18590)">
        <path
          d="M2.59998 10.5992C2.3878 10.5992 2.18432 10.5149 2.03429 10.3649C1.88426 10.2149 1.79998 10.0114 1.79998 9.79922C1.79998 9.58704 1.88426 9.38356 2.03429 9.23353C2.18432 9.0835 2.3878 8.99922 2.59998 8.99922C2.81215 8.99922 3.01563 9.0835 3.16566 9.23353C3.31569 9.38356 3.39998 9.58704 3.39998 9.79922C3.39998 10.0114 3.31569 10.2149 3.16566 10.3649C3.01563 10.5149 2.81215 10.5992 2.59998 10.5992ZM4.50748 9.19922C4.25248 8.38672 3.49498 7.79922 2.59998 7.79922C1.49498 7.79922 0.599976 8.69422 0.599976 9.79922C0.599976 10.9042 1.49498 11.7992 2.59998 11.7992C3.49498 11.7992 4.25248 11.2117 4.50748 10.3992H12.8C13.1325 10.3992 13.4 10.1317 13.4 9.79922C13.4 9.46672 13.1325 9.19922 12.8 9.19922H4.50748ZM12.2 4.19922C12.2 4.41139 12.1157 4.61487 11.9657 4.7649C11.8156 4.91493 11.6121 4.99922 11.4 4.99922C11.1878 4.99922 10.9843 4.91493 10.8343 4.7649C10.6843 4.61487 10.6 4.41139 10.6 4.19922C10.6 3.98705 10.6843 3.78356 10.8343 3.63353C10.9843 3.4835 11.1878 3.39922 11.4 3.39922C11.6121 3.39922 11.8156 3.4835 11.9657 3.63353C12.1157 3.78356 12.2 3.98705 12.2 4.19922ZM9.49248 3.59922H1.19998C0.867476 3.59922 0.599976 3.86672 0.599976 4.19922C0.599976 4.53172 0.867476 4.79922 1.19998 4.79922H9.49248C9.74748 5.61172 10.505 6.19922 11.4 6.19922C12.505 6.19922 13.4 5.30422 13.4 4.19922C13.4 3.09422 12.505 2.19922 11.4 2.19922C10.505 2.19922 9.74748 2.78672 9.49248 3.59922Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8837_18590">
          <rect
            width="12.8"
            height="12.8"
            fill="white"
            transform="translate(0.599976 0.599609)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
