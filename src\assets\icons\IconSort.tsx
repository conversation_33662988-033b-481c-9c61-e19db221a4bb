import React from 'react';

type IconSortProps = {
  color?: string;
  size?: number;
};

export const IconSort: React.FC<IconSortProps> = ({
  color = '#3C3C3C',
  size = 14,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 12 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8837_18597)">
        <path
          d="M0.400024 2.79922C0.400024 2.46797 0.668774 2.19922 1.00002 2.19922H11C11.3325 2.19922 11.6 2.46797 11.6 2.79922C11.6 3.13172 11.3325 3.39922 11 3.39922H1.00002C0.668774 3.39922 0.400024 3.13172 0.400024 2.79922ZM0.400024 6.79922C0.400024 6.46672 0.668774 6.19922 1.00002 6.19922H7.80002C8.13252 6.19922 8.40003 6.46672 8.40003 6.79922C8.40003 7.13172 8.13252 7.39922 7.80002 7.39922H1.00002C0.668774 7.39922 0.400024 7.13172 0.400024 6.79922ZM4.60002 11.3992H1.00002C0.668774 11.3992 0.400024 11.1317 0.400024 10.7992C0.400024 10.4667 0.668774 10.1992 1.00002 10.1992H4.60002C4.93252 10.1992 5.20002 10.4667 5.20002 10.7992C5.20002 11.1317 4.93252 11.3992 4.60002 11.3992Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8837_18597">
          <rect
            width="11.2"
            height="12.8"
            fill="white"
            transform="translate(0.400024 0.599609)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
