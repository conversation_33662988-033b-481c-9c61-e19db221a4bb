import React from 'react';

type IconTextAaProps = {
  color?: string;
  size?: number;
};

export const IconTextAa: React.FC<IconTextAaProps> = ({
  color = '#B1B1B1',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.81562 4.10857C6.76515 4.0012 6.68516 3.91041 6.58499 3.84681C6.48483 3.78322 6.36864 3.74945 6.25 3.74945C6.13135 3.74945 6.01516 3.78322 5.915 3.84681C5.81484 3.91041 5.73484 4.0012 5.68437 4.10857L0.684371 14.7336C0.649437 14.8078 0.629476 14.8883 0.625627 14.9703C0.621777 15.0523 0.634116 15.1342 0.661937 15.2114C0.718125 15.3674 0.833967 15.4947 0.98398 15.5652C1.13399 15.6358 1.30589 15.6438 1.46185 15.5876C1.61781 15.5315 1.74507 15.4156 1.81562 15.2656L3.11718 12.5H9.38281L10.6844 15.2656C10.7193 15.3399 10.7685 15.4066 10.8292 15.4618C10.8899 15.5171 10.9609 15.5598 11.0381 15.5876C11.1154 15.6155 11.1973 15.6278 11.2793 15.624C11.3613 15.6201 11.4417 15.6001 11.516 15.5652C11.5903 15.5303 11.657 15.4811 11.7122 15.4204C11.7675 15.3597 11.8102 15.2887 11.8381 15.2114C11.8659 15.1342 11.8782 15.0523 11.8744 14.9703C11.8705 14.8883 11.8506 14.8078 11.8156 14.7336L6.81562 4.10857ZM3.70546 11.25L6.25 5.84294L8.79453 11.25H3.70546ZM15.625 7.49997C14.6281 7.49997 13.8492 7.77107 13.3102 8.30622C13.1973 8.42388 13.1349 8.58097 13.1362 8.744C13.1375 8.90702 13.2025 9.06307 13.3173 9.17886C13.4321 9.29465 13.5875 9.36101 13.7505 9.36379C13.9136 9.36657 14.0712 9.30554 14.1898 9.19372C14.4867 8.89919 14.9711 8.74997 15.625 8.74997C16.6586 8.74997 17.5 9.4531 17.5 10.3125V10.564C16.9454 10.193 16.2923 9.9965 15.625 9.99997C13.9016 9.99997 12.5 11.2617 12.5 12.8125C12.5 14.3633 13.9016 15.625 15.625 15.625C16.2926 15.6279 16.9457 15.4306 17.5 15.0586C17.5078 15.2243 17.5811 15.3802 17.7038 15.4919C17.8265 15.6036 17.9885 15.662 18.1543 15.6543C18.3201 15.6465 18.4759 15.5732 18.5877 15.4505C18.6994 15.3278 18.7578 15.1657 18.75 15V10.3125C18.75 8.76169 17.3484 7.49997 15.625 7.49997ZM15.625 14.375C14.5914 14.375 13.75 13.6718 13.75 12.8125C13.75 11.9531 14.5914 11.25 15.625 11.25C16.6586 11.25 17.5 11.9531 17.5 12.8125C17.5 13.6718 16.6586 14.375 15.625 14.375Z"
        fill={color}
      />
    </svg>
  );
};
