import React from 'react';

type IconUserProps = {
  color?: string;
  size?: number;
};

export const IconUser: React.FC<IconUserProps> = ({
  color = '#3C3C3C',
  size = 20,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8646_5619)">
        <path
          d="M10 10C12.2094 10 14 8.20906 14 6C14 3.79094 12.2094 2 10 2C7.79063 2 6 3.79094 6 6C6 8.20906 7.79063 10 10 10ZM11.5844 11.5H8.41563C5.42531 11.5 3 13.925 3 16.9156C3 17.5138 3.485 17.9991 4.08312 17.9991H15.9175C16.5156 18 17 17.5156 17 16.9156C17 13.925 14.575 11.5 11.5844 11.5Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_8646_5619">
          <rect
            width="14"
            height="16"
            fill="white"
            transform="translate(3 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
