import React from 'react';

type IconUserRoundProps = {
  color?: string;
  size?: number;
};

export const IconUserRound: React.FC<IconUserRoundProps> = ({
  color = '#717171',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.4329 13.2497C13.481 11.6041 12.0142 10.4241 10.3023 9.86475C11.1491 9.36066 11.8069 8.59255 12.1749 7.67837C12.5429 6.76419 12.6006 5.7545 12.3392 4.80435C12.0778 3.85419 11.5117 3.01612 10.7279 2.41883C9.94408 1.82153 8.98587 1.49805 8.00041 1.49805C7.01496 1.49805 6.05675 1.82153 5.27293 2.41883C4.48911 3.01612 3.92304 3.85419 3.66163 4.80435C3.40022 5.7545 3.45793 6.76419 3.82591 7.67837C4.19388 8.59255 4.85177 9.36066 5.69854 9.86475C3.98666 10.4235 2.51979 11.6035 1.56791 13.2497C1.53301 13.3067 1.50985 13.37 1.49982 13.436C1.48978 13.502 1.49307 13.5694 1.50949 13.6341C1.52591 13.6988 1.55512 13.7596 1.59541 13.8128C1.63569 13.8661 1.68624 13.9107 1.74405 13.9441C1.80187 13.9775 1.86579 13.999 1.93204 14.0073C1.9983 14.0156 2.06554 14.0105 2.1298 13.9924C2.19407 13.9743 2.25405 13.9435 2.30622 13.9018C2.35838 13.8601 2.40168 13.8084 2.43354 13.7497C3.61104 11.7147 5.69229 10.4997 8.00041 10.4997C10.3085 10.4997 12.3898 11.7147 13.5673 13.7497C13.5992 13.8084 13.6424 13.8601 13.6946 13.9018C13.7468 13.9435 13.8068 13.9743 13.871 13.9924C13.9353 14.0105 14.0025 14.0156 14.0688 14.0073C14.135 13.999 14.199 13.9775 14.2568 13.9441C14.3146 13.9107 14.3651 13.8661 14.4054 13.8128C14.4457 13.7596 14.4749 13.6988 14.4913 13.6341C14.5078 13.5694 14.511 13.502 14.501 13.436C14.491 13.37 14.4678 13.3067 14.4329 13.2497ZM4.50041 5.99975C4.50041 5.30751 4.70568 4.63082 5.09027 4.05525C5.47485 3.47968 6.02148 3.03108 6.66102 2.76617C7.30056 2.50126 8.0043 2.43195 8.68323 2.567C9.36216 2.70205 9.9858 3.03539 10.4753 3.52487C10.9648 4.01436 11.2981 4.638 11.4332 5.31693C11.5682 5.99586 11.4989 6.6996 11.234 7.33914C10.9691 7.97868 10.5205 8.52531 9.94491 8.90989C9.36934 9.29448 8.69265 9.49975 8.00041 9.49975C7.07246 9.49875 6.1828 9.12969 5.52664 8.47353C4.87047 7.81736 4.50141 6.9277 4.50041 5.99975Z"
        fill={color}
      />
    </svg>
  );
};
