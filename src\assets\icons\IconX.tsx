import React from 'react';

type IconXProps = {
  color?: string;
  size?: number;
};

export const IconX: React.FC<IconXProps> = ({
  color = '#767676',
  size = 16,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.8025 10.9752C12.0367 11.2094 12.0367 11.5894 11.8025 11.8237C11.5683 12.058 11.1882 12.0579 10.954 11.8237L8 8.8477L5.02499 11.8227C4.79077 12.0569 4.41074 12.0569 4.17649 11.8227C3.94224 11.5885 3.94227 11.2084 4.17649 10.9742L7.1525 8.0002L4.17567 5.00269C3.94144 4.76847 3.94144 4.38844 4.17567 4.15419C4.40989 3.91994 4.78992 3.91997 5.02417 4.15419L8 7.1527L10.975 4.17769C11.2092 3.94347 11.5892 3.94347 11.8235 4.17769C12.0577 4.41192 12.0577 4.79194 11.8235 5.02619L8.8475 8.0002L11.8025 10.9752Z"
        fill={color}
      />
    </svg>
  );
};
