/**
 * Icons Index
 * 
 * This file exports all icon components and SVG assets.
 * It serves as the central hub for all icon-related imports.
 * 
 * Key Concepts:
 * - Centralized icon management
 * - Tree-shakable exports
 * - Consistent icon interface
 * - SVG optimization and reusability
 * 
 * Usage Patterns:
 * - Raw SVGs for simple icons
 * - React components for interactive icons
 * - Icon libraries integration (Lucide, Heroicons)
 * - Custom icon components with variants
 */

// Raw SVG exports (for simple static icons)
// export { default as LogoSvg } from './logo.svg'
// export { default as ArrowSvg } from './arrow.svg'

// React Icon Components (for interactive/styled icons)
// export { default as Logo } from './Logo'
// export { default as Arrow } from './Arrow'
// export { default as Menu } from './Menu'
// export { default as Close } from './Close'

// Icon library re-exports (when using libraries like Lucide)
// export {
//   ChevronDown,
//   ChevronUp,
//   Search,
//   User,
//   Settings,
//   Home,
//   Bell,
//   Mail
// } from 'lucide-react'

/**
 * Icon Component Interface
 * 
 * All icon components should follow this interface for consistency:
 */
export interface IconProps {
  size?: number | string
  color?: string
  className?: string
  'aria-label'?: string
}

/**
 * Default Icon Sizes
 * 
 * Standardized icon sizes for consistent UI:
 */
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 48,
} as const

/**
 * Icon Colors
 * 
 * Semantic color tokens for icons:
 */
export const ICON_COLORS = {
  primary: 'text-blue-600',
  secondary: 'text-gray-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
  muted: 'text-gray-400',
} as const

/**
 * Development Notes:
 * 
 * 1. Icon Organization:
 *    - Raw SVGs: Simple static icons
 *    - Components: Interactive icons with props
 *    - Library icons: Third-party icon sets
 * 
 * 2. SVG Optimization:
 *    - Use SVGO for optimization
 *    - Remove unnecessary attributes
 *    - Ensure proper viewBox settings
 *    - Add aria-labels for accessibility
 * 
 * 3. Icon Components:
 *    - Accept size, color, className props
 *    - Use semantic color tokens
 *    - Include proper TypeScript types
 *    - Support accessibility attributes
 * 
 * 4. Performance:
 *    - Tree-shakable exports
 *    - Lazy loading for large icon sets
 *    - SVG sprites for repeated icons
 *    - Proper caching headers
 * 
 * 5. Accessibility:
 *    - Include aria-labels
 *    - Use semantic HTML
 *    - Ensure proper contrast
 *    - Support screen readers
 * 
 * Example Icon Component:
 * ```tsx
 * import { IconProps } from './index'
 * 
 * export default function SearchIcon({ 
 *   size = ICON_SIZES.md, 
 *   color = ICON_COLORS.secondary,
 *   className = '',
 *   'aria-label': ariaLabel = 'Search'
 * }: IconProps) {
 *   return (
 *     <svg
 *       width={size}
 *       height={size}
 *       viewBox="0 0 24 24"
 *       fill="none"
 *       stroke="currentColor"
 *       className={`${color} ${className}`}
 *       aria-label={ariaLabel}
 *       role="img"
 *     >
 *       <circle cx="11" cy="11" r="8" />
 *       <path d="m21 21-4.35-4.35" />
 *     </svg>
 *   )
 * }
 * ```
 * 
 * Example Usage:
 * ```tsx
 * import { SearchIcon, ICON_SIZES, ICON_COLORS } from '@/assets/icons'
 * 
 * <SearchIcon 
 *   size={ICON_SIZES.lg} 
 *   color={ICON_COLORS.primary}
 *   className="hover:scale-110 transition-transform"
 * />
 * ```
 */

// Temporary placeholder exports until actual icons are added
export const PLACEHOLDER_ICONS = {
  logo: '🍋',
  search: '🔍',
  user: '👤',
  settings: '⚙️',
  home: '🏠',
  menu: '☰',
  close: '✕',
  arrow: '→',
  check: '✓',
  warning: '⚠️',
  error: '❌',
  info: 'ℹ️',
} as const

export type PlaceholderIconName = keyof typeof PLACEHOLDER_ICONS