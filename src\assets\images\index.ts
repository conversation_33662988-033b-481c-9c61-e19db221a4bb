/**
 * Images Index
 * 
 * This file manages all static images and provides optimized image imports.
 * It serves as the central hub for image asset management.
 * 
 * Key Concepts:
 * - Centralized image management
 * - Next.js Image optimization
 * - Responsive image handling
 * - Asset organization and naming
 * 
 * Image Categories:
 * - Logos and branding
 * - UI illustrations
 * - User avatars and profiles
 * - Background images
 * - Product images
 * - Marketing assets
 */

// Static image imports (for Next.js optimization)
// import logoImage from './logo.png'
// import heroImage from './hero.jpg'
// import placeholderAvatar from './placeholder-avatar.png'
// import backgroundPattern from './background-pattern.svg'

/**
 * Image Asset Interface
 * 
 * Standard interface for image objects:
 */
export interface ImageAsset {
  src: string
  alt: string
  width?: number
  height?: number
  blurDataURL?: string // For Next.js blur placeholder
}

/**
 * Image Collections
 * 
 * Organized image collections for different use cases:
 */
export const IMAGES = {
  // Logos and Branding
  logos: {
    // main: {
    //   src: logoImage,
    //   alt: 'Lemon App Logo',
    //   width: 200,
    //   height: 60
    // },
    // icon: {
    //   src: logoIcon,
    //   alt: 'Lemon App Icon',
    //   width: 64,
    //   height: 64
    // }
  },
  
  // UI Illustrations
  illustrations: {
    // hero: {
    //   src: heroImage,
    //   alt: 'Hero illustration',
    //   width: 800,
    //   height: 600
    // },
    // emptyState: {
    //   src: emptyStateImage,
    //   alt: 'No data available',
    //   width: 400,
    //   height: 300
    // }
  },
  
  // Placeholders
  placeholders: {
    // avatar: {
    //   src: placeholderAvatar,
    //   alt: 'Default user avatar',
    //   width: 100,
    //   height: 100
    // },
    // image: {
    //   src: placeholderImage,
    //   alt: 'Placeholder image',
    //   width: 400,
    //   height: 300
    // }
  },
  
  // Backgrounds
  backgrounds: {
    // pattern: {
    //   src: backgroundPattern,
    //   alt: 'Background pattern',
    //   width: 1920,
    //   height: 1080
    // }
  }
} as const

/**
 * Image Sizes
 * 
 * Standardized image sizes for consistent UI:
 */
export const IMAGE_SIZES = {
  avatar: {
    xs: { width: 24, height: 24 },
    sm: { width: 32, height: 32 },
    md: { width: 48, height: 48 },
    lg: { width: 64, height: 64 },
    xl: { width: 96, height: 96 },
  },
  thumbnail: {
    sm: { width: 120, height: 90 },
    md: { width: 200, height: 150 },
    lg: { width: 300, height: 225 },
  },
  card: {
    sm: { width: 280, height: 160 },
    md: { width: 400, height: 225 },
    lg: { width: 600, height: 338 },
  },
  hero: {
    sm: { width: 640, height: 360 },
    md: { width: 1024, height: 576 },
    lg: { width: 1920, height: 1080 },
  }
} as const

/**
 * Image Utilities
 * 
 * Helper functions for image handling:
 */

/**
 * Generate blur data URL for Next.js Image placeholder
 */
export function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = typeof window !== 'undefined' ? document.createElement('canvas') : null
  if (!canvas) return ''
  
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''
  
  // Create a simple gradient blur placeholder
  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#f3f4f6')
  gradient.addColorStop(1, '#e5e7eb')
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  return canvas.toDataURL()
}

/**
 * Get responsive image sizes string for Next.js Image
 */
export function getResponsiveSizes(breakpoints: Record<string, string>): string {
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => `(${breakpoint}) ${size}`)
    .join(', ')
}

/**
 * Common responsive sizes configurations
 */
export const RESPONSIVE_SIZES = {
  fullWidth: '100vw',
  halfWidth: '(max-width: 768px) 100vw, 50vw',
  thirdWidth: '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
  avatar: '(max-width: 768px) 48px, 64px',
  thumbnail: '(max-width: 768px) 120px, 200px',
  card: '(max-width: 768px) 280px, 400px',
} as const

/**
 * Development Notes:
 * 
 * 1. Image Optimization:
 *    - Use Next.js Image component for automatic optimization
 *    - Provide width and height for layout stability
 *    - Use appropriate formats (WebP, AVIF when supported)
 *    - Implement lazy loading for below-fold images
 * 
 * 2. Image Organization:
 *    - Group images by category and usage
 *    - Use descriptive file names
 *    - Maintain consistent naming conventions
 *    - Keep source files organized in subfolders
 * 
 * 3. Performance:
 *    - Optimize images before importing
 *    - Use appropriate compression levels
 *    - Implement blur placeholders
 *    - Consider CDN for large image sets
 * 
 * 4. Responsive Images:
 *    - Provide multiple sizes for different breakpoints
 *    - Use srcSet for high-DPI displays
 *    - Implement art direction when needed
 *    - Consider container queries for component-based sizing
 * 
 * 5. Accessibility:
 *    - Always provide meaningful alt text
 *    - Use empty alt for decorative images
 *    - Ensure proper contrast for text overlays
 *    - Support reduced motion preferences
 * 
 * Example Usage:
 * ```tsx
 * import Image from 'next/image'
 * import { IMAGES, IMAGE_SIZES, RESPONSIVE_SIZES } from '@/assets/images'
 * 
 * // Using predefined image
 * <Image
 *   src={IMAGES.logos.main.src}
 *   alt={IMAGES.logos.main.alt}
 *   width={IMAGES.logos.main.width}
 *   height={IMAGES.logos.main.height}
 *   priority // For above-fold images
 * />
 * 
 * // Using responsive sizing
 * <Image
 *   src="/images/hero.jpg"
 *   alt="Hero image"
 *   fill
 *   sizes={RESPONSIVE_SIZES.fullWidth}
 *   className="object-cover"
 * />
 * 
 * // Using standardized sizes
 * <Image
 *   src={avatarSrc}
 *   alt="User avatar"
 *   width={IMAGE_SIZES.avatar.lg.width}
 *   height={IMAGE_SIZES.avatar.lg.height}
 *   className="rounded-full"
 * />
 * ```
 * 
 * File Organization:
 * ```
 * src/assets/images/
 * ├── logos/
 * │   ├── logo.svg
 * │   ├── logo-dark.svg
 * │   └── favicon.ico
 * ├── illustrations/
 * │   ├── hero.jpg
 * │   ├── empty-state.svg
 * │   └── 404.svg
 * ├── placeholders/
 * │   ├── avatar.png
 * │   ├── image.svg
 * │   └── loading.gif
 * └── backgrounds/
 *     ├── pattern.svg
 *     ├── gradient.jpg
 *     └── texture.png
 * ```
 */

// Temporary placeholder until actual images are added
export const PLACEHOLDER_IMAGES = {
  logo: '/next.svg',
  avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGM0Y0RjYiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQxLjUwNjYgMjAuNTA2NiAzMyAzMSAzM0gzM0M0My40OTM0IDMzIDUyIDQxLjUwNjYgNTIgNTJWNjRIMTJWNTJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=',
  placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjE1MCIgeT0iMTAwIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgcng9IjEwIiBmaWxsPSIjOUNBM0FGIi8+CjxjaXJjbGUgY3g9IjE3NSIgY3k9IjEyNSIgcj0iMTUiIGZpbGw9IiNGM0Y0RjYiLz4KPHBhdGggZD0iTTE2NSAxNjVMMTg1IDE0NUwyMjUgMTg1SDE2NVoiIGZpbGw9IiNGM0Y0RjYiLz4KPC9zdmc+Cg=='
} as const