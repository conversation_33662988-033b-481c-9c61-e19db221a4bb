/**
 * Component Styles
 * 
 * This file contains component-specific styles, variants, and modifiers
 * that extend the base component styles defined in globals.css.
 * 
 * Key Concepts:
 * - Component variants and sizes
 * - State-based styling
 * - Composition-friendly classes
 * - Atomic design alignment
 * - Consistent naming conventions
 * 
 * Usage:
 * - Import after globals.css
 * - Use with component props
 * - Extend with Tailwind utilities
 * - Support design system tokens
 */

/* ===== BUTTON VARIANTS ===== */

/* Primary Button */
.btn-primary {
  @apply btn-base;
  background-color: var(--color-interactive-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-interactive-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-interactive-primary-hover);
  border-color: var(--color-interactive-primary-hover);
}

.btn-primary:active:not(:disabled) {
  background-color: var(--color-interactive-primary-active);
  border-color: var(--color-interactive-primary-active);
  transform: translateY(1px);
}

/* Secondary Button */
.btn-secondary {
  @apply btn-base;
  background-color: var(--color-interactive-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-interactive-secondary-hover);
  border-color: var(--color-border-secondary);
}

/* Outline Button */
.btn-outline {
  @apply btn-base;
  background-color: transparent;
  color: var(--color-interactive-primary);
  border-color: var(--color-interactive-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-interactive-primary);
  color: var(--color-text-inverse);
}

/* Ghost Button */
.btn-ghost {
  @apply btn-base;
  background-color: transparent;
  color: var(--color-text-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-interactive-secondary);
}

/* Destructive Button */
.btn-destructive {
  @apply btn-base;
  background-color: var(--color-error-500);
  color: var(--color-text-inverse);
  border-color: var(--color-error-500);
}

.btn-destructive:hover:not(:disabled) {
  background-color: var(--color-error-600);
  border-color: var(--color-error-600);
}

/* ===== BUTTON SIZES ===== */

.btn-xs {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--spacing-5) var(--spacing-10);
  font-size: var(--font-size-xl);
}

/* ===== INPUT VARIANTS ===== */

/* Input sizes */
.input-sm {
  @apply input-base;
  padding: var(--spacing-2);
  font-size: var(--font-size-xs);
}

.input-lg {
  @apply input-base;
  padding: var(--spacing-4);
  font-size: var(--font-size-lg);
}

/* Input states */
.input-error {
  border-color: var(--color-border-error);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.input-success {
  border-color: var(--color-success-500);
  box-shadow: 0 0 0 3px rgb(34 197 94 / 0.1);
}

/* ===== BADGE VARIANTS ===== */

.badge-base {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  white-space: nowrap;
}

.badge-primary {
  @apply badge-base;
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-secondary {
  @apply badge-base;
  background-color: var(--color-secondary-100);
  color: var(--color-secondary-800);
}

.badge-success {
  @apply badge-base;
  background-color: var(--color-success-50);
  color: var(--color-success-700);
}

.badge-warning {
  @apply badge-base;
  background-color: var(--color-warning-50);
  color: var(--color-warning-700);
}

.badge-error {
  @apply badge-base;
  background-color: var(--color-error-50);
  color: var(--color-error-700);
}

.badge-neutral {
  @apply badge-base;
  background-color: var(--color-neutral-100);
  color: var(--color-neutral-700);
}

/* ===== CARD VARIANTS ===== */

.card-elevated {
  @apply card-base;
  box-shadow: var(--shadow-md);
}

.card-interactive {
  @apply card-base;
  cursor: pointer;
  transition: all var(--transition-base);
}

.card-interactive:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-bordered {
  @apply card-base;
  border-width: 2px;
}

/* ===== FORM FIELD VARIANTS ===== */

.form-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.form-label.required::after {
  content: " *";
  color: var(--color-error-500);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--color-error-600);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.form-help {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* ===== NAVIGATION VARIANTS ===== */

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--transition-base);
}

.nav-link:hover {
  color: var(--color-text-primary);
  background-color: var(--color-interactive-secondary);
  text-decoration: none;
}

.nav-link.active {
  color: var(--color-interactive-primary);
  background-color: var(--color-primary-50);
}

.nav-link.disabled {
  color: var(--color-text-disabled);
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== DROPDOWN VARIANTS ===== */

.dropdown-menu {
  position: absolute;
  z-index: var(--z-index-dropdown);
  min-width: 12rem;
  padding: var(--spacing-2);
  background-color: var(--color-background);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-0.5rem);
  transition: all var(--transition-base);
}

.dropdown-menu.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: none;
  border-radius: var(--radius-md);
  background: none;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  text-align: left;
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.dropdown-item:hover {
  background-color: var(--color-interactive-secondary);
}

.dropdown-item.destructive {
  color: var(--color-error-600);
}

.dropdown-item.destructive:hover {
  background-color: var(--color-error-50);
}

.dropdown-divider {
  height: 1px;
  margin: var(--spacing-2) 0;
  background-color: var(--color-border-primary);
}

/* ===== MODAL VARIANTS ===== */

.modal-backdrop {
  position: fixed;
  inset: 0;
  z-index: var(--z-index-modal-backdrop);
  background-color: rgb(0 0 0 / 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.modal-backdrop.open {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: var(--z-index-modal);
  width: 90vw;
  max-width: 32rem;
  max-height: 90vh;
  padding: var(--spacing-6);
  background-color: var(--color-background);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  transform: translate(-50%, -50%) scale(0.95);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  overflow-y: auto;
}

.modal-content.open {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--color-border-primary);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.modal-close {
  @apply btn-ghost;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
}

/* ===== TOAST VARIANTS ===== */

.toast {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  min-width: 20rem;
  padding: var(--spacing-4);
  background-color: var(--color-background);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  transform: translateX(100%);
  transition: transform var(--transition-base);
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  border-color: var(--color-success-500);
  background-color: var(--color-success-50);
}

.toast-warning {
  border-color: var(--color-warning-500);
  background-color: var(--color-warning-50);
}

.toast-error {
  border-color: var(--color-error-500);
  background-color: var(--color-error-50);
}

.toast-info {
  border-color: var(--color-info-500);
  background-color: var(--color-info-50);
}

/* ===== LOADING VARIANTS ===== */

.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-border-primary);
  border-top-color: var(--color-interactive-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-sm {
  width: 0.75rem;
  height: 0.75rem;
  border-width: 1px;
}

.spinner-lg {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 3px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-neutral-200) 25%,
    var(--color-neutral-100) 50%,
    var(--color-neutral-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== RESPONSIVE UTILITIES ===== */

.hide-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }
  
  .hide-desktop {
    display: none;
  }
}

/* ===== DARK THEME ADJUSTMENTS ===== */

[data-theme="dark"] {
  .skeleton {
    background: linear-gradient(
      90deg,
      var(--color-neutral-700) 25%,
      var(--color-neutral-600) 50%,
      var(--color-neutral-700) 75%
    );
  }
  
  .badge-primary {
    background-color: var(--color-primary-900);
    color: var(--color-primary-200);
  }
  
  .badge-secondary {
    background-color: var(--color-secondary-900);
    color: var(--color-secondary-200);
  }
  
  .nav-link.active {
    background-color: var(--color-primary-900);
  }
}

/**
 * Development Notes:
 * 
 * 1. Component Architecture:
 *    - Base styles in globals.css
 *    - Variants and modifiers here
 *    - Atomic design principles
 *    - Composition over inheritance
 * 
 * 2. Naming Convention:
 *    - BEM-inspired methodology
 *    - Component-variant-modifier pattern
 *    - Consistent prefixing
 * 
 * 3. State Management:
 *    - CSS-based state classes
 *    - JavaScript toggle support
 *    - Accessibility considerations
 * 
 * 4. Responsive Design:
 *    - Mobile-first approach
 *    - Breakpoint consistency
 *    - Progressive enhancement
 * 
 * 5. Theme Support:
 *    - Light/dark theme variants
 *    - CSS custom property usage
 *    - Consistent color application
 * 
 * 6. Performance:
 *    - Efficient selectors
 *    - Minimal specificity
 *    - Hardware acceleration
 * 
 * Usage Examples:
 * 
 * Button variants:
 * ```tsx
 * <button className="btn-primary btn-lg">
 *   Primary Large
 * </button>
 * ```
 * 
 * Form field:
 * ```tsx
 * <div className="form-field">
 *   <label className="form-label required">Email</label>
 *   <input className="input-base" type="email" />
 *   <span className="form-error">Invalid email</span>
 * </div>
 * ```
 * 
 * Navigation:
 * ```tsx
 * <a className="nav-link active" href="/dashboard">
 *   Dashboard
 * </a>
 * ```
 */