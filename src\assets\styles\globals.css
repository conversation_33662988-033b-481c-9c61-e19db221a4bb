/**
 * Global Styles
 * 
 * This file contains global styles, CSS resets, and base styles
 * for the Lemon App. It imports design tokens and sets up
 * foundational styling.
 * 
 * Key Concepts:
 * - CSS Reset and normalization
 * - Base typography styles
 * - Global utility classes
 * - Accessibility improvements
 * - Print styles
 * 
 * Usage:
 * - Import in app/layout.tsx
 * - Provides consistent base styling
 * - Works with Tailwind CSS
 * - Supports component styling
 */

/* Import design tokens */
@import './tokens.css';

/* Import Tailwind CSS (if using) */
/* @tailwind base;
@tailwind components; */
@tailwind utilities;

/* ===== CSS RESET & NORMALIZATION ===== */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  /* Prevent font size inflation */
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
  
  /* Set base font size for rem calculations */
  font-size: 16px;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

body {
  /* Typography */
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  
  /* Colors */
  color: var(--color-text-primary);
  background-color: var(--color-background);
  
  /* Rendering optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  
  /* Minimum height */
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height */
}

/* ===== TYPOGRAPHY RESET ===== */

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin: 0;
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin: 0;
  line-height: var(--line-height-normal);
}

a {
  color: var(--color-interactive-primary);
  text-decoration: none;
  transition: color var(--transition-base);
}

a:hover {
  color: var(--color-interactive-primary-hover);
  text-decoration: underline;
}

a:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

strong, b {
  font-weight: var(--font-weight-semibold);
}

em, i {
  font-style: italic;
}

small {
  font-size: var(--font-size-sm);
}

code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-background-secondary);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border-primary);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-background-secondary);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-primary);
  overflow-x: auto;
  line-height: var(--line-height-relaxed);
}

pre code {
  background: none;
  padding: 0;
  border: none;
  font-size: var(--font-size-sm);
}

blockquote {
  border-left: 4px solid var(--color-border-secondary);
  padding-left: var(--spacing-4);
  margin: var(--spacing-4) 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

/* ===== FORM ELEMENTS RESET ===== */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button,
select {
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
}

button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

input::placeholder,
textarea::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

/* ===== LIST RESET ===== */

ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* ===== TABLE RESET ===== */

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th, td {
  text-align: left;
  vertical-align: top;
  padding: var(--spacing-2) var(--spacing-3);
  border-bottom: 1px solid var(--color-border-primary);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  background-color: var(--color-background-secondary);
}

/* ===== MEDIA RESET ===== */

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

svg {
  fill: currentColor;
}

/* ===== UTILITY CLASSES ===== */

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible utility */
.focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* Container utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn var(--transition-base) ease-in-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.animate-slide-down {
  animation: slideDown var(--transition-base) ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ===== COMPONENT BASE STYLES ===== */

/* Button base styles */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  transition: all var(--transition-base);
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}

.btn-base:disabled {
  pointer-events: none;
  opacity: 0.6;
}

/* Input base styles */
.input-base {
  display: block;
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: border-color var(--transition-base), box-shadow var(--transition-base);
}

.input-base:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.input-base:disabled {
  background-color: var(--color-background-secondary);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

/* Card base styles */
.card-base {
  background-color: var(--color-background);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

/* ===== PRINT STYLES ===== */

@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]::after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  
  pre {
    white-space: pre-wrap !important;
  }
  
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  thead {
    display: table-header-group;
  }
  
  tr,
  img {
    page-break-inside: avoid;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
  :root {
    --color-border-primary: #000000;
    --color-border-secondary: #000000;
    --color-text-secondary: #000000;
  }
  
  [data-theme="dark"] {
    --color-border-primary: #ffffff;
    --color-border-secondary: #ffffff;
    --color-text-secondary: #ffffff;
  }
}

/**
 * Development Notes:
 * 
 * 1. Reset Strategy:
 *    - Modern CSS reset approach
 *    - Preserve useful browser defaults
 *    - Focus on consistency across browsers
 * 
 * 2. Base Styles:
 *    - Typography hierarchy
 *    - Form element consistency
 *    - Interactive element states
 * 
 * 3. Utility Classes:
 *    - Common layout patterns
 *    - Accessibility helpers
 *    - Animation utilities
 * 
 * 4. Component Base:
 *    - Shared component foundations
 *    - Consistent interaction patterns
 *    - Extensible design system
 * 
 * 5. Accessibility:
 *    - Focus management
 *    - Screen reader support
 *    - High contrast mode
 *    - Reduced motion support
 * 
 * 6. Performance:
 *    - Minimal global styles
 *    - Efficient selectors
 *    - Hardware acceleration hints
 * 
 * Usage Examples:
 * 
 * In layout.tsx:
 * ```tsx
 * import '@/assets/styles/globals.css'
 * ```
 * 
 * Custom component styling:
 * ```tsx
 * <button className="btn-base bg-primary-500 text-white hover:bg-primary-600">
 *   Click me
 * </button>
 * ```
 * 
 * Container usage:
 * ```tsx
 * <div className="container">
 *   <h1>Page Content</h1>
 * </div>
 * ```
 */