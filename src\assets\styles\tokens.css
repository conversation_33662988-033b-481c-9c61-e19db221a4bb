/**
 * Design Tokens
 * 
 * This file contains all design tokens (colors, typography, spacing, etc.)
 * that define the visual language of the Lemon App.
 * 
 * Key Concepts:
 * - CSS Custom Properties for dynamic theming
 * - Semantic color naming
 * - Consistent spacing scale
 * - Typography hierarchy
 * - Component-agnostic tokens
 * 
 * Usage:
 * - Import in globals.css
 * - Reference in Tailwind config
 * - Use in component styles
 * - Support for dark/light themes
 */

:root {
  /* ===== COLOR PALETTE ===== */
  
  /* Primary Colors (Lemon Theme) */
  --color-primary-50: #fffbeb;
  --color-primary-100: #fef3c7;
  --color-primary-200: #fde68a;
  --color-primary-300: #fcd34d;
  --color-primary-400: #fbbf24;
  --color-primary-500: #f59e0b; /* Main primary */
  --color-primary-600: #d97706;
  --color-primary-700: #b45309;
  --color-primary-800: #92400e;
  --color-primary-900: #78350f;
  --color-primary-950: #451a03;
  
  /* Secondary Colors (Blue) */
  --color-secondary-50: #eff6ff;
  --color-secondary-100: #dbeafe;
  --color-secondary-200: #bfdbfe;
  --color-secondary-300: #93c5fd;
  --color-secondary-400: #60a5fa;
  --color-secondary-500: #3b82f6; /* Main secondary */
  --color-secondary-600: #2563eb;
  --color-secondary-700: #1d4ed8;
  --color-secondary-800: #1e40af;
  --color-secondary-900: #1e3a8a;
  --color-secondary-950: #172554;
  
  /* Neutral Colors */
  --color-neutral-0: #ffffff;
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;
  --color-neutral-950: #030712;
  
  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  
  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  
  /* ===== SEMANTIC COLOR ASSIGNMENTS ===== */
  
  /* Background Colors */
  --color-background: var(--color-neutral-0);
  --color-background-secondary: var(--color-neutral-50);
  --color-background-tertiary: var(--color-neutral-100);
  --color-background-inverse: var(--color-neutral-900);
  
  /* Text Colors */
  --color-text-primary: var(--color-neutral-900);
  --color-text-secondary: var(--color-neutral-600);
  --color-text-tertiary: var(--color-neutral-500);
  --color-text-inverse: var(--color-neutral-0);
  --color-text-disabled: var(--color-neutral-400);
  
  /* Border Colors */
  --color-border-primary: var(--color-neutral-200);
  --color-border-secondary: var(--color-neutral-300);
  --color-border-focus: var(--color-secondary-500);
  --color-border-error: var(--color-error-500);
  
  /* Interactive Colors */
  --color-interactive-primary: var(--color-secondary-500);
  --color-interactive-primary-hover: var(--color-secondary-600);
  --color-interactive-primary-active: var(--color-secondary-700);
  --color-interactive-secondary: var(--color-neutral-100);
  --color-interactive-secondary-hover: var(--color-neutral-200);
  
  /* ===== TYPOGRAPHY ===== */
  
  /* Font Families */
  --font-family-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif;
  --font-family-mono: var(--font-geist-mono), ui-monospace, 'SF Mono', monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Font Weights */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* ===== SPACING ===== */
  
  /* Base spacing unit (4px) */
  --spacing-unit: 0.25rem;
  
  /* Spacing Scale */
  --spacing-0: 0;
  --spacing-1: calc(var(--spacing-unit) * 1);   /* 4px */
  --spacing-2: calc(var(--spacing-unit) * 2);   /* 8px */
  --spacing-3: calc(var(--spacing-unit) * 3);   /* 12px */
  --spacing-4: calc(var(--spacing-unit) * 4);   /* 16px */
  --spacing-5: calc(var(--spacing-unit) * 5);   /* 20px */
  --spacing-6: calc(var(--spacing-unit) * 6);   /* 24px */
  --spacing-8: calc(var(--spacing-unit) * 8);   /* 32px */
  --spacing-10: calc(var(--spacing-unit) * 10); /* 40px */
  --spacing-12: calc(var(--spacing-unit) * 12); /* 48px */
  --spacing-16: calc(var(--spacing-unit) * 16); /* 64px */
  --spacing-20: calc(var(--spacing-unit) * 20); /* 80px */
  --spacing-24: calc(var(--spacing-unit) * 24); /* 96px */
  --spacing-32: calc(var(--spacing-unit) * 32); /* 128px */
  
  /* ===== SIZING ===== */
  
  /* Container Sizes */
  --container-xs: 20rem;      /* 320px */
  --container-sm: 24rem;      /* 384px */
  --container-md: 28rem;      /* 448px */
  --container-lg: 32rem;      /* 512px */
  --container-xl: 36rem;      /* 576px */
  --container-2xl: 42rem;     /* 672px */
  --container-3xl: 48rem;     /* 768px */
  --container-4xl: 56rem;     /* 896px */
  --container-5xl: 64rem;     /* 1024px */
  --container-6xl: 72rem;     /* 1152px */
  --container-7xl: 80rem;     /* 1280px */
  
  /* ===== BORDER RADIUS ===== */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;      /* 2px */
  --radius-base: 0.25rem;     /* 4px */
  --radius-md: 0.375rem;      /* 6px */
  --radius-lg: 0.5rem;        /* 8px */
  --radius-xl: 0.75rem;       /* 12px */
  --radius-2xl: 1rem;         /* 16px */
  --radius-3xl: 1.5rem;       /* 24px */
  --radius-full: 9999px;
  
  /* ===== SHADOWS ===== */
  
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  /* ===== TRANSITIONS ===== */
  
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  --transition-slower: 500ms ease;
  
  /* ===== Z-INDEX ===== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ===== DARK THEME ===== */

[data-theme="dark"] {
  /* Background Colors */
  --color-background: var(--color-neutral-900);
  --color-background-secondary: var(--color-neutral-800);
  --color-background-tertiary: var(--color-neutral-700);
  --color-background-inverse: var(--color-neutral-0);
  
  /* Text Colors */
  --color-text-primary: var(--color-neutral-0);
  --color-text-secondary: var(--color-neutral-300);
  --color-text-tertiary: var(--color-neutral-400);
  --color-text-inverse: var(--color-neutral-900);
  --color-text-disabled: var(--color-neutral-600);
  
  /* Border Colors */
  --color-border-primary: var(--color-neutral-700);
  --color-border-secondary: var(--color-neutral-600);
  
  /* Interactive Colors */
  --color-interactive-secondary: var(--color-neutral-800);
  --color-interactive-secondary-hover: var(--color-neutral-700);
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
    --transition-slower: 0ms;
  }
}

/**
 * Development Notes:
 * 
 * 1. Token Organization:
 *    - Raw color palette first
 *    - Semantic assignments second
 *    - Component-specific tokens in components
 * 
 * 2. Naming Convention:
 *    - Use kebab-case for CSS custom properties
 *    - Prefix with category (color-, font-, spacing-)
 *    - Use semantic names for assignments
 * 
 * 3. Theme Support:
 *    - Light theme as default
 *    - Dark theme with data-theme attribute
 *    - System preference detection
 * 
 * 4. Accessibility:
 *    - Respect reduced motion preferences
 *    - Ensure sufficient color contrast
 *    - Support high contrast mode
 * 
 * 5. Maintenance:
 *    - Keep tokens in sync with Tailwind config
 *    - Document token usage and relationships
 *    - Version control token changes
 * 
 * Usage Examples:
 * 
 * In CSS:
 * ```css
 * .button {
 *   background-color: var(--color-interactive-primary);
 *   color: var(--color-text-inverse);
 *   padding: var(--spacing-3) var(--spacing-6);
 *   border-radius: var(--radius-md);
 *   transition: background-color var(--transition-base);
 * }
 * ```
 * 
 * In Tailwind config:
 * ```js
 * module.exports = {
 *   theme: {
 *     extend: {
 *       colors: {
 *         primary: {
 *           50: 'var(--color-primary-50)',
 *           500: 'var(--color-primary-500)',
 *           // ...
 *         }
 *       }
 *     }
 *   }
 * }
 * ```
 */