"use client";

import * as React from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";

// Definisi BaseAlert
type BaseAlertProps = React.ComponentPropsWithoutRef<typeof Alert>;

const BaseAlert = React.forwardRef<HTMLDivElement, BaseAlertProps>(
  ({ className, ...props }, ref) => {
    return (
      <Alert
        ref={ref}
        className={cn(
          "bg-[#FEF4E9] border-[#F9B667] border-2 [&>svg]:size-5",
          !props.variant && "text-[#F8A644]",
          className
        )}
        {...props}
      />
    );
  }
);
BaseAlert.displayName = "BaseAlert";

// Definisi BaseAlertTitle
type BaseAlertTitleProps = React.ComponentPropsWithoutRef<typeof AlertTitle>;

const BaseAlertTitle = React.forwardRef<HTMLDivElement, BaseAlertTitleProps>(
  ({ className, ...props }, ref) => {
    return (
      <AlertTitle ref={ref} className={cn("text-base", className)} {...props} />
    );
  }
);
BaseAlertTitle.displayName = "BaseAlertTitle";

// Definisi BaseAlertDescription
type BaseAlertDescriptionProps = React.ComponentPropsWithoutRef<
  typeof AlertDescription
>;

const BaseAlertDescription = React.forwardRef<
  HTMLDivElement,
  BaseAlertDescriptionProps
>(({ className, ...props }, ref) => {
  return (
    <AlertDescription
      ref={ref}
      className={cn("text-sm", className)}
      {...props}
    />
  );
});
BaseAlertDescription.displayName = "BaseAlertDescription";

export { BaseAlert, BaseAlertTitle, BaseAlertDescription };
