"use client";

import * as React from "react";
import {
  Avatar as ShadcnAvatar,
  AvatarImage as ShadcnAvatarImage,
  AvatarFallback as ShadcnAvatarFallback,
} from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

// BaseAvatar Component
export type BaseAvatarProps = React.ComponentPropsWithoutRef<
  typeof ShadcnAvatar
>;
const BaseAvatar = React.forwardRef<HTMLDivElement, BaseAvatarProps>(
  ({ className, ...props }, ref) => {
    return <ShadcnAvatar ref={ref} className={cn("", className)} {...props} />;
  }
);
BaseAvatar.displayName = "BaseAvatar";

// BaseAvatarImage Component
export type BaseAvatarImageProps = React.ComponentPropsWithoutRef<
  typeof ShadcnAvatarImage
>;
const BaseAvatarImage = React.forwardRef<
  HTMLImageElement,
  BaseAvatarImageProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnAvatarImage ref={ref} className={cn("", className)} {...props} />
  );
});
BaseAvatarImage.displayName = "BaseAvatarImage";

// BaseAvatarFallback Component
export type BaseAvatarFallbackProps = React.ComponentPropsWithoutRef<
  typeof ShadcnAvatarFallback
>;
const BaseAvatarFallback = React.forwardRef<
  HTMLDivElement,
  BaseAvatarFallbackProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnAvatarFallback ref={ref} className={cn("", className)} {...props} />
  );
});
BaseAvatarFallback.displayName = "BaseAvatarFallback";

export { BaseAvatar, BaseAvatarImage, BaseAvatarFallback };
