"use client";

import * as React from "react";
import { Badge as UIBadge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

type BaseBadgeProps = React.ComponentPropsWithoutRef<typeof UIBadge>;

const BaseBadge = React.forwardRef<HTMLSpanElement, BaseBadgeProps>(
  ({ className, ...props }, ref) => (
    <UIBadge
      ref={ref}
      className={cn(
        "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
        className
      )}
      {...props}
    />
  )
);

BaseBadge.displayName = "BaseBadge";

export { BaseBadge };
