"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type BaseButtonProps = React.ComponentPropsWithoutRef<typeof Button>;

const BaseButton = React.forwardRef<HTMLButtonElement, BaseButtonProps>(
  ({ className, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(
          !props.variant ? "bg-[#F7941E] hover:bg-[#FFA733]" : "",
          "hover:cursor-pointer",
          className
        )}
        {...props}
      />
    );
  }
);

BaseButton.displayName = "BaseButton";

export { BaseButton };
