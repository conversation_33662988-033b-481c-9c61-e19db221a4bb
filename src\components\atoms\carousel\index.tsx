"use client";

import * as React from "react";
import {
  Carousel as UICarousel,
  CarouselContent as UICarouselContent,
  CarouselItem as UICarouselItem,
  CarouselPrevious as UICarouselPrevious,
  CarouselNext as UICarouselNext,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";

// BaseCarousel
export interface BaseCarouselProps
  extends React.ComponentPropsWithoutRef<"div"> {}

const BaseCarousel = React.forwardRef<HTMLDivElement, BaseCarouselProps>(
  ({ className, ...props }, ref) => {
    return (
      <UICarousel ref={ref} className={cn("relative", className)} {...props} />
    );
  }
);
BaseCarousel.displayName = "BaseCarousel";

// BaseCarouselContent
export interface BaseCarouselContentProps
  extends React.ComponentPropsWithoutRef<"div"> {}

const BaseCarouselContent = React.forwardRef<
  HTMLDivElement,
  BaseCarouselContentProps
>(({ className, ...props }, ref) => {
  return (
    <UICarouselContent
      ref={ref}
      className={cn("overflow-hidden", className)}
      {...props}
    />
  );
});
BaseCarouselContent.displayName = "BaseCarouselContent";

// BaseCarouselItem
export interface BaseCarouselItemProps
  extends React.ComponentPropsWithoutRef<"div"> {}

const BaseCarouselItem = React.forwardRef<
  HTMLDivElement,
  BaseCarouselItemProps
>(({ className, ...props }, ref) => {
  return (
    <UICarouselItem
      ref={ref}
      className={cn("min-w-0 shrink-0 grow-0 basis-full", className)}
      {...props}
    />
  );
});
BaseCarouselItem.displayName = "BaseCarouselItem";

// BaseCarouselPrevious
export interface BaseCarouselPreviousProps
  extends React.ComponentPropsWithoutRef<"button"> {}

const BaseCarouselPrevious = React.forwardRef<
  HTMLButtonElement,
  BaseCarouselPreviousProps
>(({ className, ...props }, ref) => {
  return (
    <UICarouselPrevious
      ref={ref}
      className={cn("absolute", className)}
      {...props}
    />
  );
});
BaseCarouselPrevious.displayName = "BaseCarouselPrevious";

// BaseCarouselNext
export interface BaseCarouselNextProps
  extends React.ComponentPropsWithoutRef<"button"> {}

const BaseCarouselNext = React.forwardRef<
  HTMLButtonElement,
  BaseCarouselNextProps
>(({ className, ...props }, ref) => {
  return (
    <UICarouselNext
      ref={ref}
      className={cn("absolute", className)}
      {...props}
    />
  );
});
BaseCarouselNext.displayName = "BaseCarouselNext";

export {
  BaseCarousel,
  BaseCarouselContent,
  BaseCarouselItem,
  BaseCarouselPrevious,
  BaseCarouselNext,
};
