'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import {
  Tooltip as RechartsTooltip,
  type TooltipProps as RechartsTooltipProps,
} from 'recharts';

type ChartConfig = Record<string, { label?: string; color?: string }>;

export function ChartContainer({
  className,
  children,
  config,
}: React.PropsWithChildren<{ className?: string; config?: ChartConfig }>) {
  const style: React.CSSProperties = {};
  if (config) {
    Object.entries(config).forEach(([_, v], i) => {
      (style as any)[`--chart-${i + 1}`] = v.color ?? 'hsl(25 95% 55%)';
    });
  }
  return (
    <div
      className={cn('rounded-lg bg-white', className)}
      style={style}
    >
      {children}
    </div>
  );
}

export function ChartTooltip<
  TValue extends number | string = number,
  TName extends number | string = string
>({
  content,
  ...props
}: Omit<RechartsTooltipProps<TValue, TName>, 'wrapperStyle'> & {
  content?: React.ReactNode;
}) {
  return (
    <RechartsTooltip
      cursor={{ fill: 'rgba(0,0,0,0.04)' }}
      wrapperStyle={{ outline: 'none' }}
      content={content as any}
      {...props}
    />
  );
}

export function ChartTooltipContent({
  label,
  payload,
}: Readonly<{ label?: React.ReactNode; payload?: any[] }>) {
  if (!payload?.length) return null;
  const p = payload[0];
  return (
    <div className="rounded-md border border-[#EAEAEA] bg-white px-3 py-2 text-xs shadow-sm">
      {label && <div className="mb-1 text-[11px] text-[#7a7a7a]">{label}</div>}
      <div className="flex items-center gap-2">
        <span
          className="inline-block h-2 w-2 rounded"
          style={{ background: p?.color }}
        />
        <span className="text-[#3C3C3C] font-medium">{p?.value}</span>
      </div>
    </div>
  );
}
