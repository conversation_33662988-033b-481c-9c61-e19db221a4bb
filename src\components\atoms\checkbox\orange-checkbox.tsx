'use client';
import { Checkbox, type CheckboxProps } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

export function OrangeCheckbox({ className, ...props }: CheckboxProps) {
  return (
    <Checkbox
      className={cn(
        'h-4 w-4 rounded border border-[#DEDEDE] text-white cursor-pointer',
        'data-[state=checked]:bg-[#F7941E] data-[state=checked]:border-[#F7941E]',
        'focus-visible:ring-2 focus-visible:ring-[#F7941E] focus-visible:ring-offset-2',
        'transition-colors',
        className
      )}
      {...props}
    />
  );
}
