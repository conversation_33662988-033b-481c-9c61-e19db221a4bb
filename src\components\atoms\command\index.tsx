"use client";

import * as React from "react";
import { Command as CommandPrimitive } from "cmdk";
import { SearchIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

// Definisi BaseCommand
type BaseCommandProps = React.ComponentPropsWithoutRef<typeof CommandPrimitive>;

const BaseCommand = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive>,
  BaseCommandProps
>(({ className, ...props }, ref) => (
  <CommandPrimitive
    ref={ref}
    data-slot="base-command"
    className={cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md bg-white text-zinc-800 dark:bg-zinc-900 dark:text-white",
      className
    )}
    {...props}
  />
));
BaseCommand.displayName = CommandPrimitive.displayName;

// Definisi BaseCommandDialog
type BaseCommandDialogProps = React.ComponentPropsWithoutRef<typeof Dialog> &
  React.ComponentPropsWithoutRef<typeof CommandPrimitive> & {
    title?: string;
    description?: string;
    showCloseButton?: boolean;
  };

const BaseCommandDialog = ({
  title = "Command Palette",
  description = "Search for a command to run...",
  children,
  className,
  showCloseButton = true,
  ...props
}: BaseCommandDialogProps) => {
  return (
    <Dialog {...props}>
      <DialogHeader className="sr-only">
        <DialogTitle>{title}</DialogTitle>
        <DialogDescription>{description}</DialogDescription>
      </DialogHeader>
      <DialogContent
        className={cn(
          "overflow-hidden p-0",
          "rounded-2xl border-2 border-slate-200 dark:border-slate-800",
          className
        )}
        showCloseButton={showCloseButton}
      >
        <BaseCommand
          className="[&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5"
          {...props}
        >
          {children}
        </BaseCommand>
      </DialogContent>
    </Dialog>
  );
};

// Definisi BaseCommandInput
type BaseCommandInputProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.Input
>;

const BaseCommandInput = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Input>,
  BaseCommandInputProps
>(({ className, ...props }, ref) => (
  <div
    data-slot="base-command-input-wrapper"
    className="flex h-11 items-center gap-2 border-b-2 border-slate-200 px-3 dark:border-slate-800"
  >
    <SearchIcon className="size-4 shrink-0 opacity-50" />
    <CommandPrimitive.Input
      ref={ref}
      data-slot="base-command-input"
      className={cn(
        "placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    />
  </div>
));
BaseCommandInput.displayName = CommandPrimitive.Input.displayName;

// Definisi BaseCommandList
type BaseCommandListProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.List
>;

const BaseCommandList = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.List>,
  BaseCommandListProps
>(({ className, ...props }, ref) => (
  <CommandPrimitive.List
    ref={ref}
    data-slot="base-command-list"
    className={cn(
      "max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",
      className
    )}
    {...props}
  />
));
BaseCommandList.displayName = CommandPrimitive.List.displayName;

// Definisi BaseCommandEmpty
type BaseCommandEmptyProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.Empty
>;

const BaseCommandEmpty = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Empty>,
  BaseCommandEmptyProps
>(({ ...props }, ref) => (
  <CommandPrimitive.Empty
    ref={ref}
    data-slot="base-command-empty"
    className="py-6 text-center text-sm"
    {...props}
  />
));
BaseCommandEmpty.displayName = CommandPrimitive.Empty.displayName;

// Definisi BaseCommandGroup
type BaseCommandGroupProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.Group
>;

const BaseCommandGroup = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Group>,
  BaseCommandGroupProps
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Group
    ref={ref}
    data-slot="base-command-group"
    className={cn(
      "text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",
      className
    )}
    {...props}
  />
));
BaseCommandGroup.displayName = CommandPrimitive.Group.displayName;

// Definisi BaseCommandSeparator
type BaseCommandSeparatorProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.Separator
>;

const BaseCommandSeparator = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Separator>,
  BaseCommandSeparatorProps
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Separator
    ref={ref}
    data-slot="base-command-separator"
    className={cn("bg-border -mx-1 h-px", className)}
    {...props}
  />
));
BaseCommandSeparator.displayName = CommandPrimitive.Separator.displayName;

// Definisi BaseCommandItem
type BaseCommandItemProps = React.ComponentPropsWithoutRef<
  typeof CommandPrimitive.Item
>;

const BaseCommandItem = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Item>,
  BaseCommandItemProps
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Item
    ref={ref}
    data-slot="base-command-item"
    className={cn(
      "data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0",
      className
    )}
    {...props}
  />
));
BaseCommandItem.displayName = CommandPrimitive.Item.displayName;

// Definisi BaseCommandShortcut
type BaseCommandShortcutProps = React.ComponentPropsWithoutRef<"span">;

const BaseCommandShortcut = React.forwardRef<
  HTMLSpanElement,
  BaseCommandShortcutProps
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    data-slot="base-command-shortcut"
    className={cn(
      "text-muted-foreground ml-auto text-xs tracking-widest",
      className
    )}
    {...props}
  />
));
BaseCommandShortcut.displayName = "BaseCommandShortcut";

export {
  BaseCommand,
  BaseCommandDialog,
  BaseCommandInput,
  BaseCommandList,
  BaseCommandEmpty,
  BaseCommandGroup,
  BaseCommandItem,
  BaseCommandShortcut,
  BaseCommandSeparator,
};
