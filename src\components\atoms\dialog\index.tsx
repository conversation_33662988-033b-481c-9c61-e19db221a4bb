'use client';

import * as React from 'react';
import {
  Dialog as ShadcnDialog,
  DialogContent as ShadcnDialogContent,
  <PERSON><PERSON><PERSON>eader as <PERSON>hadcn<PERSON><PERSON>ogHeader,
  <PERSON><PERSON><PERSON>ooter as <PERSON><PERSON>c<PERSON><PERSON><PERSON><PERSON>Footer,
  DialogTitle as <PERSON>hadcnDialogTitle,
  DialogDescription as ShadcnDialogDescription,
  DialogPortal as ShadcnDialogPortal,
  DialogTrigger as ShadcnDialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

// BaseDialog Component
export type DialogProps = React.ComponentProps<typeof ShadcnDialog>;
const BaseDialog = (props: DialogProps) => {
  return <ShadcnDialog {...props} />;
};
BaseDialog.displayName = 'BaseDialog';

export type DialogContentProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogContent
> & {
  overlayClassName?: string;
  disableOutsideClose?: boolean;
};

const BaseDialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(
  (
    {
      className,
      overlayClassName,
      disableOutsideClose,
      onInteractOutside,
      ...props
    },
    ref
  ) => {
    return (
      <ShadcnDialogContent
        ref={ref}
        className={cn('', className)}
        overlayClassName={overlayClassName}
        onInteractOutside={(e) => {
          if (disableOutsideClose) e.preventDefault();
          onInteractOutside?.(e);
        }}
        {...props}
      />
    );
  }
);

BaseDialogContent.displayName = 'BaseDialogContent';

// BaseDialogHeader Component
export type DialogHeaderProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogHeader
>;

const BaseDialogHeader = React.forwardRef<HTMLDivElement, DialogHeaderProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnDialogHeader
        ref={ref}
        className={cn('', className)}
        {...props}
      />
    );
  }
);

BaseDialogHeader.displayName = 'BaseDialogHeader';

// BaseDialogFooter Component
export type DialogFooterProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogFooter
>;

const BaseDialogFooter = React.forwardRef<HTMLDivElement, DialogFooterProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnDialogFooter
        ref={ref}
        className={cn('', className)}
        {...props}
      />
    );
  }
);

BaseDialogFooter.displayName = 'BaseDialogFooter';

// BaseDialogTitle Component
export type DialogTitleProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogTitle
>;

const BaseDialogTitle = React.forwardRef<HTMLHeadingElement, DialogTitleProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnDialogTitle
        ref={ref}
        className={cn('', className)}
        {...props}
      />
    );
  }
);

BaseDialogTitle.displayName = 'BaseDialogTitle';

// BaseDialogDescription Component
export type DialogDescriptionProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogDescription
>;

const BaseDialogDescription = React.forwardRef<
  HTMLParagraphElement,
  DialogDescriptionProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnDialogDescription
      ref={ref}
      className={cn('', className)}
      {...props}
    />
  );
});

BaseDialogDescription.displayName = 'BaseDialogDescription';

// BaseDialogPortal Component
export type DialogPortalProps = React.ComponentProps<typeof ShadcnDialogPortal>;
const BaseDialogPortal = (props: DialogPortalProps) => {
  return <ShadcnDialogPortal {...props} />;
};
BaseDialogPortal.displayName = 'BaseDialogPortal';

// BaseDialogTrigger Component
export type DialogTriggerProps = React.ComponentPropsWithoutRef<
  typeof ShadcnDialogTrigger
>;

const BaseDialogTrigger = React.forwardRef<
  HTMLButtonElement,
  DialogTriggerProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnDialogTrigger
      ref={ref}
      className={cn('', className)}
      {...props}
    />
  );
});

BaseDialogTrigger.displayName = 'BaseDialogTrigger';

export {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogFooter,
  BaseDialogTitle,
  BaseDialogDescription,
  BaseDialogPortal,
  BaseDialogTrigger,
};
