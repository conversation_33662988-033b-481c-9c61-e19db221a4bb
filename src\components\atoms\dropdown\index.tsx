"use client";

import * as React from "react";
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuPortal,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";

// Definisi BaseDropdownMenu
type BaseDropdownMenuProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenu
>;

const BaseDropdownMenu = React.forwardRef<
  React.ComponentProps<typeof DropdownMenuPrimitive.Root>,
  BaseDropdownMenuProps
>(({ ...props }) => {
  return <DropdownMenu {...props} />;
});
BaseDropdownMenu.displayName = "BaseDropdownMenu";

// Definisi BaseDropdownMenuPortal
type BaseDropdownMenuPortalProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuPortal
>;

const BaseDropdownMenuPortal = React.forwardRef<
  React.ComponentProps<typeof DropdownMenuPrimitive.Portal>,
  BaseDropdownMenuPortalProps
>(({ ...props }) => {
  return <DropdownMenuPortal {...props} />;
});
BaseDropdownMenuPortal.displayName = "BaseDropdownMenuPortal";

// Definisi BaseDropdownMenuTrigger
type BaseDropdownMenuTriggerProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuTrigger
>;

const BaseDropdownMenuTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Trigger>,
  BaseDropdownMenuTriggerProps
>(({ ...props }, ref) => {
  return <DropdownMenuTrigger ref={ref} {...props} />;
});
BaseDropdownMenuTrigger.displayName = "BaseDropdownMenuTrigger";

// Definisi BaseDropdownMenuContent
type BaseDropdownMenuContentProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuContent
>;

const BaseDropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  BaseDropdownMenuContentProps
>(({ className, ...props }, ref) => {
  return (
    <DropdownMenuContent
      ref={ref}
      className={cn(
        "bg-white dark:bg-zinc-800 border-gray-100 dark:border-zinc-700 rounded-xl shadow-lg",
        className
      )}
      {...props}
    />
  );
});
BaseDropdownMenuContent.displayName = "BaseDropdownMenuContent";

// Definisi BaseDropdownMenuGroup
type BaseDropdownMenuGroupProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuGroup
>;

const BaseDropdownMenuGroup = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Group>,
  BaseDropdownMenuGroupProps
>(({ ...props }, ref) => {
  return <DropdownMenuGroup ref={ref} {...props} />;
});
BaseDropdownMenuGroup.displayName = "BaseDropdownMenuGroup";

// Definisi BaseDropdownMenuItem
type BaseDropdownMenuItemProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuItem
>;

const BaseDropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  BaseDropdownMenuItemProps
>(({ className, ...props }, ref) => {
  return (
    <DropdownMenuItem
      ref={ref}
      className={cn("focus:bg-zinc-100 dark:focus:bg-zinc-700", className)}
      {...props}
    />
  );
});
BaseDropdownMenuItem.displayName = "BaseDropdownMenuItem";

// Definisi BaseDropdownMenuCheckboxItem
type BaseDropdownMenuCheckboxItemProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuCheckboxItem
>;

const BaseDropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  BaseDropdownMenuCheckboxItemProps
>(({ className, ...props }, ref) => {
  return (
    <DropdownMenuCheckboxItem
      ref={ref}
      className={cn("focus:bg-zinc-100 dark:focus:bg-zinc-700", className)}
      {...props}
    />
  );
});
BaseDropdownMenuCheckboxItem.displayName = "BaseDropdownMenuCheckboxItem";

// Definisi BaseDropdownMenuRadioGroup
type BaseDropdownMenuRadioGroupProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuRadioGroup
>;

const BaseDropdownMenuRadioGroup = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioGroup>,
  BaseDropdownMenuRadioGroupProps
>(({ ...props }, ref) => {
  return <DropdownMenuRadioGroup ref={ref} {...props} />;
});
BaseDropdownMenuRadioGroup.displayName = "BaseDropdownMenuRadioGroup";

// Definisi BaseDropdownMenuRadioItem
type BaseDropdownMenuRadioItemProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuRadioItem
>;

const BaseDropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  BaseDropdownMenuRadioItemProps
>(({ className, ...props }, ref) => {
  return (
    <DropdownMenuRadioItem
      ref={ref}
      className={cn("focus:bg-zinc-100 dark:focus:bg-zinc-700", className)}
      {...props}
    />
  );
});
BaseDropdownMenuRadioItem.displayName = "BaseDropdownMenuRadioItem";

// Definisi BaseDropdownMenuLabel
type BaseDropdownMenuLabelProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuLabel
>;

const BaseDropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  BaseDropdownMenuLabelProps
>(({ ...props }, ref) => {
  return <DropdownMenuLabel ref={ref} {...props} />;
});
BaseDropdownMenuLabel.displayName = "BaseDropdownMenuLabel";

// Definisi BaseDropdownMenuSeparator
type BaseDropdownMenuSeparatorProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuSeparator
>;

const BaseDropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  BaseDropdownMenuSeparatorProps
>(({ ...props }, ref) => {
  return <DropdownMenuSeparator ref={ref} {...props} />;
});
BaseDropdownMenuSeparator.displayName = "BaseDropdownMenuSeparator";

// Definisi BaseDropdownMenuShortcut
type BaseDropdownMenuShortcutProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuShortcut
>;

const BaseDropdownMenuShortcut = React.forwardRef<
  React.ElementRef<"span">,
  BaseDropdownMenuShortcutProps
>(({ ...props }, ref) => {
  return <DropdownMenuShortcut ref={ref} {...props} />;
});
BaseDropdownMenuShortcut.displayName = "BaseDropdownMenuShortcut";

// Definisi BaseDropdownMenuSub
type BaseDropdownMenuSubProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuSub
>;

const BaseDropdownMenuSub = React.forwardRef<
  React.ComponentProps<typeof DropdownMenuPrimitive.Sub>,
  BaseDropdownMenuSubProps
>(({ ...props }) => {
  return <DropdownMenuSub {...props} />;
});
BaseDropdownMenuSub.displayName = "BaseDropdownMenuSub";

// Definisi BaseDropdownMenuSubTrigger
type BaseDropdownMenuSubTriggerProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuSubTrigger
>;

const BaseDropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  BaseDropdownMenuSubTriggerProps
>(({ ...props }, ref) => {
  return <DropdownMenuSubTrigger ref={ref} {...props} />;
});
BaseDropdownMenuSubTrigger.displayName = "BaseDropdownMenuSubTrigger";

// Definisi BaseDropdownMenuSubContent
type BaseDropdownMenuSubContentProps = React.ComponentPropsWithoutRef<
  typeof DropdownMenuSubContent
>;

const BaseDropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  BaseDropdownMenuSubContentProps
>(({ className, ...props }, ref) => {
  return (
    <DropdownMenuSubContent
      ref={ref}
      className={cn(
        "bg-white dark:bg-zinc-800 border-gray-100 dark:border-zinc-700 rounded-xl shadow-lg",
        className
      )}
      {...props}
    />
  );
});
BaseDropdownMenuSubContent.displayName = "BaseDropdownMenuSubContent";

export {
  BaseDropdownMenu,
  BaseDropdownMenuPortal,
  BaseDropdownMenuTrigger,
  BaseDropdownMenuContent,
  BaseDropdownMenuGroup,
  BaseDropdownMenuItem,
  BaseDropdownMenuCheckboxItem,
  BaseDropdownMenuRadioGroup,
  BaseDropdownMenuRadioItem,
  BaseDropdownMenuLabel,
  BaseDropdownMenuSeparator,
  BaseDropdownMenuShortcut,
  BaseDropdownMenuSub,
  BaseDropdownMenuSubTrigger,
  BaseDropdownMenuSubContent,
};
