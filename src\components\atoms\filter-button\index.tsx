'use client';

import { IconSlider } from '@/assets/icons/IconSlider';
import { cn } from '@/lib/utils';
import React from 'react';

export type FilterButtonProps = {
  onClick?: () => void;
  active?: boolean;
  className?: string;
  label?: string;
};

export default function FilterButton({
  onClick,
  active = false,
  className,
  label = 'Filter',
}: Readonly<FilterButtonProps>) {
  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        'w-full flex justify-center items-center gap-1 min-w-[114px] xl:max-w-fit h-11 rounded-md border text-sm font-medium transition cursor-pointer',
        active
          ? 'bg-[#FFF6EB] border-[#F4C27B] text-[#F7941E]'
          : 'bg-white border-[#E6E6E6] text-[#3C3C3C] hover:bg-[#F9F9F9]',
        className
      )}
    >
      <IconSlider
        size={16}
        color={active ? '#F7941E' : '#3C3C3C'}
      />
      <span>{label}</span>
    </button>
  );
}
