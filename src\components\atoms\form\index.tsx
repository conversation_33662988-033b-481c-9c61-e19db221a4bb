"use client";

import * as React from "react";
import {
  useFormField,
  Form as ShadcnForm,
  FormField as ShadcnFormField,
  FormItem as ShadcnFormItem,
  FormLabel as ShadcnFormLabel,
  FormControl as ShadcnFormControl,
  FormDescription as ShadcnFormDescription,
  FormMessage as ShadcnFormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";

// BaseForm Component
const BaseForm = ShadcnForm;

// BaseFormField Component
const BaseFormField = ShadcnFormField;

// BaseFormItem Component
const BaseFormItem = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div">
>(({ className, ...props }, ref) => {
  return <ShadcnFormItem ref={ref} className={cn("", className)} {...props} />;
});
BaseFormItem.displayName = "BaseFormItem";

// BaseFormLabel Component
const BaseFormLabel = React.forwardRef<
  HTMLLabelElement,
  React.ComponentPropsWithoutRef<typeof ShadcnFormLabel>
>(({ className, ...props }, ref) => {
  return <ShadcnFormLabel ref={ref} className={cn("", className)} {...props} />;
});
BaseFormLabel.displayName = "BaseFormLabel";

// BaseFormControl Component
const BaseFormControl = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div">
>(({ className, ...props }, ref) => {
  return (
    <ShadcnFormControl ref={ref} className={cn("", className)} {...props} />
  );
});
BaseFormControl.displayName = "BaseFormControl";

// BaseFormDescription Component
const BaseFormDescription = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof ShadcnFormDescription>
>(({ className, ...props }, ref) => {
  return (
    <ShadcnFormDescription ref={ref} className={cn("", className)} {...props} />
  );
});
BaseFormDescription.displayName = "BaseFormDescription";

// BaseFormMessage Component
const BaseFormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.ComponentPropsWithoutRef<typeof ShadcnFormMessage>
>(({ className, ...props }, ref) => {
  return (
    <ShadcnFormMessage ref={ref} className={cn("", className)} {...props} />
  );
});
BaseFormMessage.displayName = "BaseFormMessage";

export {
  BaseForm,
  useFormField as useBaseFormField,
  BaseFormField,
  BaseFormItem,
  BaseFormLabel,
  BaseFormControl,
  BaseFormDescription,
  BaseFormMessage,
};
