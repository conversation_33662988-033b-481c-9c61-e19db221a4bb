"use client";

import * as React from "react";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

type BaseLabelProps = React.ComponentPropsWithoutRef<typeof Label>;

const BaseLabel = React.forwardRef<HTMLLabelElement, BaseLabelProps>(
  ({ className, ...props }, ref) => {
    return <Label ref={ref} className={cn("", className)} {...props} />;
  }
);

BaseLabel.displayName = "BaseLabel";

export { BaseLabel };
