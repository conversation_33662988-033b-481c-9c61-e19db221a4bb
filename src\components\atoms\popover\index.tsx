"use client";

import * as React from "react";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverAnchor,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

// Definisi BasePopover
type BasePopoverProps = React.ComponentPropsWithoutRef<typeof Popover>;

const BasePopover = React.forwardRef<HTMLDivElement, BasePopoverProps>(
  ({ ...props }) => {
    return <Popover {...props} />;
  }
);
BasePopover.displayName = "BasePopover";

// Definisi BasePopoverTrigger
type BasePopoverTriggerProps = React.ComponentPropsWithoutRef<
  typeof PopoverTrigger
>;

const BasePopoverTrigger = React.forwardRef<
  HTMLButtonElement,
  BasePopoverTriggerProps
>(({ ...props }, ref) => {
  return <PopoverTrigger ref={ref} {...props} />;
});
BasePopoverTrigger.displayName = "BasePopoverTrigger";

// Definisi BasePopoverContent
type BasePopoverContentProps = React.ComponentPropsWithoutRef<
  typeof PopoverContent
>;

const BasePopoverContent = React.forwardRef<
  HTMLDivElement,
  BasePopoverContentProps
>(({ className, ...props }, ref) => {
  return (
    <PopoverContent
      ref={ref}
      className={cn(
        "rounded-xl bg-white dark:bg-zinc-800 shadow-lg border-gray-100 dark:border-zinc-700",
        className
      )}
      {...props}
    />
  );
});
BasePopoverContent.displayName = "BasePopoverContent";

// Definisi BasePopoverAnchor
type BasePopoverAnchorProps = React.ComponentPropsWithoutRef<
  typeof PopoverAnchor
>;

const BasePopoverAnchor = React.forwardRef<
  HTMLDivElement,
  BasePopoverAnchorProps
>(({ ...props }, ref) => {
  return <PopoverAnchor ref={ref} {...props} />;
});
BasePopoverAnchor.displayName = "BasePopoverAnchor";

export {
  BasePopover,
  BasePopoverTrigger,
  BasePopoverContent,
  BasePopoverAnchor,
};
