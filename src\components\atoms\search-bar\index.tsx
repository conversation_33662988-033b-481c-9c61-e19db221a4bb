'use client';

import { IconChevron } from '@/assets/icons/IconChevron';
import { IconSearchBox } from '@/assets/icons/IconSearchBox';
import { cn } from '@/lib/utils';
import React from 'react';

type Option = { label: string; value: string };

export type SearchBarProps = {
  categories: Option[];
  category: string;
  onCategoryChange: (v: string) => void;
  value: string;
  onChange: (v: string) => void;
  onSubmit?: () => void;
  placeholder?: string;
  className?: string;
};

export default function SearchBar({
  categories,
  category,
  onCategoryChange,
  value,
  onChange,
  onSubmit,
  placeholder = 'Cari...',
  className,
}: Readonly<SearchBarProps>) {
  return (
    <div
      className={cn(
        'w-full flex items-center gap-3 rounded-md border border-[#DEDEDE] bg-white h-11 px-4',
        className
      )}
    >
      {/* Select kategori */}
      <div className="relative flex items-center gap-2">
        <select
          value={category}
          onChange={(e) => onCategoryChange(e.target.value)}
          className={cn(
            'appearance-none pl-0 bg-transparent text-[#3C3C3C] text-sm font-medium',
            'focus:outline-none cursor-pointer'
          )}
        >
          {categories.map((opt) => (
            <option
              key={opt.value}
              value={opt.value}
            >
              {opt.label}
            </option>
          ))}
        </select>

        <button className="cursor-pointer">
          <IconChevron size={16} />
        </button>
      </div>

      <span className="h-5 w-px bg-[#E6E6E6]" />

      <input
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') onSubmit?.();
        }}
        placeholder={placeholder}
        className="flex-1 min-w-0 bg-transparent text-sm text-[#3C3C3C] placeholder:text-[#B1B1B1] focus:outline-none"
      />

      <button
        type="button"
        onClick={onSubmit}
        className="rounded-md p-1.5 hover:bg-[#F7F7F7] transition"
        aria-label="Cari"
        title="Cari"
      >
        <IconSearchBox
          color="#767676"
          size={16}
        />
      </button>
    </div>
  );
}
