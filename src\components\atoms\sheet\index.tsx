"use client";

import * as React from "react";
import {
  Sheet as ShadcnSheet,
  Sheet<PERSON>rigger as ShadcnSheetTrigger,
  SheetClose as ShadcnSheetClose,
  Sheet<PERSON>ontent as <PERSON>hadcn<PERSON>heetContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>heetHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>hadcnSheetFooter,
  She<PERSON><PERSON>it<PERSON> as ShadcnSheetTitle,
  SheetDescription as ShadcnSheetDescription,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

// BaseSheet Component
export type BaseSheetProps = React.ComponentPropsWithoutRef<typeof ShadcnSheet>;
const BaseSheet = React.forwardRef<HTMLDivElement, BaseSheetProps>(
  (props, ref) => {
    return <ShadcnSheet {...props} />;
  }
);
BaseSheet.displayName = "BaseSheet";

// BaseSheetTrigger Component
export type BaseSheetTriggerProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetTrigger
>;
const BaseSheetTrigger = React.forwardRef<
  HTMLButtonElement,
  BaseSheetTriggerProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnSheetTrigger ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSheetTrigger.displayName = "BaseSheetTrigger";

// BaseSheetClose Component
export type BaseSheetCloseProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetClose
>;
const BaseSheetClose = React.forwardRef<HTMLButtonElement, BaseSheetCloseProps>(
  (props, ref) => {
    return <ShadcnSheetClose ref={ref} {...props} />;
  }
);
BaseSheetClose.displayName = "BaseSheetClose";

// BaseSheetContent Component
export type BaseSheetContentProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetContent
> & { side?: "top" | "right" | "bottom" | "left" };
const BaseSheetContent = React.forwardRef<
  HTMLDivElement,
  BaseSheetContentProps
>(({ className, side, ...props }, ref) => {
  return (
    <ShadcnSheetContent
      ref={ref}
      className={cn("", className)}
      side={side}
      {...props}
    />
  );
});
BaseSheetContent.displayName = "BaseSheetContent";

// BaseSheetHeader Component
export type BaseSheetHeaderProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetHeader
>;
const BaseSheetHeader = React.forwardRef<HTMLDivElement, BaseSheetHeaderProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnSheetHeader ref={ref} className={cn("", className)} {...props} />
    );
  }
);
BaseSheetHeader.displayName = "BaseSheetHeader";

// BaseSheetFooter Component
export type BaseSheetFooterProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetFooter
>;
const BaseSheetFooter = React.forwardRef<HTMLDivElement, BaseSheetFooterProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnSheetFooter ref={ref} className={cn("", className)} {...props} />
    );
  }
);
BaseSheetFooter.displayName = "BaseSheetFooter";

// BaseSheetTitle Component
export type BaseSheetTitleProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetTitle
>;
const BaseSheetTitle = React.forwardRef<
  HTMLHeadingElement,
  BaseSheetTitleProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnSheetTitle ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSheetTitle.displayName = "BaseSheetTitle";

// BaseSheetDescription Component
export type BaseSheetDescriptionProps = React.ComponentPropsWithoutRef<
  typeof ShadcnSheetDescription
>;
const BaseSheetDescription = React.forwardRef<
  HTMLParagraphElement,
  BaseSheetDescriptionProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnSheetDescription
      ref={ref}
      className={cn("", className)}
      {...props}
    />
  );
});
BaseSheetDescription.displayName = "BaseSheetDescription";

export {
  BaseSheet,
  BaseSheetTrigger,
  BaseSheetClose,
  BaseSheetContent,
  BaseSheetHeader,
  BaseSheetFooter,
  BaseSheetTitle,
  BaseSheetDescription,
};
