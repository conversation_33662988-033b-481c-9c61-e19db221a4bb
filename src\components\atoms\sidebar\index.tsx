"use client";

import * as React from "react";
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

// BaseSidebar Component
export type BaseSidebarProps = React.ComponentPropsWithoutRef<typeof Sidebar>;
const BaseSidebar = React.forwardRef<HTMLDivElement, BaseSidebarProps>(
  ({ className, ...props }, ref) => {
    return (
      <Sidebar
        ref={ref}
        className={cn("bg-[#1E1E1E] min-h-dvh text-[#767676]", className)}
        {...props}
      />
    );
  }
);
BaseSidebar.displayName = "BaseSidebar";

// BaseSidebarContent Component
export type BaseSidebarContentProps = React.ComponentPropsWithoutRef<
  typeof SidebarContent
>;
const BaseSidebarContent = React.forwardRef<
  HTMLDivElement,
  BaseSidebarContentProps
>(({ className, ...props }, ref) => {
  return <SidebarContent ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarContent.displayName = "BaseSidebarContent";

// BaseSidebarFooter Component
export type BaseSidebarFooterProps = React.ComponentPropsWithoutRef<
  typeof SidebarFooter
>;
const BaseSidebarFooter = React.forwardRef<
  HTMLDivElement,
  BaseSidebarFooterProps
>(({ className, ...props }, ref) => {
  return <SidebarFooter ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarFooter.displayName = "BaseSidebarFooter";

// BaseSidebarGroup Component
export type BaseSidebarGroupProps = React.ComponentPropsWithoutRef<
  typeof SidebarGroup
>;
const BaseSidebarGroup = React.forwardRef<
  HTMLDivElement,
  BaseSidebarGroupProps
>(({ className, ...props }, ref) => {
  return <SidebarGroup ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarGroup.displayName = "BaseSidebarGroup";

// BaseSidebarGroupAction Component
export type BaseSidebarGroupActionProps = React.ComponentPropsWithoutRef<
  typeof SidebarGroupAction
>;
const BaseSidebarGroupAction = React.forwardRef<
  HTMLButtonElement,
  BaseSidebarGroupActionProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarGroupAction ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarGroupAction.displayName = "BaseSidebarGroupAction";

// BaseSidebarGroupContent Component
export type BaseSidebarGroupContentProps = React.ComponentPropsWithoutRef<
  typeof SidebarGroupContent
>;
const BaseSidebarGroupContent = React.forwardRef<
  HTMLDivElement,
  BaseSidebarGroupContentProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarGroupContent ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarGroupContent.displayName = "BaseSidebarGroupContent";

// BaseSidebarGroupLabel Component
export type BaseSidebarGroupLabelProps = React.ComponentPropsWithoutRef<
  typeof SidebarGroupLabel
>;
const BaseSidebarGroupLabel = React.forwardRef<
  HTMLDivElement,
  BaseSidebarGroupLabelProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarGroupLabel ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarGroupLabel.displayName = "BaseSidebarGroupLabel";

// BaseSidebarHeader Component
export type BaseSidebarHeaderProps = React.ComponentPropsWithoutRef<
  typeof SidebarHeader
>;
const BaseSidebarHeader = React.forwardRef<
  HTMLDivElement,
  BaseSidebarHeaderProps
>(({ className, ...props }, ref) => {
  return <SidebarHeader ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarHeader.displayName = "BaseSidebarHeader";

// BaseSidebarInput Component
export type BaseSidebarInputProps = React.ComponentPropsWithoutRef<
  typeof SidebarInput
>;
const BaseSidebarInput = React.forwardRef<
  HTMLInputElement,
  BaseSidebarInputProps
>(({ className, ...props }, ref) => {
  return <SidebarInput ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarInput.displayName = "BaseSidebarInput";

// BaseSidebarInset Component
export type BaseSidebarInsetProps = React.ComponentPropsWithoutRef<
  typeof SidebarInset
>;
const BaseSidebarInset = React.forwardRef<HTMLElement, BaseSidebarInsetProps>(
  ({ className, ...props }, ref) => {
    return <SidebarInset ref={ref} className={cn("", className)} {...props} />;
  }
);
BaseSidebarInset.displayName = "BaseSidebarInset";

// BaseSidebarMenu Component
export type BaseSidebarMenuProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenu
>;
const BaseSidebarMenu = React.forwardRef<
  HTMLUListElement,
  BaseSidebarMenuProps
>(({ className, ...props }, ref) => {
  return <SidebarMenu ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarMenu.displayName = "BaseSidebarMenu";

// BaseSidebarMenuAction Component
export type BaseSidebarMenuActionProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuAction
>;
const BaseSidebarMenuAction = React.forwardRef<
  HTMLButtonElement,
  BaseSidebarMenuActionProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuAction ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuAction.displayName = "BaseSidebarMenuAction";

// BaseSidebarMenuBadge Component
export type BaseSidebarMenuBadgeProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuBadge
>;
const BaseSidebarMenuBadge = React.forwardRef<
  HTMLDivElement,
  BaseSidebarMenuBadgeProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuBadge ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuBadge.displayName = "BaseSidebarMenuBadge";

// BaseSidebarMenuButton Component
export type BaseSidebarMenuButtonProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuButton
>;
const BaseSidebarMenuButton = React.forwardRef<
  HTMLButtonElement,
  BaseSidebarMenuButtonProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuButton ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuButton.displayName = "BaseSidebarMenuButton";

// BaseSidebarMenuItem Component
export type BaseSidebarMenuItemProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuItem
>;
const BaseSidebarMenuItem = React.forwardRef<
  HTMLLIElement,
  BaseSidebarMenuItemProps
>(({ className, ...props }, ref) => {
  return <SidebarMenuItem ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarMenuItem.displayName = "BaseSidebarMenuItem";

// BaseSidebarMenuSkeleton Component
export type BaseSidebarMenuSkeletonProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuSkeleton
>;
const BaseSidebarMenuSkeleton = React.forwardRef<
  HTMLDivElement,
  BaseSidebarMenuSkeletonProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuSkeleton ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuSkeleton.displayName = "BaseSidebarMenuSkeleton";

// BaseSidebarMenuSub Component
export type BaseSidebarMenuSubProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuSub
>;
const BaseSidebarMenuSub = React.forwardRef<
  HTMLUListElement,
  BaseSidebarMenuSubProps
>(({ className, ...props }, ref) => {
  return <SidebarMenuSub ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarMenuSub.displayName = "BaseSidebarMenuSub";

// BaseSidebarMenuSubButton Component
export type BaseSidebarMenuSubButtonProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuSubButton
>;
const BaseSidebarMenuSubButton = React.forwardRef<
  HTMLAnchorElement,
  BaseSidebarMenuSubButtonProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuSubButton ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuSubButton.displayName = "BaseSidebarMenuSubButton";

// BaseSidebarMenuSubItem Component
export type BaseSidebarMenuSubItemProps = React.ComponentPropsWithoutRef<
  typeof SidebarMenuSubItem
>;
const BaseSidebarMenuSubItem = React.forwardRef<
  HTMLLIElement,
  BaseSidebarMenuSubItemProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarMenuSubItem ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarMenuSubItem.displayName = "BaseSidebarMenuSubItem";

// BaseSidebarProvider Component
export type BaseSidebarProviderProps = React.ComponentPropsWithoutRef<
  typeof SidebarProvider
>;
const BaseSidebarProvider = React.forwardRef<
  HTMLDivElement,
  BaseSidebarProviderProps
>(({ className, ...props }, ref) => {
  return <SidebarProvider ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarProvider.displayName = "BaseSidebarProvider";

// BaseSidebarRail Component
export type BaseSidebarRailProps = React.ComponentPropsWithoutRef<
  typeof SidebarRail
>;
const BaseSidebarRail = React.forwardRef<
  HTMLButtonElement,
  BaseSidebarRailProps
>(({ className, ...props }, ref) => {
  return <SidebarRail ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarRail.displayName = "BaseSidebarRail";

// BaseSidebarSeparator Component
export type BaseSidebarSeparatorProps = React.ComponentPropsWithoutRef<
  typeof SidebarSeparator
>;
const BaseSidebarSeparator = React.forwardRef<
  HTMLHRElement,
  BaseSidebarSeparatorProps
>(({ className, ...props }, ref) => {
  return (
    <SidebarSeparator ref={ref} className={cn("", className)} {...props} />
  );
});
BaseSidebarSeparator.displayName = "BaseSidebarSeparator";

// BaseSidebarTrigger Component
export type BaseSidebarTriggerProps = React.ComponentPropsWithoutRef<
  typeof SidebarTrigger
>;
const BaseSidebarTrigger = React.forwardRef<
  HTMLButtonElement,
  BaseSidebarTriggerProps
>(({ className, ...props }, ref) => {
  return <SidebarTrigger ref={ref} className={cn("", className)} {...props} />;
});
BaseSidebarTrigger.displayName = "BaseSidebarTrigger";

export {
  BaseSidebar,
  BaseSidebarContent,
  BaseSidebarFooter,
  BaseSidebarGroup,
  BaseSidebarGroupAction,
  BaseSidebarGroupContent,
  BaseSidebarGroupLabel,
  BaseSidebarHeader,
  BaseSidebarInput,
  BaseSidebarInset,
  BaseSidebarMenu,
  BaseSidebarMenuAction,
  BaseSidebarMenuBadge,
  BaseSidebarMenuButton,
  BaseSidebarMenuItem,
  BaseSidebarMenuSkeleton,
  BaseSidebarMenuSub,
  BaseSidebarMenuSubButton,
  BaseSidebarMenuSubItem,
  BaseSidebarProvider,
  BaseSidebarRail,
  BaseSidebarSeparator,
  BaseSidebarTrigger,
  useSidebar as useBaseSidebar,
};
