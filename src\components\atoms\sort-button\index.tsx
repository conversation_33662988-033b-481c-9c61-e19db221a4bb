'use client';

import { IconSort } from '@/assets/icons/IconSort';
import { cn } from '@/lib/utils';
import React, { useMemo } from 'react';

type Option = { label: string; value: string };

export type SortButtonProps = {
  options: Option[];
  value: string;
  onChange: (v: string) => void;
  className?: string;
};

export default function SortButton({
  options,
  value,
  onChange,
  className,
}: Readonly<SortButtonProps>) {
  const label = useMemo(
    () => options.find((o) => o.value === value)?.label ?? '',
    [options, value]
  );

  return (
    <div className={cn('relative w-full xl:w-fit', className)}>
      <div className="flex justify-center items-center gap-1 rounded-md border border-[#DEDEDE] bg-white px-4 h-11 min-w-[114px] text-sm font-medium text-[#3C3C3C]">
        <IconSort size={16} />
        <span>{label}</span>
      </div>

      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="absolute inset-0 opacity-0 cursor-pointer"
        aria-label="Urutkan"
      >
        {options.map((o) => (
          <option
            key={o.value}
            value={o.value}
          >
            {o.label}
          </option>
        ))}
      </select>
    </div>
  );
}
