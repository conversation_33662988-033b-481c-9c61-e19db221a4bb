"use client";

import * as React from "react";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";

type BaseSwitchProps = React.ComponentPropsWithoutRef<typeof Switch>;

const BaseSwitch = React.forwardRef<HTMLButtonElement, BaseSwitchProps>(
  ({ className, ...props }, ref) => {
    return (
      <Switch
        ref={ref}
        className={cn(
          "data-[state=checked]:bg-[#F7941E] px-1 py-3 w-10",
          className
        )}
        {...props}
      />
    );
  }
);

BaseSwitch.displayName = "BaseSwitch";

export { BaseSwitch };
