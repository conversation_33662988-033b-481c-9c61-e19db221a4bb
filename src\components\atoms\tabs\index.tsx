"use client";

import * as React from "react";
import {
  Tabs as ShadcnTabs,
  <PERSON><PERSON>List as ShadcnTabsList,
  TabsTrigger as ShadcnTabsTrigger,
  TabsContent as ShadcnTabsContent,
} from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

// BaseTabs Component
export type BaseTabsProps = React.ComponentPropsWithoutRef<typeof ShadcnTabs>;
const BaseTabs = React.forwardRef<HTMLDivElement, BaseTabsProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnTabs ref={ref} className={cn("w-full", className)} {...props} />
    );
  }
);
BaseTabs.displayName = "BaseTabs";

// BaseTabsList Component
export type BaseTabsListProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTabsList
>;
const BaseTabsList = React.forwardRef<HTMLDivElement, BaseTabsListProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnTabsList
        ref={ref}
        className={cn("bg-white rounded-md p-2", className)}
        {...props}
      />
    );
  }
);
BaseTabsList.displayName = "BaseTabsList";

// BaseTabsTrigger Component
export type BaseTabsTriggerProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTabsTrigger
>;
const BaseTabsTrigger = React.forwardRef<
  HTMLButtonElement,
  BaseTabsTriggerProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnTabsTrigger
      ref={ref}
      className={cn(
        "data-[state=active]:bg-[#F7941E] data-[state=active]:text-white",
        className
      )}
      {...props}
    />
  );
});
BaseTabsTrigger.displayName = "BaseTabsTrigger";

// BaseTabsContent Component
export type BaseTabsContentProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTabsContent
>;
const BaseTabsContent = React.forwardRef<HTMLDivElement, BaseTabsContentProps>(
  ({ className, ...props }, ref) => {
    return (
      <ShadcnTabsContent ref={ref} className={cn("", className)} {...props} />
    );
  }
);
BaseTabsContent.displayName = "BaseTabsContent";

export { BaseTabs, BaseTabsList, BaseTabsTrigger, BaseTabsContent };
