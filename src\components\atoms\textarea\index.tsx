'use client';

import * as React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

type BaseTextareaProps = React.ComponentPropsWithoutRef<typeof Textarea> & {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  /** Tampilkan counter karakter di bawah textarea (kanan bawah) */
  showCount?: boolean;
  /** Label untuk accessibility (opsional) */
  label?: React.ReactNode;
  /** Kustom className untuk wrapper luar */
  wrapperClassName?: string;
};

const BaseTextarea = React.forwardRef<
  HTMLTextAreaElement,
  Readonly<BaseTextareaProps>
>(
  (
    {
      className,
      wrapperClassName,
      label,
      leftIcon,
      rightIcon,
      showCount,
      onChange,
      value,
      defaultValue,
      maxLength,
      id,
      ...props
    },
    ref
  ) => {
    const [charCount, setCharCount] = React.useState<number>(() => {
      if (typeof value === 'string') return value.length;
      if (typeof defaultValue === 'string') return defaultValue.length;
      return 0;
    });

    // Sinkron saat controlled value berubah
    React.useEffect(() => {
      if (typeof value === 'string') setCharCount(value.length);
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCharCount(e.currentTarget.value.length);
      onChange?.(e);
    };

    return (
      <div className={cn('w-full', wrapperClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-1 block text-sm font-medium text-foreground"
          >
            {label}
          </label>
        )}

        {/* field wrapper untuk ikon di dalam textarea */}
        <div className="relative">
          {leftIcon && (
            <span
              className="pointer-events-none absolute left-3 top-3 text-gray-400"
              aria-hidden="true"
            >
              {leftIcon}
            </span>
          )}

          <Textarea
            id={id}
            ref={ref}
            className={cn(
              'bg-white',
              className,
              leftIcon && 'pl-10',
              rightIcon && 'pr-10'
            )}
            onChange={handleChange}
            value={value}
            defaultValue={defaultValue}
            maxLength={maxLength}
            {...props}
          />

          {rightIcon && (
            <span
              className="pointer-events-none absolute right-3 top-3 text-gray-400"
              aria-hidden="true"
            >
              {rightIcon}
            </span>
          )}
        </div>

        {/* counter di bawah kanan */}
        {showCount && (
          <div className="mt-1 flex items-center justify-end">
            <span
              aria-live="polite"
              className="text-xs text-muted-foreground select-none"
            >
              {maxLength ? `${charCount} / ${maxLength}` : charCount}
            </span>
          </div>
        )}
      </div>
    );
  }
);

BaseTextarea.displayName = 'BaseTextarea';

export { BaseTextarea };
