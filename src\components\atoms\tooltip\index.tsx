"use client";

import * as React from "react";
import {
  Tooltip as ShadcnTooltip,
  TooltipTrigger as ShadcnTooltipTrigger,
  TooltipContent as ShadcnTooltipContent,
  TooltipProvider as ShadcnTooltipProvider,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

// BaseTooltipProvider Component
export type BaseTooltipProviderProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTooltipProvider
>;
const BaseTooltipProvider = React.forwardRef<
  HTMLDivElement,
  BaseTooltipProviderProps
>(({ ...props }) => {
  return <ShadcnTooltipProvider {...props} />;
});
BaseTooltipProvider.displayName = "BaseTooltipProvider";

// BaseTooltip Component
export type BaseTooltipProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTooltip
>;
const BaseTooltip = React.forwardRef<HTMLDivElement, BaseTooltipProps>(
  (props) => {
    return <ShadcnTooltip {...props} />;
  }
);
BaseTooltip.displayName = "BaseTooltip";

// BaseTooltipTrigger Component
export type BaseTooltipTriggerProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTooltipTrigger
>;
const BaseTooltipTrigger = React.forwardRef<
  HTMLButtonElement,
  BaseTooltipTriggerProps
>(({ ...props }, ref) => {
  return <ShadcnTooltipTrigger ref={ref} {...props} />;
});
BaseTooltipTrigger.displayName = "BaseTooltipTrigger";

// BaseTooltipContent Component
export type BaseTooltipContentProps = React.ComponentPropsWithoutRef<
  typeof ShadcnTooltipContent
>;
const BaseTooltipContent = React.forwardRef<
  HTMLDivElement,
  BaseTooltipContentProps
>(({ className, ...props }, ref) => {
  return (
    <ShadcnTooltipContent
      ref={ref}
      className={cn("bg-stone-700", className)}
      {...props}
    />
  );
});
BaseTooltipContent.displayName = "BaseTooltipContent";

export {
  BaseTooltip,
  BaseTooltipTrigger,
  BaseTooltipContent,
  BaseTooltipProvider,
};
