'use client';

import React, { useEffect, useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BaseInput } from '@/components/atoms/input';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
import {
  notifyHotError,
  notifyHotSuccess,
} from '@/components/molecules/toast/hot-toast';
import OtpVerificationDialog from '@/components/molecules/account-settings/otp-verification-dialog';
import {
  changePasswordFormSchema,
  IChangePasswordBody,
  IChangePasswordForm,
  IVerifyPasswordBody,
} from '@/interfaces/admin/account-settings/user-account';
import {
  useChangePasswordUserAccountMutation,
  useVerifyPasswordUserAccountMutation,
} from '@/services/mutation/account-settings/user-account';
import { useGetUserAccountDetailQuery } from '@/services/query/account-settings/user-account';
import {
  useSendOtpMutation,
  useVerifyOtpMutation,
} from '@/services/mutation/login/mfa';
import { ISendOtpRequest, IVerifyOtpRequest } from '@/interfaces/user/mfa';
import { encryptAES } from '@/utils/common/encryption';
import { cn } from '@/lib/utils';

const ChangePassword: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [otp, setOtp] = useState('');
  const [otpError, setOtpError] = useState<string | undefined>(undefined);
  const [resendTimer, setResendTimer] = useState(0);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [otpKey, setOtpKey] = useState<string | null>(null);

  const { data: userDetailRes } = useGetUserAccountDetailQuery();
  const { mutateAsync: verifyPassword } =
    useVerifyPasswordUserAccountMutation();
  const { mutateAsync: changePassword } =
    useChangePasswordUserAccountMutation();
  const { mutateAsync: sendOtp, isPending: isSendOtpPending } =
    useSendOtpMutation();
  const { mutateAsync: verifyOtp } = useVerifyOtpMutation();

  const changePasswordForm = useForm<IChangePasswordForm>({
    resolver: yupResolver(changePasswordFormSchema),
    defaultValues: {
      old_password: '',
      new_password: '',
      new_password_confirm: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (!open || resendTimer <= 0) return;
    const t = setInterval(() => setResendTimer((s) => s - 1), 1000);
    return () => clearInterval(t);
  }, [open, resendTimer]);

  const cnpError = changePasswordForm.formState.errors.new_password?.message;
  const cnpValue = changePasswordForm.watch('new_password');

  let cnpHelper: React.ReactNode = null;
  if (cnpError) {
    cnpHelper = <p className="text-[#EA2B1F] text-xs mt-1">{cnpError}</p>;
  } else if (!cnpValue) {
    cnpHelper = (
      <p className="text-[#6B7280] text-xs mt-1">
        Password must be at least 8 characters and contain uppercase, lowercase
        letters, and numbers.
      </p>
    );
  }

  const onSubmitChangePassword: SubmitHandler<IChangePasswordForm> = async (
    data
  ) => {
    if (!userDetailRes?.data?.user_id || !userDetailRes?.data?.phone_number) {
      notifyHotError('Error', 'User ID atau nomor HP tidak ditemukan.');
      return;
    }

    try {
      changePasswordForm.clearErrors('old_password');

      const payloadVerifyPassword: IVerifyPasswordBody = {
        old_password: encryptAES(data.old_password),
      };

      try {
        const resVerifyPassword = await verifyPassword(payloadVerifyPassword);
        const verifiedOk = !!resVerifyPassword?.status;
        if (!verifiedOk) {
          changePasswordForm.setError('old_password', {
            type: 'server',
            message:
              resVerifyPassword?.message || 'Current password is incorrect.',
          });
          changePasswordForm.setFocus('old_password');
          return;
        }
      } catch (e: any) {
        const msg =
          e?.response?.data?.message ||
          e?.message ||
          'Current password is incorrect.';
        changePasswordForm.setError('old_password', {
          type: 'server',
          message: msg,
        });
        changePasswordForm.setFocus('old_password');
        return;
      }

      const payloadSendOtp: ISendOtpRequest = {
        user_id: userDetailRes.data.user_id,
        send_to: userDetailRes.data.phone_number,
        provider: 'phone',
        otp_type: 'changepass',
      };

      const res = await sendOtp(payloadSendOtp);
      const newKey =
        (res as any)?.data?.key ?? (res as any)?.data?.data?.key ?? null;
      if (!newKey) throw new Error('OTP key tidak ditemukan pada response.');

      setOtpKey(newKey);
      setOtp('');
      setOtpError(undefined);
      setResendTimer(90);
      setOpen(true);
      notifyHotSuccess('Success', res?.message || 'OTP has been sent');
    } catch (err: any) {
      const msg =
        err?.response?.data?.message || err?.message || 'Gagal mengirim OTP.';
      notifyHotError('Error', msg);
    }
  };

  return (
    <>
      <form
        onSubmit={changePasswordForm.handleSubmit(onSubmitChangePassword)}
        className="flex flex-col gap-4"
      >
        <p className="text-base font-semibold text-[#3C3C3C]">
          Change Password
        </p>

        <div>
          <BaseLabel
            htmlFor="old_password"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            Current Password
          </BaseLabel>
          <BaseInput
            id="old_password"
            type="password"
            className={cn(
              'w-full bg-[#FFFFFF] border text-sm rounded-md shadow-none focus:outline-none! focus:ring-0!',
              changePasswordForm.formState.errors.old_password
                ? 'border-red-500 focus:border-red-500!'
                : 'border-[#DEDEDE] focus:border-[#DEDEDE]!'
            )}
            {...changePasswordForm.register('old_password', {
              onChange: () => {
                if (
                  changePasswordForm.formState.errors.old_password?.type ===
                  'server'
                ) {
                  changePasswordForm.clearErrors('old_password');
                }
              },
            })}
          />
          {changePasswordForm.formState.errors.old_password && (
            <p className="mt-1 text-xs text-red-500">
              {changePasswordForm.formState.errors.old_password.message}
            </p>
          )}
        </div>

        <div>
          <BaseLabel
            htmlFor="new_password"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            New Password
          </BaseLabel>
          <BaseInput
            id="new_password"
            type="password"
            className={cn(
              'w-full bg-[#FFFFFF] border text-sm rounded-md shadow-none focus:outline-none! focus:ring-0!',
              changePasswordForm.formState.errors.new_password
                ? 'border-red-500 focus:border-red-500!'
                : 'border-[#DEDEDE] focus:border-[#DEDEDE]!'
            )}
            {...changePasswordForm.register('new_password')}
          />
          {cnpHelper}
        </div>

        <div>
          <BaseLabel
            htmlFor="new_password_confirm"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            Confirm New Password
          </BaseLabel>
          <BaseInput
            id="new_password_confirm"
            type="password"
            className={cn(
              'w-full bg-[#FFFFFF] border text-sm rounded-md shadow-none focus:outline-none! focus:ring-0!',
              changePasswordForm.formState.errors.new_password_confirm
                ? 'border-red-500 focus:border-red-500!'
                : 'border-[#DEDEDE] focus:border-[#DEDEDE]!'
            )}
            {...changePasswordForm.register('new_password_confirm')}
          />
          {changePasswordForm.formState.errors.new_password_confirm && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {changePasswordForm.formState.errors.new_password_confirm.message}
            </p>
          )}
        </div>

        <BaseButton
          type="submit"
          className="w-[160px] self-end bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
          disabled={
            isSendOtpPending || changePasswordForm.formState.isSubmitting
          }
        >
          {isSendOtpPending || changePasswordForm.formState.isSubmitting
            ? 'Update…'
            : 'Update Password'}
        </BaseButton>
      </form>

      <OtpVerificationDialog
        open={open}
        onClose={() => setOpen(false)}
        destination={userDetailRes?.data?.phone_number || ''}
        otp={otp}
        onChangeOtp={(val) => {
          setOtp(val);
          if (otpError) setOtpError(undefined);
        }}
        onVerify={async () => {
          setVerifyLoading(true);
          try {
            if (!otpKey) {
              setOtpError('OTP key tidak tersedia. Kirim ulang OTP.');
              return;
            }

            const payloadOtp: IVerifyOtpRequest = {
              otp,
              otp_type: 'changepass',
              key: otpKey,
            };

            const resVerifyOtp = await verifyOtp(payloadOtp);
            if (!resVerifyOtp.status) {
              notifyHotError(
                'Error',
                resVerifyOtp.message || 'OTP verification failed.'
              );
              return;
            }

            const vals = changePasswordForm.getValues();
            const payload: IChangePasswordBody = {
              old_password: encryptAES(vals.old_password),
              new_password: encryptAES(vals.new_password),
              new_password_confirm: encryptAES(vals.new_password_confirm),
            };
            const resChangePassword = await changePassword(payload);
            if (!resChangePassword.status) {
              notifyHotError(
                'Error',
                resChangePassword.message || 'Gagal mengubah password.'
              );
              return;
            }

            setOpen(false);
            setOtp('');
            setOtpKey(null);
            setResendTimer(0);
            notifyHotSuccess(
              'Success',
              'Your password has been changed successfully.'
            );
            changePasswordForm.reset();
          } catch (err: any) {
            const msg =
              err?.response?.data?.message ||
              err?.message ||
              'OTP invalid. Coba lagi.';
            setOtpError(msg);
          } finally {
            setVerifyLoading(false);
          }
        }}
        verifyLoading={verifyLoading}
        resendTimer={resendTimer}
        onResend={async () => {
          try {
            if (
              !userDetailRes?.data?.user_id ||
              !userDetailRes?.data?.phone_number
            ) {
              notifyHotError('Error', 'User ID atau nomor HP tidak ditemukan.');
              return;
            }
            const payload: ISendOtpRequest = {
              user_id: userDetailRes.data.user_id,
              send_to: userDetailRes.data.phone_number,
              provider: 'phone',
              otp_type: 'changepass',
            };
            const res = await sendOtp(payload);
            const newKey =
              (res as any)?.data?.key ?? (res as any)?.data?.data?.key ?? null;
            if (!newKey)
              throw new Error('OTP key tidak ditemukan pada response.');

            setOtpKey(newKey);
            setOtp('');
            setOtpError(undefined);
            setResendTimer(90);
            notifyHotSuccess('Success', 'Your OTP has been sent');
          } catch (err: any) {
            const msg =
              err?.response?.data?.message ||
              err?.message ||
              'Gagal mengirim ulang OTP.';
            setOtpError(msg);
            notifyHotError('Error', msg);
          }
        }}
        error={otpError}
      />
    </>
  );
};

export default ChangePassword;
