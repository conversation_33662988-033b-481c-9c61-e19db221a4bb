'use client';

import React, { useCallback } from 'react';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

type ListMenuDialogProps = {
  open: boolean;
  onClose: () => void;
  className?: string;
};

type NavItem = { label: string; href: string };

const NAV_ITEMS: NavItem[] = [
  { label: 'Homepage', href: '/homepage' },
  { label: 'Online Learning', href: '/online-learning' },
  { label: 'In-Class Training', href: '/in-class-training' },
  { label: 'Calendar Training', href: '/calendar-training' },
  { label: 'Career Path', href: '/career-path' },
  { label: 'Bucket Learning', href: '/bucket-learning' },
  { label: `Lemon's Leaderboard`, href: '/leaderboard' },
  { label: 'ACC Pedia', href: '/knowledge-center/acc-pedia' },
  { label: 'ACC Guava', href: '/knowledge-center/acc-guava' },
  { label: 'Forum', href: '/knowledge-center/forum' },
  { label: 'FAQ', href: '/knowledge-center/faq' },
];

export default function ListMenuDialog(props: Readonly<ListMenuDialogProps>) {
  const { open, onClose, className } = props;
  const router = useRouter();

  const handleRoute = useCallback(
    (href: string) => {
      onClose();
      router.push(href);
    },
    [onClose, router]
  );

  return (
    <BaseDialog
      open={open}
      onOpenChange={(v) => !v && onClose()}
    >
      <BaseDialogContent
        overlayClassName="bg-black/0"
        showCloseButton={false}
        id="mobile-nav-dialog"
        className={cn(
          'md:hidden !left-4 !right-auto !top-14 !translate-x-0 !translate-y-0',
          'w-[182px] p-0 rounded-lg',
          'bg-white border border-[#E5E7EB] shadow-[0px_4px_24px_0px_rgba(0,0,0,0.08)]',
          className
        )}
      >
        <BaseDialogTitle className="sr-only">Navigasi</BaseDialogTitle>

        <nav className="py-2">
          {NAV_ITEMS.map((item) => (
            <button
              key={item.href}
              type="button"
              onClick={() => handleRoute(item.href)}
              className="w-full text-left px-4 py-3 hover:bg-[#F3F4F6] cursor-pointer"
            >
              <span className="text-sm text-[#3C3C3C]">{item.label}</span>
            </button>
          ))}
        </nav>
      </BaseDialogContent>
    </BaseDialog>
  );
}
