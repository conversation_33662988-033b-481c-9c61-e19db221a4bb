'use client';

import * as React from 'react';
import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogFooter,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { cn } from '@/lib/utils';
import OtpInput from '../login/otp-input';

type OtpVerificationDialogProps = {
  open: boolean;
  onClose: () => void;
  destination: string;
  otp: string;
  onChangeOtp: (val: string) => void;
  onVerify: () => void;
  verifyLoading?: boolean;
  resendTimer: number;
  onResend: () => void;
  error?: string;
  className?: string;
};

function formatTimer(seconds: number) {
  const mm = Math.floor(seconds / 60)
    .toString()
    .padStart(2, '0');
  const ss = (seconds % 60).toString().padStart(2, '0');
  return `${mm}:${ss}`;
}

const OtpVerificationDialog: React.FC<OtpVerificationDialogProps> = ({
  open,
  onClose,
  destination,
  otp,
  onChangeOtp,
  onVerify,
  verifyLoading,
  resendTimer,
  onResend,
  error,
  className,
}) => {
  const resendDisabled = resendTimer > 0;
  const resendLabel = resendDisabled
    ? `Resend code in ${formatTimer(resendTimer)}`
    : 'Resend code';

  return (
    <BaseDialog
      open={open}
      onOpenChange={(val) => !val && onClose()}
    >
      <BaseDialogContent
        className={cn(
          'fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
          'w-[min(560px,calc(100vw-2rem))] rounded-2xl p-0!',
          'max-h-[calc(100dvh-2rem)] overflow-auto gap-0',
          className
        )}
        style={{ paddingBottom: 'max(env(safe-area-inset-bottom), 16px)' }}
      >
        <BaseDialogHeader className="md:text-center border-b border-[#DEDEDE]">
          <BaseDialogTitle className="text-[#3C3C3C] text-left font-medium py-4 px-5 md:py-5">
            OTP Verification
          </BaseDialogTitle>
        </BaseDialogHeader>

        <div className="p-5 md:p-9 flex flex-col gap-3 md:gap-6">
          <BaseDialogDescription className="text-sm text-[#3C3C3C] self-center text-center py-1 px-4">
            We have sent an OTP Code to{' '}
            <span className="font-bold text-foreground">{destination}</span>
            .
            <br />
            Please check your message and input the OTP Code here
          </BaseDialogDescription>

          <div>
            <OtpInput
              value={otp}
              onChange={onChangeOtp}
              error={error}
              isMobileQuery="(max-width: 767px)"
            />
            {error ? (
              <p className="text-[#EA2B1F] text-xs md:text-sm mt-3 md:mt-6 text-center md:text-left">
                {error}
              </p>
            ) : null}
          </div>
        </div>

        <BaseDialogFooter className="py-4 px-5 flex flex-row gap-3 justify-center md:justify-end border-t border-[#DEDEDE]">
          <BaseButton
            type="button"
            disabled={resendDisabled}
            onClick={onResend}
            className={cn(
              'h-11 md:h-11 text-xs rounded-md bg-white border text-[#3C3C3C]',
              'px-4 md:px-5 w-full max-w-[145.5px]',
              resendDisabled
                ? 'bg-[#0202020A] border-[#A4A4A4] text-[#A4A4A4] cursor-not-allowed md:max-w-[169px]'
                : 'border-[#DEDEDE] hover:bg-[#0202020A] hover:opacity-80 cursor-pointer md:max-w-[115px]'
            )}
          >
            {resendLabel}
          </BaseButton>

          <BaseButton
            type="button"
            onClick={onVerify}
            disabled={verifyLoading}
            className={cn(
              'h-11 md:h-11 text-xs rounded-md bg-[#F7941E] text-white px-4 md:px-5 w-full max-w-[145.5px] md:max-w-[141px]',
              'hover:bg-[#F7941E] hover:opacity-80 cursor-pointer'
            )}
          >
            {verifyLoading ? 'Verifying...' : 'Verify OTP Code'}
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default OtpVerificationDialog;
