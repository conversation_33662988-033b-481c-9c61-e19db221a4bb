'use client';

import React, { useCallback } from 'react';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import {
  BaseAvatar,
  BaseAvatarImage,
  BaseAvatarFallback,
} from '@/components/atoms/avatar';
import { cn } from '@/lib/utils';
import { getInitials } from '@/utils/common/getInitials';
import { IconUser } from '@/assets/icons/IconUser';
import { IconBookOpen } from '@/assets/icons/IconBookOpen';
import { IconSignout } from '@/assets/icons/IconSignout';
import { IconGear } from '@/assets/icons/IconGear';
import { useRouter } from 'next/navigation';
import { useLogoutUserMutation } from '@/services/mutation/login/user';

type ProfileMenuDialogProps = {
  open: boolean;
  onClose: () => void;
  user: { name: string; role?: string; avatarUrl?: string };
  switchAppLabel?: string;
  layout?: 'anchored' | 'centered';
  className?: string;
};

export default function ProfileMenuDialog(
  props: Readonly<ProfileMenuDialogProps>
) {
  const {
    open,
    onClose,
    user,
    switchAppLabel = 'Lemon - Admin',
    layout = 'anchored',
    className,
  } = props;

  const router = useRouter();
  const { mutateAsync: logout } = useLogoutUserMutation();

  const onclickGoButton = useCallback(
    (url: string) => {
      onClose();
      router.push(url);
    },
    [onClose, router]
  );

  const clickThenClose = (fn?: () => void) => async () => {
    await logout();
    fn?.();
    onClose();
  };

  return (
    <BaseDialog
      open={open}
      onOpenChange={(v) => !v && onClose()}
    >
      <BaseDialogContent
        overlayClassName="bg-black/0"
        onEscapeKeyDown={() => onClose()}
        showCloseButton={false}
        id="profile-menu-dialog"
        className={cn(
          layout === 'anchored'
            ? '!left-auto !right-4 !top-16 md:!right-10 md:!top-24 !translate-x-0 !translate-y-0'
            : '',
          'w-full max-w-[280px] [@media(min-width:375px)]:max-w-[330px] p-0 rounded-md gap-3',
          'bg-white border-none shadow-[0px_0px_12px_0px_rgba(60,60,60,0.05)]',
          className
        )}
      >
        <BaseDialogTitle className="sr-only">Profile menu</BaseDialogTitle>

        <BaseDialogHeader className="flex py-3 pt-5 pl-4 gap-3">
          <div className="flex items-center gap-3">
            <BaseAvatar className="w-10 h-10">
              {user.avatarUrl ? (
                <BaseAvatarImage
                  src={user.avatarUrl}
                  alt={user.name}
                />
              ) : (
                <BaseAvatarFallback>
                  {getInitials(user.name)}
                </BaseAvatarFallback>
              )}
            </BaseAvatar>
            <div className="min-w-0">
              <p className="text-sm text-[#3C3C3C] truncate">{user.name}</p>
              {user.role && (
                <p className="text-[10px] leading-[14px] text-[#767676] truncate">
                  {user.role}
                </p>
              )}
            </div>
          </div>
        </BaseDialogHeader>

        <div className="md:pb-2 flex flex-col gap-1 bg-white">
          <button
            type="button"
            onClick={() => onclickGoButton('/profile-dashboard')}
            className="w-full px-4 py-1 flex flex-row gap-3 items-center hover:bg-[#DEDEDE] cursor-pointer"
          >
            <IconUser />
            <p className="text-sm text-[#3C3C3C]">Profile Dashboard</p>
          </button>
          <button
            type="button"
            onClick={() => onclickGoButton('/account-settings')}
            className="w-full px-4 py-1 flex flex-row gap-3 items-center hover:bg-[#DEDEDE] cursor-pointer"
          >
            <IconGear />
            <span className="text-sm text-[#3C3C3C]">Account Setting</span>
          </button>
          <button
            type="button"
            onClick={() => onclickGoButton('/admin')}
            className="w-full px-4 py-1 flex flex-row gap-3 items-center hover:bg-[#DEDEDE] cursor-pointer"
          >
            <IconBookOpen />
            <p className="text-sm text-[#3C3C3C]">{switchAppLabel}</p>
          </button>
        </div>

        <div className="md:hidden pt-1 pb-2 border-t border-[#DEDEDE] bg-white rounded-b-md">
          <button
            type="button"
            onClick={clickThenClose(() => onclickGoButton('/login'))}
            className="w-full flex justify-start px-4 py-2 gap-3 bg-white rounded-none border-none hover:bg-[#DEDEDE] cursor-pointer"
          >
            <IconSignout
              size={20}
              color="#D3483E"
              rotate={180}
            />
            <span className="text-sm text-[#D3483E]">Log Out</span>
          </button>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
}
