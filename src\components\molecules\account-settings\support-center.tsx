'use client';

import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
import { BaseTextarea } from '@/components/atoms/textarea';
import {
  notifyHotError,
  notifyHotSuccess,
} from '@/components/molecules/toast/hot-toast';
import { useCustomerServiceMutation } from '@/services/mutation/account-settings/customer-support';
import {
  customerSupportFormSchema,
  ICustomerServiceBody,
  ICustomerSupportForm,
} from '@/interfaces/admin/account-settings/customer-support';
import { cn } from '@/lib/utils';

const SupportCenter: React.FC = () => {
  const { mutateAsync: sendCustomerService, isPending: isSendingTicket } =
    useCustomerServiceMutation();

  const supportTicketForm = useForm<ICustomerSupportForm>({
    resolver: yup<PERSON><PERSON><PERSON>ver(customerSupportFormSchema),
    defaultValues: { message: '' },
    mode: 'onChange',
  });

  const onSubmitSupportTicket: SubmitHandler<ICustomerSupportForm> = async (
    data
  ) => {
    try {
      const body: ICustomerServiceBody = { message: data.message };
      const sendData = await sendCustomerService(body);
      notifyHotSuccess('Success', `${sendData.message}`);
      supportTicketForm.reset({ message: '' });
    } catch (err: any) {
      const msg =
        err?.response?.data?.message ||
        err?.message ||
        'Failed to submit support ticket. Please try again.';
      notifyHotError('Error', msg);
    }
  };

  return (
    <form
      onSubmit={supportTicketForm.handleSubmit(onSubmitSupportTicket)}
      className="flex flex-col gap-4"
    >
      <p className="text-base font-semibold text-[#3C3C3C]">Support Center</p>
      <div>
        <BaseLabel
          htmlFor="issue"
          className="text-xs font-medium text-[#3C3C3C] mb-1"
        >
          Tell us your issue
        </BaseLabel>
        <BaseTextarea
          id="message"
          placeholder="Input your issue here..."
          maxLength={100}
          showCount
          className={cn(
            'w-full min-h-[100px] bg-[#FFFFFF] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none focus:outline-none! focus:ring-0!',
            supportTicketForm.formState.errors.message
              ? 'border-red-500 focus:border-red-500!'
              : 'border-[#DEDEDE] focus:border-[#DEDEDE]!'
          )}
          {...supportTicketForm.register('message')}
        />
        {supportTicketForm.formState.errors.message && (
          <p className="text-[#EA2B1F] text-xs mt-1">
            {supportTicketForm.formState.errors.message.message}
          </p>
        )}
      </div>
      <BaseButton
        type="submit"
        className="w-[160px] self-end bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
        disabled={isSendingTicket || supportTicketForm.formState.isSubmitting}
      >
        {isSendingTicket || supportTicketForm.formState.isSubmitting
          ? 'Submitting…'
          : 'Submit'}
      </BaseButton>
    </form>
  );
};

export default SupportCenter;
