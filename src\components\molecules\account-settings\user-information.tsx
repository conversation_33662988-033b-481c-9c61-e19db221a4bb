'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BaseInput } from '@/components/atoms/input';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
import {
  notifyHotError,
  notifyHotSuccess,
} from '@/components/molecules/toast/hot-toast';
import {
  BaseAvatar,
  BaseAvatarFallback,
  BaseAvatarImage,
} from '@/components/atoms/avatar';
import { getInitials } from '@/utils/common/getInitials';
import { PencilIcon, XIcon } from 'lucide-react';
import { useGetUserAccountDetailQuery } from '@/services/query/account-settings/user-account';
import { useUpdateUserAccountMutation } from '@/services/mutation/account-settings/user-account';
import {
  getUserAccountFormSchema,
  IGetUserAccountForm,
} from '@/interfaces/admin/account-settings/user-account';
import { useGetFileQuery } from '@/services/query/file/get';
import { bufferToFile } from '@/utils/common/file';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ACCEPT_TYPES = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'];

const guessMimeFromName = (name: string) => {
  const n = name.toLowerCase();
  if (n.endsWith('.png')) return 'image/png';
  if (n.endsWith('.jpg') || n.endsWith('.jpeg')) return 'image/jpeg';
  if (n.endsWith('.webp')) return 'image/webp';
  return 'image/png';
};

const UserInformation: React.FC = () => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [serverAvatarUrl, setServerAvatarUrl] = useState<string | null>(null);

  const { data: userDetailRes, refetch: refetchUserDetail } =
    useGetUserAccountDetailQuery();
  const { mutateAsync: updateAccount, isPending: isUpdatingAccount } =
    useUpdateUserAccountMutation();

  const userInfoForm = useForm<IGetUserAccountForm>({
    resolver: yupResolver(getUserAccountFormSchema),
    mode: 'onChange',
    defaultValues: {
      name: '',
      npk: '',
      job_name: '',
      entity_name: '',
      email: '',
      second_email: '',
      phone_number: '',
    },
  });

  useEffect(() => {
    if (!userDetailRes?.data) return;
    userInfoForm.reset({
      name: userDetailRes?.data.name ?? '',
      npk: userDetailRes?.data.npk ?? '',
      job_name: userDetailRes?.data.job_name ?? '',
      entity_name: userDetailRes?.data.entity_name ?? '',
      email: userDetailRes?.data.email ?? '',
      second_email: userDetailRes?.data.second_email ?? '',
      phone_number: userDetailRes?.data.phone_number ?? '',
    });
  }, [userDetailRes?.data, userInfoForm]);

  const avatarPath = userDetailRes?.data?.avatar ?? '';
  const image = useGetFileQuery({ path: avatarPath });

  useEffect(() => {
    if (!avatarPath) {
      setServerAvatarUrl(null);
      return;
    }
    if (image.data) {
      const mime = guessMimeFromName(avatarPath);
      const file = bufferToFile(image.data, avatarPath, mime);
      const url = URL.createObjectURL(file);
      setServerAvatarUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return url;
      });
    }
    return () => {
      setServerAvatarUrl((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return null;
      });
    };
  }, [avatarPath, image.data]);

  const openAvatarPicker = () => fileInputRef.current?.click();

  const handlePickAvatar: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;

    if (f.size > MAX_FILE_SIZE) {
      notifyHotError(
        'Error',
        `Max avatar size is ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
      e.target.value = '';
      return;
    }
    if (!ACCEPT_TYPES.includes(f.type)) {
      notifyHotError('Error', 'Only PNG/JPG/JPEG/WEBP supported');
      e.target.value = '';
      return;
    }

    setAvatarFile(f);
    const url = URL.createObjectURL(f);
    setAvatarPreview((prev) => {
      if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
      return url;
    });

    e.target.value = '';
  };

  const handleUpdateUserInformation = async (formData: FormData) => {
    try {
      const updated = await updateAccount(formData);
      setAvatarFile(null);
      setAvatarPreview((prev) => {
        if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
        return null;
      });
      if (fileInputRef.current) fileInputRef.current.value = '';

      refetchUserDetail();

      notifyHotSuccess('Success', `${updated.message}`);
    } catch (err: any) {
      const msg =
        err?.response?.data?.message ||
        err?.message ||
        'Failed to save changes. Please try again.';
      notifyHotError('Error', msg);
    }
  };

  const onSubmitUserInfo: SubmitHandler<IGetUserAccountForm> = async (data) => {
    try {
      const formData = new FormData();
      if (avatarFile) formData.append('avatar', avatarFile, avatarFile.name);
      formData.append(
        'data',
        JSON.stringify({ second_email: data.second_email })
      );
      await handleUpdateUserInformation(formData);
    } catch {
      notifyHotError('Error', 'Failed to save changes. Please try again.');
    }
  };

  const avatarNode = (() => {
    if (avatarPreview) return <BaseAvatarImage src={avatarPreview} />;
    if (serverAvatarUrl) return <BaseAvatarImage src={serverAvatarUrl} />;
    return (
      <BaseAvatarFallback>
        {getInitials(userInfoForm.getValues('name') ?? '')}
      </BaseAvatarFallback>
    );
  })();

  return (
    <div className="flex flex-col gap-4">
      <p className="text-base font-semibold text-[#3C3C3C]">User Information</p>

      <form
        onSubmit={userInfoForm.handleSubmit(onSubmitUserInfo)}
        className="flex flex-col gap-4"
      >
        <div className="self-center relative">
          <BaseAvatar className="w-20 h-20 shadow-lg shadow-black/10 ring-1 ring-black/5">
            {avatarNode}
          </BaseAvatar>

          <button
            type="button"
            onClick={openAvatarPicker}
            className="absolute right-0 bottom-0 p-1 bg-white rounded-full shadow-md shadow-black/20 ring-1 ring-black/5 transition-shadow cursor-pointer"
            title="Change avatar"
          >
            <PencilIcon
              size={20}
              color="#3C3C3C"
            />
          </button>

          {avatarPreview && (
            <button
              type="button"
              onClick={() => {
                setAvatarFile(null);
                setAvatarPreview((prev) => {
                  if (prev?.startsWith('blob:')) URL.revokeObjectURL(prev);
                  return null;
                });
              }}
              className="absolute -right-2 top-0 p-1 bg-white rounded-full shadow-md shadow-black/20 ring-1 ring-black/5 cursor-pointer"
              title="Cancel selected avatar"
            >
              <XIcon size={16} />
            </button>
          )}

          <BaseInput
            ref={(el) => (fileInputRef.current = el as any)}
            id="avatar"
            type="file"
            accept={ACCEPT_TYPES.join(',')}
            className="sr-only"
            onChange={handlePickAvatar}
          />
        </div>

        <div>
          <BaseLabel
            htmlFor="name"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            Full Name
          </BaseLabel>
          <BaseInput
            id="name"
            type="text"
            disabled
            className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
            {...userInfoForm.register('name')}
          />
        </div>

        <div className="flex flex-col xl:flex-row w-full gap-5">
          <div className="w-full">
            <BaseLabel
              htmlFor="npk"
              className="text-xs font-medium text-[#3C3C3C] mb-1"
            >
              NPK
            </BaseLabel>
            <BaseInput
              id="npk"
              type="text"
              disabled
              className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
              {...userInfoForm.register('npk')}
            />
          </div>
          <div className="w-full">
            <BaseLabel
              htmlFor="job_name"
              className="text-xs font-medium text-[#3C3C3C] mb-1"
            >
              Job Position
            </BaseLabel>
            <BaseInput
              id="job_name"
              type="text"
              disabled
              className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
              {...userInfoForm.register('job_name')}
            />
          </div>
        </div>

        <div>
          <BaseLabel
            htmlFor="entity_name"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            Entity
          </BaseLabel>
          <BaseInput
            id="entity_name"
            type="text"
            disabled
            className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
            {...userInfoForm.register('entity_name')}
          />
        </div>

        <div className="flex flex-col xl:flex-row w-full gap-5">
          <div className="w-full">
            <BaseLabel
              htmlFor="email"
              className="text-xs font-medium text-[#3C3C3C] mb-1"
            >
              Email
            </BaseLabel>
            <BaseInput
              id="email"
              type="text"
              disabled
              className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
              {...userInfoForm.register('email')}
            />
          </div>

          <div className="w-full">
            <BaseLabel
              htmlFor="second_email"
              className="text-xs font-medium text-[#3C3C3C] mb-1"
            >
              Secondary Email
            </BaseLabel>
            <BaseInput
              id="second_email"
              type="email"
              className="w-full bg-[#FFFFFF] border border-[#DEDEDE] text-sm text-[#3C3C3C] rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
              {...userInfoForm.register('second_email')}
            />
            {userInfoForm.formState.errors.second_email && (
              <p className="mt-1 text-xs text-red-500">
                {userInfoForm.formState.errors.second_email.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <BaseLabel
            htmlFor="phone_number"
            className="text-xs font-medium text-[#3C3C3C] mb-1"
          >
            Phone Number
          </BaseLabel>
          <BaseInput
            id="phone_number"
            type="text"
            disabled
            className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
            {...userInfoForm.register('phone_number')}
          />
          <p className="text-xs text-[#767676] mt-1">
            Anda dapat mengubah nomor telepon Anda melalui Talent.
          </p>
        </div>

        <BaseButton
          type="submit"
          className="w-[160px] self-end bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
          disabled={isUpdatingAccount || userInfoForm.formState.isSubmitting}
        >
          {isUpdatingAccount || userInfoForm.formState.isSubmitting
            ? 'Saving…'
            : 'Save Changes'}
        </BaseButton>
      </form>
    </div>
  );
};

export default UserInformation;
