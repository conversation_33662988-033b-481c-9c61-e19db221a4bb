'use client';

import React, { useState } from 'react';
import {
  BaseAvatar,
  BaseAvatarFallback,
  BaseAvatarImage,
} from '@/components/atoms/avatar';
import Image from 'next/image';
import ProfileMenuDialogAdmin from './profile-menu-dialog';

const dummyUsers = {
  name: '<PERSON>',
  role: 'UI/UX Designer',
};

const AdminNavbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <div className="relative bg-[#0F0F0F] w-full h-15 text-white flex justify-between px-4">
      <div className="flex justify-center items-center">
        <Image
          alt="logo"
          src="/icons/lemon-alt.svg"
          width={50}
          height={50}
          className="h-8"
        />
        <span className="text-lg font-semibold">
          Lemon Content Management System
        </span>
      </div>
      <div className="flex justify-center items-center gap-3">
        <span>{dummyUsers.name}</span>
        <button
          type="button"
          className="cursor-pointer"
          onClick={() => setMenuOpen(true)}
          aria-haspopup="dialog"
          aria-expanded={menuOpen}
          aria-controls="profile-menu-dialog"
        >
          <BaseAvatar>
            <BaseAvatarImage src="https://github.com/shadcn.png" />
            <BaseAvatarFallback>CN</BaseAvatarFallback>
          </BaseAvatar>
        </button>
      </div>

      <ProfileMenuDialogAdmin
        open={menuOpen}
        onClose={() => setMenuOpen(false)}
        user={{
          name: dummyUsers.name,
          role: dummyUsers.role,
          avatarUrl: 'https://github.com/shadcn.png',
        }}
        className="absolute h-fit z-[60]"
      />
    </div>
  );
};

export default AdminNavbar;
