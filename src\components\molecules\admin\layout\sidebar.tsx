'use client';

import { useMemo, useState, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

import {
  BaseSidebar,
  BaseSidebarContent,
  BaseSidebarGroup,
  BaseSidebarGroupContent,
  BaseSidebarMenu,
  BaseSidebarMenuButton,
  BaseSidebarMenuItem,
} from '@/components/atoms/sidebar';

type MenuItem = {
  title: string;
  url: string;
  children?: MenuItem[];
};

const basePath = '/admin';

const joinPath = (...parts: string[]) =>
  parts
    .join('/')
    .replace(/\/{2,}/g, '/')
    .replace(/\/$/, '') || '/';

const items: MenuItem[] = [
  { title: 'Dashboard', url: '/' },
  {
    title: 'Learning Preparation',
    url: '#',
    children: [
      {
        title: 'Manage Learning Path',
        url: '/learning-preparation/manage-learning-path',
      },
      {
        title: 'Manage Materials',
        url: '/learning-preparation/manage-materials',
      },
      { title: 'Manage Test', url: '/learning-preparation/manage-test' },
      { title: 'Manage Section', url: '/learning-preparation/manage-section' },
    ],
  },
  {
    title: 'Online Learning',
    url: '#',
    children: [
      { title: 'Manage Module', url: '/online-learning/manage-module' },
      {
        title: 'Module Certificate',
        url: '/online-learning/module-certificate',
      },
      {
        title: 'Assignment and Test Grading',
        url: '/online-learning/assignment-grading',
      },
      {
        title: 'Assignment and Test Report',
        url: '/online-learning/assignment-report',
      },
      {
        title: 'Learning History Report',
        url: '/online-learning/learning-history',
      },
      {
        title: 'Training History Report',
        url: '/online-learning/training-history',
      },
    ],
  },
  {
    title: 'Bucket Learning',
    url: '#',
    children: [
      { title: 'Overview', url: '/bucket-learning/overview' },
      { title: 'Admin Assignment', url: '/bucket-learning/admin-assignment' },
      {
        title: 'Manager Assignment',
        url: '/bucket-learning/manager-assignment',
      },
      { title: 'Competencies', url: '/bucket-learning/competencies' },
    ],
  },
  {
    title: 'In-Class Training',
    url: '#',
    children: [
      { title: 'Manage Class', url: '/in-class-training/manage-class' },
      {
        title: 'Manage Participant',
        url: '/in-class-training/manage-participant',
      },
      { title: 'Manage Section', url: '/in-class-training/manage-section' },
      { title: 'Manage Materials', url: '/in-class-training/manage-materials' },
      { title: 'Manage Test', url: '/in-class-training/manage-test' },
    ],
  },
  {
    title: 'Master Data',
    url: '#',
    children: [
      { title: 'Manage Job Position', url: '/master-data/manage-job-position' },
      { title: 'Master Module', url: '/master-data/master-module' },
      {
        title: 'Manage Category & Sub Category',
        url: '/master-data/manage-category',
      },
      { title: 'Manage Branch Grade', url: '/master-data/manage-branch-grade' },
      { title: 'Log User Login', url: '/master-data/log-user-login' },
    ],
  },
];

function hasActiveDescendant(item: MenuItem, currentPath: string): boolean {
  const full = joinPath(basePath, item.url);
  if (full !== joinPath(basePath, '#') && currentPath === full) return true;
  return (item.children ?? []).some((c) => hasActiveDescendant(c, currentPath));
}

/** Toggle button terpisah untuk menurunkan level nesting & hindari inline lambda */
function ToggleButton({
  title,
  depth,
  isOpen,
  className,
  onToggle,
}: Readonly<{
  title: string;
  depth: number;
  isOpen: boolean;
  className?: string;
  onToggle: (e: React.MouseEvent<HTMLButtonElement>) => void;
}>) {
  const labelIndentStyle =
    depth > 0
      ? { paddingLeft: depth * 20, paddingRight: depth * 20 }
      : undefined;

  const id = `${title.toLowerCase().replace(/\s+/g, '-')}-${depth}`;

  return (
    <button
      type="button"
      data-title={title}
      onClick={onToggle}
      className={className}
      aria-expanded={isOpen}
      aria-controls={id}
    >
      <span style={labelIndentStyle}>{title}</span>
      {isOpen ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
    </button>
  );
}

const AdminBaseSidebar = () => {
  const pathname = usePathname();

  const defaultOpen = useMemo(() => {
    const state: Record<string, boolean> = {};
    items.forEach((it) => {
      if (it.children?.length)
        state[it.title] = hasActiveDescendant(it, pathname);
    });
    return state;
  }, [pathname]);

  const [open, setOpen] = useState<Record<string, boolean>>(defaultOpen);

  const handleToggle = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    const key = e.currentTarget.dataset.title as string;
    if (!key) return;
    setOpen((s) => ({ ...s, [key]: !s[key] }));
  }, []);

  const renderTree = (nodes: MenuItem[], depth = 0): React.ReactNode[] =>
    nodes.map((item) => {
      const fullHref = item.url === '#' ? '#' : joinPath(basePath, item.url);

      const isSelfActive = item.url !== '#' && pathname === fullHref;
      const isBranchActive =
        !isSelfActive &&
        (item.children ?? []).some((c) => hasActiveDescendant(c, pathname));

      const isParent = !!item.children?.length;
      const isOpen = isParent ? open[item.title] ?? isBranchActive : false;
      const isChild = depth > 0;

      const baseButton =
        'flex w-full items-center justify-between gap-2 px-0 py-3 rounded-lg border-none transition-colors cursor-pointer';
      const baseText = 'text-[#767676]';

      const parentStateClasses = cn(
        baseText,
        'hover:!text-white hover:!bg-[#3C3C3C14] hover:!rounded-md',
        (isSelfActive || isBranchActive) &&
          '!text-white !bg-[#3C3C3C14] !rounded-md'
      );

      const childStateClasses = cn(
        baseText,
        'hover:!text-white hover:!bg-[#303030] hover:!rounded-md',
        isSelfActive && '!text-white !bg-[#303030] !rounded-md'
      );

      const topLeafStateClasses = cn(
        baseText,
        'hover:!text-white hover:!bg-[#3C3C3C14] hover:!rounded-md',
        isSelfActive && '!text-white !bg-[#3C3C3C14] !rounded-md'
      );

      let stateClasses;
      if (isParent) {
        stateClasses = parentStateClasses;
      } else if (isChild) {
        stateClasses = childStateClasses;
      } else {
        stateClasses = topLeafStateClasses;
      }

      const labelIndentStyle =
        depth > 0
          ? { paddingLeft: depth * 20, paddingRight: depth * 20 }
          : undefined;

      const key = `${fullHref}-${depth}-${item.title}`;

      return (
        <BaseSidebarMenuItem key={key}>
          <BaseSidebarMenuButton
            asChild
            className="h-fit !overflow-visible [&>span:last-child]:!whitespace-normal [&>span:last-child]:!break-words [&>span:last-child]:!text-clip text-sm"
          >
            {isParent ? (
              <ToggleButton
                title={item.title}
                depth={depth}
                isOpen={isOpen}
                className={cn(baseButton, stateClasses)}
                onToggle={handleToggle}
              />
            ) : (
              <Link
                href={fullHref}
                className={cn(baseButton, stateClasses)}
                aria-current={isSelfActive ? 'page' : undefined}
              >
                <span style={labelIndentStyle}>{item.title}</span>
              </Link>
            )}
          </BaseSidebarMenuButton>

          {isParent && isOpen && (
            <BaseSidebarMenu
              id={`${item.title.toLowerCase().replace(/\s+/g, '-')}-${depth}`}
              className="mt-1"
            >
              {renderTree(item.children!, depth + 1)}
            </BaseSidebarMenu>
          )}
        </BaseSidebarMenuItem>
      );
    });

  return (
    <BaseSidebar
      variant="sidebar"
      collapsible="none"
      className="bg-[#1E1E1E] w-full max-w-[220px] h-screen overflow-hidden"
    >
      <BaseSidebarContent className="w-[220px] p-4 py-5 h-full overflow-hidden">
        <BaseSidebarGroup className="p-0 h-full flex flex-col min-h-0">
          <BaseSidebarGroupContent className="flex-1 min-h-0">
            <BaseSidebarMenu className="h-full overflow-y-auto pr-1">
              {renderTree(items)}
            </BaseSidebarMenu>
          </BaseSidebarGroupContent>
        </BaseSidebarGroup>
      </BaseSidebarContent>
    </BaseSidebar>
  );
};

export default AdminBaseSidebar;
