"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import ManageCategoryNewModal from "./new-modal";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";

const ManageCategoryTableHeaderFilter = () => {
  const { activeTab } = useManageCategoryTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );

  const { setOpenAddModal } = useManageCategoryModal(
    useShallow(({ setOpenAddModal }) => ({
      setOpenAddModal,
    }))
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton className="h-12 px-5" onClick={() => setOpenAddModal(true)}>
        <div className="flex items-center gap-2">
          <Plus />
          {activeTab === "category" ? "Add Category" : "Add Sub Category"}
        </div>
      </BaseButton>
      <ManageCategoryNewModal />
    </div>
  );
};

export default ManageCategoryTableHeaderFilter;
