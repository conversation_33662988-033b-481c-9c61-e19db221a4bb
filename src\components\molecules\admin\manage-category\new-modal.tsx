import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";
import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";
import {
  createCategoryBodySchema,
  createSubCategoryBodySchema,
  ICreateCategoryBody,
  ICreateSubCategoryBody,
} from "@/interfaces/admin/manage-category/new";
import {
  useInsertCategoryMutation,
  useInsertSubCategoryMutation,
  useUpdateCategoryMutation,
  useUpdateSubCategoryMutation,
} from "@/services/mutation/admin/manage-category";
import { useGetListCategoryQuery } from "@/services/query/admin/manage-category";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import { yupResolver } from "@hookform/resolvers/yup";
import React from "react";
import { useForm, useFormContext, Path, FormProvider } from "react-hook-form";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const ManageCategoryNewModal = () => {
  const {
    openAddModal,
    openedCategory,
    openedSubCategory,
    setOpenAddModal,
    setOpenedCategory,
    setOpenedSubCategory,
  } = useManageCategoryModal(
    useShallow(
      ({
        openAddModal,
        openedCategory,
        openedSubCategory,
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
      }) => ({
        openAddModal,
        openedCategory,
        openedSubCategory,
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
      })
    )
  );

  const activeTab = useManageCategoryTabStore((state) => state.activeTab);

  const title = activeTab === "category" ? "Add Category" : "Add Sub Category";
  const Modal = activeTab === "category" ? NewCategoryForm : NewSubCategoryForm;

  const handleOpenChangeModal = (state: boolean) => {
    if (!state) {
      setOpenedCategory(null);
      setOpenedSubCategory(null);
    }

    setOpenAddModal(state);
  };

  return (
    <BaseDialog open={openAddModal} onOpenChange={handleOpenChangeModal}>
      <BaseDialogContent>
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{title}</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <Modal
          isOpen={openAddModal}
          data={openedCategory || openedSubCategory}
          onCloseModal={() => handleOpenChangeModal(false)}
        />
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: ICategory | ISubCategory | null;
  onCloseModal: VoidFunction;
}

const NewCategoryForm = ({ data, isOpen, onCloseModal }: IFormProps) => {
  const form = useForm({
    resolver: yupResolver(createCategoryBodySchema),
  });

  const insertCategory = useInsertCategoryMutation();
  const updateCategory = useUpdateCategoryMutation();

  React.useEffect(() => {
    if (isOpen && data) {
      form.reset({
        id: data.id.toString(),
        name: data.category_name ?? "",
      });
    }

    if (!isOpen)
      form.reset({
        id: undefined,
        name: "",
      });
  }, [isOpen, data]);

  const onSubmit = (body: ICreateCategoryBody) => {
    if (!data) {
      insertCategory.mutate(
        {
          category_name: body.name,
        },
        {
          onSuccess: () => {
            onCloseModal();
            toast.success("Category added successfully");
          },
          onError: () => {
            toast.error("Failed to add Category");
          },
        }
      );
      return;
    }

    updateCategory.mutate(
      {
        id: data.id,
        body: {
          category_name: body.name,
        },
      },
      {
        onSuccess: () => {
          onCloseModal();
          toast.success("Category updated successfully");
        },
        onError: () => {
          toast.error("Failed to update Category");
        },
      }
    );
  };

  return (
    <FormProvider {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <InputString<ICreateCategoryBody>
          label="Category ID"
          id="id"
          readonly
        />
        <InputString<ICreateCategoryBody> label="Category Name" id="name" />
        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton className="h-11 w-32" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11 w-32"
            type="submit"
            disabled={insertCategory.isPending || updateCategory.isPending}
          >
            {data ? "Save Changes" : "Add Category"}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const NewSubCategoryForm = ({ data, isOpen, onCloseModal }: IFormProps) => {
  const form = useForm({
    resolver: yupResolver(createSubCategoryBodySchema),
  });

  const category = useGetListCategoryQuery();
  const insertSubCategory = useInsertSubCategoryMutation();
  const updateSubCategory = useUpdateSubCategoryMutation();

  React.useEffect(() => {
    const subCategoryData = data as ISubCategory;

    if (isOpen && subCategoryData) {
      form.reset({
        id: subCategoryData.id.toString(),
        name: subCategoryData.subcategory_name ?? "",
        category_id: subCategoryData?.category_id?.toString() ?? undefined,
      });
    }

    if (!isOpen) {
      form.reset({
        id: undefined,
        name: "",
        category_id: undefined,
      });
    }
  }, [isOpen, data]);

  const categoryOptions = React.useMemo(
    () =>
      category.data?.data?.map((it) => ({
        label: it.category_name ?? "",
        value: it.id.toString(),
      })) ?? [],
    [category.data]
  );

  const onSubmit = (body: ICreateSubCategoryBody) => {
    if (!data) {
      insertSubCategory.mutate(
        {
          subcategory_name: body.name,
          category_id: body?.category_id ? +body.category_id : undefined,
        },
        {
          onSuccess: () => {
            onCloseModal();
            toast.success("Sub category added successfully");
          },
          onError: () => {
            toast.error("Failed to add Sub category");
          },
        }
      );
      return;
    }

    const subCategoryData = data as ISubCategory;

    updateSubCategory.mutate(
      {
        id: subCategoryData.id,
        body: {
          subcategory_name: body.name,
          category_id: body?.category_id ? +body.category_id : undefined,
        },
      },
      {
        onSuccess: () => {
          onCloseModal();
          toast.success("Sub category updated successfully");
        },
        onError: () => {
          toast.error("Failed to update Sub category");
        },
      }
    );
  };

  return (
    <FormProvider {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <InputString<ICreateSubCategoryBody>
          label="Sub Category ID"
          id="id"
          readonly
        />
        <InputSelect<ICreateSubCategoryBody>
          label="Category Name"
          id="category_id"
          value={form.watch("category_id")}
          options={categoryOptions}
        />
        <InputString<ICreateSubCategoryBody>
          label="Sub Category Name"
          id="name"
        />
        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton className="h-11 w-32" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11 w-32"
            type="submit"
            disabled={
              insertSubCategory.isPending || updateSubCategory.isPending
            }
          >
            {data ? "Save Changes" : "Add Sub Category"}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const InputString = <T extends ICreateCategoryBody | ICreateSubCategoryBody>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputSelect = <T extends ICreateCategoryBody | ICreateSubCategoryBody>({
  label,
  id,
  placeholder,
  options,
  value,
  optional = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseSelect
        {...form.register(id as Path<T>)}
        value={value}
        onValueChange={(val) => {
          if (!val) return;
          form.setValue(id as Path<T>, val as any, {
            shouldValidate: true,
          });
        }}
      >
        <BaseSelectTrigger className="w-full min-h-11" id={id as string}>
          <BaseSelectValue placeholder={placeholder} />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {options.map((option) => (
            <BaseSelectItem key={option.value} value={option.value}>
              {option.label}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};

export default ManageCategoryNewModal;
