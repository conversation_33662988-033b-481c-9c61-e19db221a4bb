import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import { Search } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import lodash from "lodash";

const ManageCategoryTableHeaderSearch = () => {
  const activeTab = useManageCategoryTabStore((state) => state.activeTab);
  const {
    categoryQuery,
    subCategoryQuery,
    setCategoryQuery,
    setSubCategoryQuery,
  } = useManageCategoryQueryStore(
    useShallow(
      ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      }) => ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      })
    )
  );

  const searchByOption = React.useMemo(() => {
    if (activeTab === "category") {
      return [
        { value: "category_id", label: "Category ID" },
        { value: "category_name", label: "Category Name" },
        { value: "created_by", label: "Created By" },
      ];
    }

    return [
      { value: "sub_category_id", label: "Sub Category ID" },
      { value: "sub_category_name", label: "Sub Category Name" },
      { value: "category_name", label: "Category Name" },
      { value: "created_by", label: "Created By" },
    ];
  }, [activeTab]);

  const handleQueryChange = (
    query: Partial<IGetListCategoryQuery | IGetListSubCategoryQuery>
  ) => {
    if (activeTab === "category") {
      setCategoryQuery(query as Partial<IGetListCategoryQuery>);
      return;
    }

    setSubCategoryQuery(query as Partial<IGetListSubCategoryQuery>);
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={
            activeTab === "category"
              ? categoryQuery.search_by
              : subCategoryQuery.search_by
          }
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {searchByOption.map((it) => (
              <BaseSelectItem value={it.value}>{it.label}</BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageCategoryTableHeaderSearch;
