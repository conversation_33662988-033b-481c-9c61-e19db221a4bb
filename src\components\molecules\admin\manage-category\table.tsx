"use client";

import React from "react";
import { DataTable } from "../../global/table";
import ManageCategoryTableHeader from "./table-header";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import {
  useGetListCategoryQuery,
  useGetListSubCategoryQuery,
} from "@/services/query/admin/manage-category";
import {
  getColumnsManageCategory,
  getColumnsManageSubCategory,
} from "./column";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useShallow } from "zustand/react/shallow";
import { ISubCategory } from "@/interfaces/admin/manage-category/list";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import ManageCategoryDeleteConfirmationModal from "./delete-category-confirmation-modal";

const ManageCategoryTable = () => {
  const activeTab = useManageCategoryTabStore((state) => state.activeTab);
  const {
    setOpenAddModal,
    setOpenedCategory,
    setOpenedSubCategory,
    setOpenDeleteModal,
  } = useManageCategoryModal(
    useShallow(
      ({
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
        setOpenDeleteModal,
      }) => ({
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
        setOpenDeleteModal,
      })
    )
  );
  const {
    categoryQuery,
    subCategoryQuery,
    setCategoryQuery,
    setSubCategoryQuery,
  } = useManageCategoryQueryStore(
    useShallow(
      ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      }) => ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      })
    )
  );

  const category = useGetListCategoryQuery(
    categoryQuery,
    activeTab === "category"
  );
  const subCategory = useGetListSubCategoryQuery(
    subCategoryQuery,
    activeTab === "sub-category"
  );

  const categoryColumns = React.useMemo(
    () =>
      getColumnsManageCategory({
        onEdit: (data) => {
          setOpenedCategory(data);
          setOpenAddModal(true);
        },
        onDelete: (data) => {
          setOpenedCategory(data);
          setOpenDeleteModal(true);
        },
      }),
    [category.data]
  );

  const subCategoryColumns = React.useMemo(
    () =>
      getColumnsManageSubCategory({
        onEdit: (data) => {
          setOpenedSubCategory(data as ISubCategory);
          setOpenAddModal(true);
        },
        onDelete: (data) => {
          setOpenedSubCategory(data as ISubCategory);
          setOpenDeleteModal(true);
        },
      }),
    [subCategory.data]
  );

  return (
    <div className="flex flex-col gap-4 h-full">
      <ManageCategoryTableHeader />
      <ManageCategoryDeleteConfirmationModal
        fitur={activeTab === "category" ? "Category" : "Sub Category"}
      />

      {activeTab === "category" ? (
        <DataTable
          columns={categoryColumns}
          data={category.data?.data ?? []}
          pagination={category.data?.pagination}
          onPageChange={(page) => setCategoryQuery({ page })}
        />
      ) : (
        <DataTable
          columns={subCategoryColumns}
          data={subCategory.data?.data ?? []}
          pagination={subCategory.data?.pagination}
          onPageChange={(page) => setSubCategoryQuery({ page })}
        />
      )}
    </div>
  );
};

export default ManageCategoryTable;
