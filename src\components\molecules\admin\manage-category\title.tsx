"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import { useShallow } from "zustand/react/shallow";

const ManageCategoryTitle = () => {
  const { activeTab, setActiveTab } = useManageCategoryTabStore(
    useShallow(({ activeTab, setActiveTab }) => ({ activeTab, setActiveTab }))
  );

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Category & Sub Category
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={activeTab}
          onValueChange={(val) => {
            setActiveTab(val as "category" | "sub-category");
          }}
        >
          <BaseTabsList className="w-full h-12">
            <BaseTabsTrigger value="category">Category</BaseTabsTrigger>
            <BaseTabsTrigger value="sub-category">Sub Category</BaseTabsTrigger>
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default ManageCategoryTitle;
