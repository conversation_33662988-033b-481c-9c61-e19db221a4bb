import { BaseButton } from "@/components/atoms/button";
import { IMaterial } from "@/interfaces/admin/manage-material/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import {
  Pencil,
  Trash2,
  Download,
  ChevronDown,
  FileText,
  Eye,
} from "lucide-react";
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import IconMaterialVideo from "@/assets/icons/IconMaterialVideo";
import { ICertificate } from "@/interfaces/admin/manage-certificate/list";
import { BaseSwitch } from "@/components/atoms/switch";
import IconEye from "@/assets/icons/IconEye";

interface Props {
  onView: (certificate: ICertificate) => void;
  onToggleActive: (certificate: ICertificate, checked: boolean) => void;
}

export const getCertificateColumns = ({
  onView,
  onToggleActive,
}: Props): ColumnDef<ICertificate>[] => {
  return [
    {
      accessorKey: "userId",
      header: "User ID",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">
            {row.original.userId}
          </div>
        );
      },
    },
    {
      accessorKey: "npk",
      header: "NPK",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">{row.original.npk}</div>
        );
      },
    },
    {
      accessorKey: "fullName",
      header: "Full Name",
      cell({ row }) {
        return <div className="font-medium">{row.original.fullName}</div>;
      },
    },
    {
      accessorKey: "moduleName",
      header: "Module Name",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.moduleName}</div>
        );
      },
    },
    {
      accessorKey: "moduleType",
      header: "Module Type",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.moduleType}</div>
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.level}</div>
        );
      },
    },
    {
      accessorKey: "attempt",
      header: "Attempt",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.attempt}</div>
        );
      },
    },
    {
      accessorKey: "issuedDate",
      header: "Issued Date",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground w-[100px] text-wrap">
            {row.original.issuedDate
              ? dayjs(row.original.issuedDate).format("DD MMM YYYY HH:mm:ss")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "expiredDate",
      header: "Expired Date",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground w-[100px] text-wrap">
            {row.original.expiredDate
              ? dayjs(row.original.expiredDate).format("DD MMM YYYY HH:mm:ss")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.status}</div>
        );
      },
    },
    {
      accessorKey: "active",
      header: "Active?",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            <BaseSwitch
              checked={row.original.active}
              onCheckedChange={(checked) =>
                onToggleActive(row.original, checked)
              }
            />
          </div>
        );
      },
    },

    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-end gap-1.5">
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onView(row.original)}
              title="Download"
            >
              <IconEye />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
