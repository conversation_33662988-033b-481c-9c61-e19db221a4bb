"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { cn } from "@/utils/common";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useState } from "react";
import { BaseButton } from "@/components/atoms/button";

interface IFilter {
  status: string | undefined;
  moduleType: string | undefined;
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

const statusOptions = [
  { value: "active", label: "Active" },
  { value: "expired", label: "Expired" },
  { value: "revoked", label: "Revoked" },
];

const moduleTypeOptions = [
  { value: "safety", label: "Safety" },
  { value: "compliance", label: "Compliance" },
  { value: "technical", label: "Technical" },
  { value: "soft_skills", label: "Soft Skills" },
];

const ManageCertificateFilterInput = () => {
  const [filter, setFilter] = useState<IFilter>({
    status: undefined,
    moduleType: undefined,
    dateRange: {
      from: undefined,
      to: undefined,
    },
  });

  const handleFilterChange = (key: keyof IFilter, value: any) => {
    setFilter((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleDateRangeChange = (
    key: "from" | "to",
    value: Date | undefined
  ) => {
    setFilter((prev) => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [key]: value,
      },
    }));
  };

  const handleApply = () => {
    console.log("Applying filters:", filter);
    // TODO: Apply filters to the table
  };

  const handleReset = () => {
    setFilter({
      status: undefined,
      moduleType: undefined,
      dateRange: {
        from: undefined,
        to: undefined,
      },
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton className="h-9" onClick={handleApply}>
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Module Type Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Module Type</BaseLabel>
          <BaseSelect
            value={filter.moduleType}
            onValueChange={(value) => handleFilterChange("moduleType", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select module type" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {moduleTypeOptions.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Status Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Status</BaseLabel>
          <BaseSelect
            value={filter.status}
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select status" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {statusOptions.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Date Range Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Issued Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.dateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.dateRange.from ? (
                    format(filter.dateRange.from, "PPP")
                  ) : (
                    <span>From</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filter.dateRange.from}
                  onSelect={(date) => handleDateRangeChange("from", date)}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Expired Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.dateRange.to && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.dateRange.to ? (
                    format(filter.dateRange.to, "PPP")
                  ) : (
                    <span>To</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filter.dateRange.to}
                  onSelect={(date) => handleDateRangeChange("to", date)}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageCertificateFilterInput;
