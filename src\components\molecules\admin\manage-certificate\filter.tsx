"use client";

import FilterButton from "@/components/atoms/filter-button";
import { useState } from "react";

interface Props {
  filterOpen: boolean;
  setFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ManageCertificateTableHeaderFilter = ({
  filterOpen,
  setFilterOpen,
}: Readonly<Props>) => {
  return (
    <div className="flex items-center justify-end gap-3">
      <FilterButton
        active={filterOpen}
        onClick={() => setFilterOpen((prev) => !prev)}
        className="h-12"
      />
    </div>
  );
};

export default ManageCertificateTableHeaderFilter;
