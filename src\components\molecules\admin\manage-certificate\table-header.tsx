"use client";
import React, { useState } from "react";
import ManageCertificateTableHeaderSearch from "./search";
import ManageCertificateTableHeaderFilter from "./filter";
import ManageCertificateFilterInput from "./filter-input";

const ManageCertificateTableHeader = () => {
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ManageCertificateTableHeaderSearch />
        <ManageCertificateTableHeaderFilter
          filterOpen={filterOpen}
          setFilterOpen={setFilterOpen}
        />
      </div>
      {filterOpen && <ManageCertificateFilterInput />}
    </div>
  );
};

export default ManageCertificateTableHeader;
