"use client";

import React from "react";
import { DataTable } from "../../global/table";

// import { getMaterialColumns } from "./column";
// import ManageCertificateTableHeader from "./table-header";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { ICertificate } from "@/interfaces/admin/manage-certificate/list";
import { getCertificateColumns } from "./column";
import ManageCertificateTableHeader from "./table-header";
import CertificateDetail from "../../profile-dashboard/riwayat-belajar/certificate-detail";
import { useRouter } from "next/navigation";
import ConfirmationModal from "../../modal/confirmation-modal";

// Dummy data for the certificates table
const dummyCertificates: ICertificate[] = [
  {
    userId: 1,
    npk: "EMP001",
    fullName: "<PERSON> Johnson",
    moduleName: "Safety Training",
    moduleType: "Online",
    level: "1",
    attempt: 1,
    issuedDate: "2025-01-10T09:30:00.000Z",
    expiredDate: "2026-01-10T23:59:59.999Z",
    status: "Passed",
    active: true,
  },
  {
    userId: 2,
    npk: "EMP002",
    fullName: "Bob Smith",
    moduleName: "Fire Drill Procedure",
    moduleType: "Offline",
    level: "2",
    attempt: 2,
    issuedDate: "2025-03-05T14:15:00.000Z",
    expiredDate: "2025-09-05T23:59:59.999Z",
    status: "Failed",
    active: false,
  },
  {
    userId: 3,
    npk: "EMP003",
    fullName: "Charlie Davis",
    moduleName: "Workplace Ethics",
    moduleType: "Hybrid",
    level: "3",
    attempt: 1,
    issuedDate: "2025-06-15T10:00:00.000Z",
    expiredDate: "2026-06-15T23:59:59.999Z",
    status: "In Progress",
    active: true,
  },
];

const DUMMY_PAGINATION = {
  current_page: 1,
  total_page: 1,
  total_data: 1,
  next: null,
  prev: null,
};

type IModalData = {
  isOpen: boolean;
  data: ICertificate | null;
};

const ManageCertificateTable = () => {
  const router = useRouter();
  const activeTab = useManageMaterialTabStore((state) => state.activeTab);
  const [openModal, setOpenModal] = React.useState<IModalData>({
    isOpen: false,
    data: null,
  });
  const columns = React.useMemo(
    () =>
      getCertificateColumns({
        onView: (certificate) => {
          router.push(
            `/admin/manage-certificate/certificate/${certificate.npk}`
          );
        },
        onToggleActive: (certificate) => {
          setOpenModal({ isOpen: true, data: certificate });
        },
      }),
    []
  );

  return (
    <div className="flex flex-col gap-4 h-full">
      <ConfirmationModal
        isOpen={openModal.isOpen}
        onOpenChange={(open) => setOpenModal({ isOpen: open, data: null })}
        onConfirm={() => {}}
        title={`${
          openModal.data?.active ? "Nonaktifkan" : "Aktifkan"
        } Certificate?`}
        description={`Anda yakin ingin ${
          openModal.data?.active ? "nonaktifkan" : "aktifkan"
        } certificate ini?`}
      />
      <ManageCertificateTableHeader />
      <DataTable
        key={activeTab}
        columns={columns}
        data={dummyCertificates}
        pagination={DUMMY_PAGINATION}
        onPageChange={(page) => console.log(page)}
      />
    </div>
  );
};

export default ManageCertificateTable;
