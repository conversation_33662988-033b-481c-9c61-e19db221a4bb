"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useShallow } from "zustand/react/shallow";

const TABS = [
  { value: "video", label: "Manage Video" },
  { value: "audio", label: "Manage Audio" },
  { value: "document", label: "Manage Document" },
  { value: "scorm", label: "Manage SCORM" },
];

const ManageCertificateTitle = () => {
  const { activeTab, setActiveTab } = useManageMaterialTabStore(
    useShallow(({ activeTab, setActiveTab }) => ({ activeTab, setActiveTab }))
  );

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Certificate
      </div>
    </div>
  );
};

export default ManageCertificateTitle;
