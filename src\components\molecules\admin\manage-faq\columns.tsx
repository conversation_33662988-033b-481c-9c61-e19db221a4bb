"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Pencil, Trash2 } from "lucide-react";
import { BaseButton } from "@/components/atoms/button";

import { IFaq } from "@/interfaces/admin/manage-faq/list";
interface Props {
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

export const getColumnsManageFaq = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IFaq>[] => [
  {
    id: "question",
    accessorKey: "question",
    header: "Question",
  },
  {
    id: "answer",
    accessorKey: "answer",
    header: "Answer",
    meta: {
      className: "w-[50%]",
    },
  },
  {
    id: "action",
    accessorKey: "id",
    header: "Action",
    cell({ row }) {
      return (
        <div className="flex items-center justify-start gap-2">
          <BaseButton
            variant={"outline"}
            className="border-none"
            onClick={() => onEdit(row.original.id)}
          >
            <Pencil
              size={24}
              className="fill-gray-700 text-white"
              strokeWidth={1}
            />
          </BaseButton>
          <BaseButton
            variant={"outline"}
            className="border-none"
            onClick={() => onDelete(row.original.id)}
          >
            <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
          </BaseButton>
        </div>
      );
    },
    meta: {
      className: "w-24",
    },
  },
];
