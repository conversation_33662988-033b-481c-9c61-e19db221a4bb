"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ManageFaqNewModal from "./new-modal";
import { useManageFaqModal } from "@/store/admin/manage-faq/modal";

const ManageFaqTableHeaderFilter = () => {
  const { setOpenAddFaq } = useManageFaqModal(
    useShallow(({ setOpenAddFaq }) => ({
      setOpenAddFaq,
    }))
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton className="h-12 px-5" onClick={() => setOpenAddFaq(true)}>
        <div className="flex items-center gap-2">
          <Plus />
          New FAQ
        </div>
      </BaseButton>
      <ManageFaqNewModal />
    </div>
  );
};

export default ManageFaqTableHeaderFilter;
