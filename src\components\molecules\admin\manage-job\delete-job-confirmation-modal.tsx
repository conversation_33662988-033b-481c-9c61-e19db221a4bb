import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useDeleteJobPositionMutation } from "@/services/mutation/manage-job/delete";
import { useGetJobPositionListQuery } from "@/services/query/manage-job/list";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";
import { Trash2 } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const ManageJobDeleteConfirmationModal = () => {
  const {
    openDeleteJobPosition,
    setOpenDeleteJobPosition,
    currentData,
    setCurrentData,
  } = useManageJobModalStore(
    useShallow(
      ({
        openDeleteJobPosition,
        setOpenDeleteJobPosition,
        currentData,
        setCurrentData,
      }) => ({
        openDeleteJobPosition,
        setOpenDeleteJobPosition,
        currentData,
        setCurrentData,
      })
    )
  );

  const { query } = useManageJobFilterStore(
    useShallow(({ query }) => ({
      query,
    }))
  );

  const jobPositions = useGetJobPositionListQuery(query);

  const deleteJob = useDeleteJobPositionMutation();

  const handleDelete = () => {
    if (currentData) {
      deleteJob.mutate(
        { params: { id: currentData } },
        {
          onSuccess: (data) => {
            toast.success(data.message);
            jobPositions.refetch();
            setOpenDeleteJobPosition(false);
            setCurrentData(null);
          },
          onError: (data) => {
            toast.error(data.message);
            setOpenDeleteJobPosition(false);
            setCurrentData(null);
          },
        }
      );
    }
  };

  return (
    <BaseDialog
      open={openDeleteJobPosition}
      onOpenChange={(open) => {
        setOpenDeleteJobPosition(open);
        setCurrentData(null);
      }}
    >
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus Job Position?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Job position yang dipilih akan dihapus secara permanen. Tindakan ini
            tidak bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <DialogClose asChild>
            <BaseButton
              className="w-34 h-11"
              variant={"destructive"}
              onClick={handleDelete}
            >
              Hapus
            </BaseButton>
          </DialogClose>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ManageJobDeleteConfirmationModal;
