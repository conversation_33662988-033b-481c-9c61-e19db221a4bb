"use client";

import { BaseButton } from "@/components/atoms/button";
import { cn } from "@/utils/common";
import {
  DownloadCloud,
  Plus,
  RefreshCw,
  Settings2,
  UploadCloud,
} from "lucide-react";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useShallow } from "zustand/react/shallow";
import {
  BaseTooltip,
  BaseTooltipContent,
  BaseTooltipTrigger,
} from "@/components/atoms/tooltip";

import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";
import ManageJobUploadHistoryModal from "./upload-history-modal";
import ManageJobUploadExcelModal from "./upload-excel-modal";
import ManageJobNewJobPositionModal from "./new-job-modal";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";

const ManageJobTableHeaderFilter = () => {
  const { openFilter, setOpenFilter } = useManageJobFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  const { setOpenAddJobPosition, setOpenUploadHistory, setOpenUploadExcel } =
    useManageJobModalStore(
      useShallow(
        ({
          setOpenAddJobPosition,
          setOpenUploadHistory,
          setOpenUploadExcel,
        }) => ({
          setOpenAddJobPosition,
          setOpenUploadHistory,
          setOpenUploadExcel,
        })
      )
    );

  return (
    <div className="flex justify-end gap-3">
      <BaseDropdownMenu>
        <BaseDropdownMenuTrigger asChild>
          <BaseButton variant={"outline"} size={"icon"} className="size-12">
            <DownloadCloud />
          </BaseButton>
        </BaseDropdownMenuTrigger>
        <BaseDropdownMenuContent>
          <BaseDropdownMenuItem className="px-4">
            Download Excel Data
          </BaseDropdownMenuItem>
          <BaseDropdownMenuItem className="px-4">
            Download Excel Template
          </BaseDropdownMenuItem>
        </BaseDropdownMenuContent>
      </BaseDropdownMenu>
      <BaseDropdownMenu>
        <BaseDropdownMenuTrigger asChild>
          <BaseButton variant={"outline"} size={"icon"} className="size-12">
            <UploadCloud />
          </BaseButton>
        </BaseDropdownMenuTrigger>
        <BaseDropdownMenuContent>
          <BaseDropdownMenuItem
            className="px-4"
            onClick={() => setOpenUploadExcel(true)}
          >
            Upload Excel Data
          </BaseDropdownMenuItem>
          <BaseDropdownMenuItem className="px-4">
            Upload Excel Template
          </BaseDropdownMenuItem>
        </BaseDropdownMenuContent>
      </BaseDropdownMenu>
      <BaseTooltip>
        <BaseTooltipTrigger asChild>
          <BaseButton
            variant={"outline"}
            className="size-12"
            onClick={() => setOpenUploadHistory(true)}
          >
            <RefreshCw />
          </BaseButton>
        </BaseTooltipTrigger>
        <BaseTooltipContent side="bottom">
          <p>Upload Progress and History</p>
        </BaseTooltipContent>
      </BaseTooltip>
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
      <BaseButton
        className="h-12 px-5"
        onClick={() => setOpenAddJobPosition(true)}
      >
        <div className="flex items-center gap-2">
          <Plus />
          Add Job Position
        </div>
      </BaseButton>
      <ManageJobNewJobPositionModal />
      <ManageJobUploadHistoryModal />
      <ManageJobUploadExcelModal />
    </div>
  );
};

export default ManageJobTableHeaderFilter;
