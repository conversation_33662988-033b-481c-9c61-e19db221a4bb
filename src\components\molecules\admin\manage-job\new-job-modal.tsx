import { BaseButton } from "@/components/atoms/button";
import {
  BaseCommand,
  BaseCommandEmpty,
  BaseCommandGroup,
  BaseCommandInput,
  BaseCommandItem,
  BaseCommandList,
} from "@/components/atoms/command";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BasePopover,
  BasePopoverContent,
  BasePopoverTrigger,
} from "@/components/atoms/popover";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  createJobPositionFormSchema,
  ICreateJobPositionForm,
} from "@/interfaces/admin/manage-job/new";
import { cn } from "@/lib/utils";
import { useCreateNewJobPositionMutation } from "@/services/mutation/manage-job/new";
import { useUpdateJobPositionMutation } from "@/services/mutation/manage-job/update";
import { useGetJobPositionDepartmentListQuery } from "@/services/query/manage-job/department";
import { useGetJobPositionDetailQuery } from "@/services/query/manage-job/detail";
import { useGetJobPositionFunctionListQuery } from "@/services/query/manage-job/function";
import { useGetJobPositionListQuery } from "@/services/query/manage-job/list";
import { useGetJobPositionStartingLevelListQuery } from "@/services/query/manage-job/starting-level";
import { useGetUserMasterEntity } from "@/services/query/user-management/user-entity";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";
import { isNumberInput } from "@/utils/common/number";
import { yupResolver } from "@hookform/resolvers/yup";
import { DialogClose } from "@radix-ui/react-dialog";
import { Check, ChevronsUpDown } from "lucide-react";
import { useEffect } from "react";
import {
  Controller,
  DefaultValues,
  FormProvider,
  Path,
  useForm,
  useFormContext,
} from "react-hook-form";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const defaultValue: DefaultValues<ICreateJobPositionForm> = {
  id: "",
  name: "",
  position_type: "",
  department_id: undefined,
  job_function_id: undefined,
  status: undefined,
  starting_level_id: undefined,
  entity_id: undefined,
  is_neop: undefined,
  welcoming_kit: undefined,
  starter_module_priority: "",
};

const ManageJobNewJobPositionModal = () => {
  const {
    openAddJobPosition,
    setOpenAddJobPosition,
    currentData,
    setCurrentData,
  } = useManageJobModalStore(
    useShallow(
      ({
        openAddJobPosition,
        setOpenAddJobPosition,
        currentData,
        setCurrentData,
      }) => ({
        openAddJobPosition,
        setOpenAddJobPosition,
        currentData,
        setCurrentData,
      })
    )
  );

  const { query } = useManageJobFilterStore(
    useShallow(({ query }) => ({
      query,
    }))
  );

  const jobPositions = useGetJobPositionListQuery(query);

  const departments = useGetJobPositionDepartmentListQuery({});
  const startingLearningLevels = useGetJobPositionStartingLevelListQuery({});
  const jobFunctions = useGetJobPositionFunctionListQuery({});
  const entities = useGetUserMasterEntity({});

  const createJobPosition = useCreateNewJobPositionMutation();
  const updateJobPosition = useUpdateJobPositionMutation();

  const currentJob = useGetJobPositionDetailQuery({
    id: currentData,
  });

  const form = useForm({
    resolver: yupResolver(createJobPositionFormSchema),
    mode: "all",
  });

  useEffect(() => {
    if (currentJob.data) {
      form.reset({
        department_id: currentJob.data.data.department_id
          ? +currentJob.data.data.department_id
          : undefined,
        entity_id: currentJob.data.data.entity_id
          ? +currentJob.data.data.entity_id
          : undefined,
        id: currentJob.data.data.job_id ?? undefined,
        name: currentJob.data.data.job_name ?? undefined,
        job_function_id: currentJob.data.data.job_function_id
          ? +currentJob.data.data.job_function_id
          : undefined,
        status: currentJob.data.data.is_active ?? undefined,
        starting_level_id: currentJob.data.data.level_id ?? undefined,
        is_neop: currentJob.data.data.is_need_neop ?? undefined,
        welcoming_kit: currentJob.data.data.is_need_welcoming_kit ?? undefined,
        starter_module_priority:
          currentJob.data.data.starter_module_priority ?? undefined,
        position_type: currentJob.data.data.job_position_type ?? undefined,
      });
    }
  }, [currentJob.data]);

  const handleSubmit = (data: ICreateJobPositionForm) => {
    if (currentData) {
      updateJobPosition.mutate(
        {
          form: data,
          departments: departments.data?.data ?? [],
          job_functions: jobFunctions.data?.data ?? [],
          levels: startingLearningLevels.data?.data ?? [],
          params: {
            id: currentData,
          },
        },
        {
          onSuccess: (data) => {
            toast.success(data?.message ?? "Job position updated successfully");
            jobPositions.refetch();
            setOpenAddJobPosition(false);
            setCurrentData(null);
            form.reset(defaultValue);
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to update job position");
          },
        }
      );
    } else {
      createJobPosition.mutate(
        {
          form: data,
          departments: departments.data?.data ?? [],
          job_functions: jobFunctions.data?.data ?? [],
          levels: startingLearningLevels.data?.data ?? [],
        },
        {
          onSuccess: (data) => {
            toast.success(data?.message ?? "Job position created successfully");
            jobPositions.refetch();
            setOpenAddJobPosition(false);
            setCurrentData(null);
            form.reset(defaultValue);
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to create job position");
          },
        }
      );
    }
  };

  const handleCancel = (value: boolean) => {
    setOpenAddJobPosition(value);

    if (!value) {
      form.reset(defaultValue);
      setCurrentData(null);
    }
  };

  return (
    <BaseDialog open={openAddJobPosition} onOpenChange={handleCancel}>
      <BaseDialogContent className="min-w-5xl overflow-y-auto overflow-x-hidden h-fit">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{currentData ? "Update" : "Add"} Job Position</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <div className="flex flex-col gap-4">
          <FormProvider {...form}>
            <form
              className="flex flex-col gap-6"
              onSubmit={form.handleSubmit(handleSubmit)}
            >
              <div className="flex flex-col gap-2">
                <span>Job Position Information</span>
                <div className="grid grid-cols-3 gap-4">
                  <InputString
                    label="Job Position ID"
                    id="id"
                    placeholder="Input job position ID"
                  />
                  <InputString
                    label="Job Position Name"
                    id="name"
                    placeholder="Input job position name"
                  />
                  <InputSelect
                    id="position_type"
                    label="Job Position Type"
                    placeholder="Select job position type"
                    options={
                      entities.data?.data.map((entity) => ({
                        label: entity.entity_name ?? "",
                        value: String(entity.entity_id),
                      })) || []
                    }
                  />
                  <InputSelect
                    id="department_id"
                    label="Department"
                    placeholder="Select department"
                    options={
                      departments.data?.data.map((department) => ({
                        label: department.department_name ?? "",
                        value: String(department.id),
                      })) || []
                    }
                    optional
                  />

                  <InputSelect
                    id="job_function_id"
                    label="Job Function"
                    placeholder="Select job function"
                    options={
                      jobFunctions.data?.data.map((jobFunction) => ({
                        label: jobFunction.function_name ?? "",
                        value: String(jobFunction.id),
                      })) || []
                    }
                    optional
                  />
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <span>Job Position Configuration</span>
                <div className="grid grid-cols-3 gap-4">
                  <InputSelect
                    id="status"
                    label="Status"
                    placeholder="Select status"
                    options={[
                      { label: "Active", value: "true" },
                      { label: "Inactive", value: "false" },
                    ]}
                  />
                  <InputSelect
                    id="starting_level_id"
                    label="Starting Learning Level"
                    placeholder="Select level"
                    options={
                      startingLearningLevels.data?.data.map((level) => ({
                        label: level.name ?? "",
                        value: String(level.id),
                      })) || []
                    }
                  />
                  <InputSelect
                    id="entity_id"
                    label="Entity"
                    placeholder="Select entity"
                    options={
                      entities.data?.data.map((entity) => ({
                        label: entity.entity_name ?? "",
                        value: String(entity.entity_id),
                      })) || []
                    }
                  />
                  <InputSelect
                    id="is_neop"
                    label="Is User Need NEOP Module"
                    placeholder="Select option"
                    options={[
                      { label: "TRUE", value: "true" },
                      { label: "FALSE", value: "false" },
                    ]}
                  />

                  <InputSelect
                    id="welcoming_kit"
                    label="Is User Need Welcoming Kit Module"
                    placeholder="Select option"
                    options={[
                      { label: "TRUE", value: "true" },
                      { label: "FALSE", value: "false" },
                    ]}
                  />
                  <InputSelect
                    id="starter_module_priority"
                    label="Starter Module Priority"
                    placeholder="Select option"
                    options={[
                      { label: "NEOP", value: "NEOP" },
                      { label: "Welcoming Kit", value: "WELCOMING KIT" },
                    ]}
                  />
                </div>
              </div>
              <BaseSeparator className="mt-4 -mb-2" />

              <div className="flex justify-end gap-3">
                <DialogClose asChild>
                  <BaseButton className="h-11 w-32" variant={"outline"}>
                    Cancel
                  </BaseButton>
                </DialogClose>
                <BaseButton
                  className="h-11 min-w-32"
                  type="submit"
                  disabled={!form.formState.isValid}
                >
                  {currentData ? "Update" : "Add"} Job Position
                </BaseButton>
              </div>
            </form>
          </FormProvider>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

const InputString = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateJobPositionForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateJobPositionForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
      />
    </div>
  );
};

const InputNumber = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateJobPositionForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateJobPositionForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
        onKeyDown={(e) => {
          if (isNumberInput(e)) e.preventDefault();
        }}
        type="string"
      />
    </div>
  );
};

const InputSelect = ({
  label,
  id,
  placeholder,
  options,
  optional = false,
}: {
  label: string;
  id: Path<ICreateJobPositionForm>;
  placeholder?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateJobPositionForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>

      <Controller
        name={id as any}
        control={form.control}
        render={({ field }) => {
          return (
            <BasePopover>
              <BasePopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className="w-full min-h-11 justify-between"
                >
                  {field.value != null
                    ? options.find((o) => o.value === String(field.value))
                        ?.label ?? placeholder
                    : placeholder}

                  <ChevronsUpDown className="opacity-50" />
                </BaseButton>
              </BasePopoverTrigger>
              <BasePopoverContent className="w-full p-0">
                <BaseCommand>
                  <BaseCommandInput
                    placeholder="Search data..."
                    className="h-9"
                  />
                  <BaseCommandList>
                    <BaseCommandEmpty>No data found.</BaseCommandEmpty>
                    <BaseCommandGroup>
                      {options.map((option) => (
                        <BaseCommandItem
                          key={`${id}-${option.value}`}
                          value={`${option.label}__${option.value}`}
                          onSelect={(currentValue) => {
                            field.onChange(currentValue.split("__")[1]);
                          }}
                        >
                          {option.label}
                          <Check
                            className={cn(
                              "ml-auto",
                              field.value === option.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </BaseCommandItem>
                      ))}
                    </BaseCommandGroup>
                  </BaseCommandList>
                </BaseCommand>
              </BasePopoverContent>
            </BasePopover>
          );
        }}
      />
    </div>
  );
};

export default ManageJobNewJobPositionModal;
