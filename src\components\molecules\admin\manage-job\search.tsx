import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { Search } from "lucide-react";
import React from "react";
import { useDebouncedCallback } from "use-debounce";
import { useShallow } from "zustand/react/shallow";

const ManageJobTableHeaderSearch = () => {
  const { query, setQuery } = useManageJobFilterStore(
    useShallow(({ setQuery, query }) => ({
      setQuery,
      query,
    }))
  );

  const handleSearch = useDebouncedCallback((value: string) => {
    setQuery({ ...query, search: value ?? undefined, page: 1 });
  }, 500);

  const handleChange = (value: "job_name" | "job_function") => {
    setQuery({ ...query, search_by: value ?? undefined, page: 1 });
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <div>
        <BaseSelect onValueChange={handleChange}>
          <BaseSelectTrigger className="w-32 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            <BaseSelectItem value="job_name">Job Name</BaseSelectItem>
            <BaseSelectItem value="job_function">Job Function</BaseSelectItem>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={(e) => handleSearch(e.target.value)}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageJobTableHeaderSearch;
