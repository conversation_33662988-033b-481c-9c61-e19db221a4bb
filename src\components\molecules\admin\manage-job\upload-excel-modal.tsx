import { <PERSON><PERSON><PERSON>t, BaseAlertDescription } from "@/components/atoms/alert";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseSeparator } from "@/components/atoms/separator";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import {
  CloudUpload,
  Download,
  FileSpreadsheet,
  Info,
  RefreshCw,
  Trash2,
  X,
} from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import { ColumnDef } from "@tanstack/react-table";
import { IProgressHistory } from "@/interfaces/admin/user-management/list";
import dayjs from "dayjs";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import { BaseInput } from "@/components/atoms/input";
import { DialogClose } from "@radix-ui/react-dialog";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";

export const exampleProgressHistory: IProgressHistory[] = [
  {
    filename: "example.xlsx",
    status: "success",
    success: 10,
    failed: 0,
    uploaded_at: dayjs().format("DD MMM YYYY HH:mm"),
    uploaded_by: "AdminUser",
    failed_log: null,
  },
  {
    filename: "failed.xlsx",
    status: "failed",
    success: 0,
    failed: 5,
    uploaded_at: dayjs().subtract(1, "day").format("DD MMM YYYY HH:mm"),
    uploaded_by: "EditorUser",
    failed_log: "Error: invalid data",
  },
  {
    filename: "processing.xlsx",
    status: "processing",
    success: null,
    failed: null,
    uploaded_at: dayjs().subtract(2, "day").format("DD MMM YYYY HH:mm"),
    uploaded_by: "AdminUser",
    failed_log: null,
  },
];

const columns: ColumnDef<IProgressHistory>[] = [
  {
    accessorKey: "filename",
    header: "Filename",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "success",
    header: "Success",
    cell(props) {
      return <span>{props.row.original.success ?? "-"}</span>;
    },
  },
  {
    accessorKey: "failed",
    header: "Failed",
    cell(props) {
      return <span>{props.row.original.failed ?? "-"}</span>;
    },
  },
  {
    accessorKey: "uploaded_at",
    header: "Uploaded At",
  },
  {
    accessorKey: "uploaded_by",
    header: "Uploaded By",
  },
  {
    accessorKey: "failed_log",
    header: "Failed Log",
    cell(props) {
      const value = props.row.original.failed_log;

      if (!value) return "";

      return (
        <BaseButton className="w-10 h-10" variant={"ghost"}>
          <Download strokeWidth={3} />
        </BaseButton>
      );
    },
  },
];

const ManageJobUploadExcelModal = () => {
  const {
    openUploadExcel,
    setOpenUploadExcel,
    setOpenUploadExcelConfirmation,
  } = useManageJobModalStore(
    useShallow(
      ({
        openUploadExcel,
        setOpenUploadExcel,
        setOpenUploadExcelConfirmation,
      }) => ({
        openUploadExcel,
        setOpenUploadExcel,
        setOpenUploadExcelConfirmation,
      })
    )
  );

  return (
    <BaseDialog open={openUploadExcel} onOpenChange={setOpenUploadExcel}>
      <BaseDialogContent className="h-fit min-w-1/3" showCloseButton={true}>
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>Upload by Excel Template</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <InputFile id="upload-excel" />
        <div className="w-full flex p-4 border rounded-md border-gray-200">
          <div className="flex gap-4 items-center w-3/4">
            <FileSpreadsheet className="text-green-500" size={42} />
            <div className="flex flex-col justify-center">
              <span className="text-base text-orange-400">Data 2023.xlsx</span>
              <span className="text-sm text-gray-400">Uploading 60%</span>
            </div>
          </div>
          <div className="flex justify-end gap-1 items-center w-1/4">
            <div className="rounded-full p-1 w-9 h-9 flex justify-center items-center border-4 border-t-orange-400 border-r-orange-400 border-b-orange-400 rotate-45 stroke-1">
              <X size={14} className="text-gray-500 -rotate-45" />
            </div>
            {/* <BaseButton className="w-10 h-10" variant={"ghost"} size={"icon"}>
              <Trash2 className="text-red-500" />
            </BaseButton> */}
            {/* <BaseButton className="w-10 h-10" variant={"ghost"} size={"icon"}>
              <RefreshCw className="text-orange-400" />
            </BaseButton> */}
          </div>
        </div>
        <BaseSeparator className="mt-2" />
        <div className="flex justify-end gap-3">
          <DialogClose asChild>
            <BaseButton className="w-32 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <DialogClose asChild>
            <BaseButton
              className="w-32 h-11"
              onClick={() => setOpenUploadExcelConfirmation(true)}
            >
              Continue
            </BaseButton>
          </DialogClose>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

const InputFile = ({
  id,
  placeholder,
}: {
  id: string;
  placeholder?: string;
}) => {
  return (
    <div className="flex flex-col gap-1">
      <BaseLabel
        className="w-full py-12 rounded-md border border-dashed border-gray-300 bg-gray-50 flex justify-center items-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-lg"
        htmlFor={id}
      >
        <CloudUpload size={48} className="text-gray-300" />
        {"Drag & drop your files or"}
        <span className="text-orange-400">browse</span>
      </BaseLabel>
      <span className="text-sm text-gray-400 mt-1">
        Supported file types: XLSX, max. size 20MB.
      </span>
      <BaseInput
        id={id}
        placeholder={placeholder}
        type="file"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        className="sr-only"
      />
    </div>
  );
};

export default ManageJobUploadExcelModal;
