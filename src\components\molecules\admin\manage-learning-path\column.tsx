import { BaseBadge } from "@/components/atoms/badge";
import { BaseButton } from "@/components/atoms/button";
import { BaseSwitch } from "@/components/atoms/switch";

import {
  IGetLearningCodeResponse,
  IGetLearningLevelResponse,
} from "@/interfaces/admin/manage-learning-path/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { ChevronDown, Pencil, Trash2 } from "lucide-react";

interface Props {
  onEdit: (id: IGetLearningCodeResponse | IGetLearningLevelResponse) => void;
  onDelete: (id: IGetLearningCodeResponse | IGetLearningLevelResponse) => void;
}

export const getColumnsManageLearningCode = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IGetLearningCodeResponse>[] => {
  return [
    {
      accessorKey: "learning_code",
      header: "Learning Code",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.learning_code}
          </span>
        );
      },
    },
    { accessorKey: "learning_code_name", header: "Learning Code Name" },
    {
      accessorKey: "related_job_position",
      header: "Related Job Position",
      cell(props) {
        const data = props.row.original.related_job_position;

        return (
          <BaseBadge className="flex gap-2 justify-between">
            <span>{`${data.length} Job Position`}</span>

            <ChevronDown size={12} />
          </BaseBadge>
        );
      },
    },
    {
      accessorKey: "assosiated_module",
      header: "Associated Module",
      cell(props) {
        const data = props.row.original.assosiated_module;

        return (
          <BaseBadge className="flex gap-2 justify-between">
            <span>{`${data.length} Module`}</span>

            <ChevronDown size={12} />
          </BaseBadge>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;

        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "updated_at",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.updated_at;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "is_active",
      header: "Status",
      cell({ row }) {
        return <BaseSwitch checked={row.original.is_active ?? false} />;
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};

export const getColumnsManageSubCategory = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IGetLearningLevelResponse>[] => {
  return [
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.level}
          </span>
        );
      },
    },
    { accessorKey: "level_name", header: "Level Name" },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "updated_at",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.updated_at;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "is_active",
      header: "Status",
      cell({ row }) {
        return <BaseSwitch checked={row.original.is_active ?? false} />;
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
