import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import {
  useUpdateCategoryMutation,
  useUpdateSubCategoryMutation,
} from "@/services/mutation/admin/manage-category";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import { Trash2 } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

interface Props {
  fitur: string;
}

const ManageCategoryDeleteConfirmationModal = ({ fitur }: Readonly<Props>) => {
  const activeTab = useManageCategoryTabStore((state) => state.activeTab);

  const {
    openedCategory,
    openedSubCategory,
    openDeleteModal,
    setOpenDeleteModal,
    setOpenedCategory,
    setOpenedSubCategory,
  } = useManageCategoryModal(
    useShallow(
      ({
        openedCategory,
        openedSubCategory,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedCategory,
        setOpenedSubCategory,
      }) => ({
        openedCategory,
        openedSubCategory,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedCategory,
        setOpenedSubCategory,
      })
    )
  );

  const updateCategory = useUpdateCategoryMutation();
  const updateSubCategory = useUpdateSubCategoryMutation();

  const handleDeleteData = () => {
    if (activeTab === "category") {
      updateCategory.mutate(
        {
          id: openedCategory!.id,
          body: {
            category_name: openedCategory?.category_name!,
            is_deleted: true,
          },
        },
        {
          onSuccess: () => {
            handleOpenChange(false);
            toast.success("Category successfully deleted");
          },
          onError: () => {
            toast.error("Failed to delete Category");
          },
        }
      );

      return;
    }

    updateSubCategory.mutate(
      {
        id: openedSubCategory!.id,
        body: {
          subcategory_name: openedSubCategory?.subcategory_name!,
          is_deleted: true,
        },
      },
      {
        onSuccess: () => {
          handleOpenChange(false);
          toast.success("Sub category successfully deleted");
        },
        onError: () => {
          toast.error("Failed to delete Sub category");
        },
      }
    );
  };

  const handleOpenChange = (state: boolean) => {
    if (!state) {
      setOpenedCategory(null);
      setOpenedSubCategory(null);
    }
    setOpenDeleteModal(state);
  };

  return (
    <BaseDialog open={openDeleteModal} onOpenChange={handleOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus {fitur}?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            {fitur} yang dipilih akan dihapus secara permanen. Tindakan ini
            tidak bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={() => handleDeleteData()}
            disabled={updateCategory.isPending || updateSubCategory.isPending}
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ManageCategoryDeleteConfirmationModal;
