import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { useGetJobPositionStartingLevelListQuery } from "@/services/query/manage-job/starting-level";
import { useGetUserMasterEntity } from "@/services/query/user-management/user-entity";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { cn } from "@/utils/common";
import React, { useState } from "react";
import { useShallow } from "zustand/react/shallow";

interface IFilter {
  job_position_type: string | undefined;
  starting_learning_level: number | undefined;
  is_neop: boolean | undefined;
  is_welcoming_kit: boolean | undefined;
  starter_module_priority: string | undefined;
}

const ManageLearningPathFilterInput = () => {
  const { openFilter } = useManageLearningPathFilterStore(
    useShallow(({ openFilter }) => ({
      openFilter,
    }))
  );

  const [filter, setFilter] = useState<IFilter>({
    job_position_type: undefined,
    starting_learning_level: undefined,
    is_neop: undefined,
    is_welcoming_kit: undefined,
    starter_module_priority: undefined,
  });

  const handleChange = (
    key: keyof IFilter,
    value: string | number | boolean | undefined
  ) => {
    setFilter((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = () => {
    // setQuery({
    //   ...query,
    //   is_neop: filter.is_neop,
    //   level: filter.starting_learning_level,
    //   page: 1,
    //   job_position_type: filter.job_position_type,
    //   starter_modul_priority: filter.starter_module_priority,
    // });
  };

  const startingLearningLevels = useGetJobPositionStartingLevelListQuery({
    order: "desc",
    order_by: "name",
  });
  const entities = useGetUserMasterEntity({});

  const handleReset = () => {
    setFilter({
      job_position_type: undefined,
      starting_learning_level: undefined,
      is_neop: undefined,
      is_welcoming_kit: undefined,
      starter_module_priority: undefined,
    });

    // setQuery({
    //   ...query,
    //   is_neop: undefined,
    //   level: undefined,
    //   page: 1,
    //   job_position_type: undefined,
    //   starter_modul_priority: undefined,
    // });
  };

  return (
    <div
      hidden={!openFilter}
      className={cn("flex flex-col gap-4 w-full bg-white rounded-lg p-3")}
    >
      <div className="flex items-center justify-between">
        <span className="mt-1 font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            className="w-24 h-10 text-red-600 border-red-600 hover:text-red-600"
            variant={"outline"}
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton className="w-24 h-10" onClick={handleSubmit}>
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="flex gap-4 justify-between">
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">Job Position Type</BaseLabel>
          <BaseSelect
            value={filter.job_position_type?.toString() ?? ""}
            onValueChange={(value) => {
              handleChange("job_position_type", value);
            }}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {entities.data?.data.map((entity) => (
                <BaseSelectItem
                  key={entity.entity_name}
                  value={entity.entity_name?.toString() ?? ""}
                >
                  {entity.entity_name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">Starting Learning Level</BaseLabel>
          <BaseSelect
            value={filter.starting_learning_level?.toString() ?? ""}
            onValueChange={(value) =>
              handleChange("starting_learning_level", +value)
            }
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {startingLearningLevels.data?.data.map((level) => (
                <BaseSelectItem
                  key={level.id}
                  value={level.level?.toString() ?? ""}
                >
                  {level.name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">Is Need NEOP or Welcoming</BaseLabel>
          <BaseSelect
            value={(() => {
              if (filter.is_neop === true) return "neop_true";
              if (filter.is_neop === false) return "neop_false";
              if (filter.is_welcoming_kit === true) return "welcoming-kit_true";
              if (filter.is_welcoming_kit === false)
                return "welcoming-kit_false";
              return "";
            })()}
            onValueChange={(value) => {
              const splitted = value.split("_");
              const is_neop = splitted[0] === "neop";
              const result = splitted[1] === "true";

              if (is_neop) {
                handleChange("is_neop", result);
                handleChange("is_welcoming_kit", undefined);
              } else {
                handleChange("is_welcoming_kit", result);
                handleChange("is_neop", undefined);
              }
            }}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="neop_true">Need NEOP</BaseSelectItem>
              <BaseSelectItem value="neop_false">No Need NEOP</BaseSelectItem>
              <BaseSelectItem value="welcoming-kit_true">
                Need Welcoming Kit
              </BaseSelectItem>
              <BaseSelectItem value="welcoming-kit_false">
                No Need Welcoming Kit
              </BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs" htmlFor="neop">
            Starter Module Priority
          </BaseLabel>
          <BaseSelect
            value={filter.starter_module_priority ?? ""}
            onValueChange={(value) =>
              handleChange("starter_module_priority", value)
            }
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="neop">NEOP</BaseSelectItem>
              <BaseSelectItem value="welcoming kit">
                Welcoming Kit
              </BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>
  );
};

export default ManageLearningPathFilterInput;
