"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus, Settings2 } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ManageLearningLevelNewModal from "./new-learning-level-modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import { useManageLearningPathModal } from "@/store/admin/manage-learning-path/modal";
import { cn } from "@/lib/utils";
import { useManageLearningPathFilterStore } from "@/store/admin/manage-learning-path/filter";
import { useRouter } from "next/navigation";

const ManageCategoryTableHeaderFilter = () => {
  const { activeTab } = useManageLearningPathTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );

  const { setOpenAddLearningLevelModal } = useManageLearningPathModal(
    useShallow(({ setOpenAddLearningLevelModal }) => ({
      setOpenAddLearningLevelModal,
    }))
  );

  const { openFilter, setOpenFilter } = useManageLearningPathFilterStore(
    useShallow(({ openFilter, setOpenFilter }) => ({
      openFilter,
      setOpenFilter,
    }))
  );

  const router = useRouter();

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
      <BaseButton
        className="h-12 px-5"
        onClick={() => {
          if (activeTab === "learning-level") {
            setOpenAddLearningLevelModal(true);
          } else {
            router.push("/admin/manage-learning-path/learning-code/add");
          }
        }}
      >
        <div className="flex items-center gap-2">
          <Plus />
          {activeTab === "learning-code"
            ? "Add Learning Code"
            : "Add Learning Level"}
        </div>
      </BaseButton>
      <ManageLearningLevelNewModal />
    </div>
  );
};

export default ManageCategoryTableHeaderFilter;
