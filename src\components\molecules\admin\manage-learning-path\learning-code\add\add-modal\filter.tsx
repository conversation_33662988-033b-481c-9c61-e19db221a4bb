"use client";

import { BaseButton } from "@/components/atoms/button";
import { cn } from "@/utils/common";
import {
  DownloadCloud,
  Plus,
  RefreshCw,
  Settings2,
  UploadCloud,
} from "lucide-react";
import { useShallow } from "zustand/react/shallow";
import {
  BaseTooltip,
  BaseTooltipContent,
  BaseTooltipTrigger,
} from "@/components/atoms/tooltip";

import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";

import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useManageLearningPathModal } from "@/store/admin/manage-learning-path/modal";

const ManageJobTableHeaderFilter = () => {
  const { openFilter, setOpenFilter } = useManageJobFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  const { setOpenAddJobPosition, setOpenUploadHistory, setOpenUploadExcel } =
    useManageJobModalStore(
      useShallow(
        ({
          setOpenAddJobPosition,
          setOpenUploadHistory,
          setOpenUploadExcel,
        }) => ({
          setOpenAddJobPosition,
          setOpenUploadHistory,
          setOpenUploadExcel,
        })
      )
    );

  const { setOpenAddLearningLevelModal, openAddLearningLevelModal } =
    useManageLearningPathModal(
      useShallow(
        ({ openAddLearningLevelModal, setOpenAddLearningLevelModal }) => ({
          setOpenAddLearningLevelModal,
          openAddLearningLevelModal,
        })
      )
    );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
    </div>
  );
};

export default ManageJobTableHeaderFilter;
