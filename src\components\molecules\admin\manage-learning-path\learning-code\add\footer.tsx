"use client";

import { BaseButton } from "@/components/atoms/button";
import { useRouter } from "next/navigation";

const AddLearningCodeFooter = () => {
  const router = useRouter();

  return (
    <div className="flex justify-end gap-4 w-full bg-white px-3 py-3.5 rounded-lg mt-44">
      <BaseButton
        variant={"outline"}
        onClick={() => router.back()}
        className="h-11 px-8"
      >
        Cancel
      </BaseButton>
      <BaseButton className="h-11 px-8">Add Learning Code</BaseButton>
    </div>
  );
};

export default AddLearningCodeFooter;
