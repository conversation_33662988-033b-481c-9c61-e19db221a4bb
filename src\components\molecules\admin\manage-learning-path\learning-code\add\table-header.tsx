import React from "react";
import AddLearningCodeTableHeaderFilter from "./filter";
import AddLearningCodeTableHeaderSearch from "./search";
import AddLearningCodeFilterInput from "./filter-input";
import { Path, useFormContext } from "react-hook-form";
import { ICreateLearningLevelForm } from "@/interfaces/admin/manage-learning-path/new";
import { BaseLabel } from "@/components/atoms/label";
import { BaseInput } from "@/components/atoms/input";
import { isNumberInput } from "@/utils/common/number";

const AddLearningCodeTableHeader = () => {
  return (
    <div className="flex flex-col gap-7">
      <AddLearningCodeInput />
      <div className="flex flex-col gap-1">
        <span className="font-semibold">Related Job Position</span>
        <div className="flex justify-between items-center">
          <AddLearningCodeTableHeaderSearch />
          <AddLearningCodeTableHeaderFilter />
        </div>
      </div>
      <AddLearningCodeFilterInput />
    </div>
  );
};

const AddLearningCodeInput = () => {
  return (
    <div className="flex flex-col gap-1">
      <span className="font-semibold">Learning Code Information</span>
      <div className="grid grid-cols-3 gap-4">
        <InputNumber
          label="Learning Code"
          id="level"
          placeholder="input learning code"
        />
        <div className="col-span-2">
          <InputString
            label="Learning Code Name"
            id="level_name"
            placeholder="Input learning code name"
          />
        </div>
      </div>
    </div>
  );
};

const InputString = <T extends ICreateLearningLevelForm>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputNumber = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateLearningLevelForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateLearningLevelForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
        onKeyDown={(e) => {
          if (isNumberInput(e)) e.preventDefault();
        }}
        type="string"
      />
    </div>
  );
};

export default AddLearningCodeTableHeader;
