"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { BaseSwitch } from "@/components/atoms/switch";
import { Check, Pencil, Trash2 } from "lucide-react";
import { cn } from "@/utils/common";
import { BaseButton } from "@/components/atoms/button";
import { useManageJobModalStore } from "@/store/admin/manage-job/modal";
import { useShallow } from "zustand/react/shallow";
import { IGetJobPositionListResponse } from "@/interfaces/admin/manage-job/list";
import { useManageJobFilterStore } from "@/store/admin/manage-job/filter";
import { useGetJobPositionListQuery } from "@/services/query/manage-job/list";
import { useSetActiveJobPositionMutation } from "@/services/mutation/manage-job/set-active";
import toast from "react-hot-toast";
import ManageJobTableHeader from "./table-header";
import { DataTable } from "@/components/molecules/global/table";
import { FormProvider, useForm } from "react-hook-form";
import AddLearningCodeModal from "./add-modal/new";
import AddLearningCodeFooter from "./footer";

const AddLearningCodeTable = () => {
  const { setOpenDeleteJobPosition, setCurrentData, setOpenAddJobPosition } =
    useManageJobModalStore(
      useShallow(
        ({
          setOpenDeleteJobPosition,
          setCurrentData,
          setOpenAddJobPosition,
        }) => ({
          setOpenDeleteJobPosition,
          setCurrentData,
          setOpenAddJobPosition,
        })
      )
    );
  const { query, setQuery } = useManageJobFilterStore(
    useShallow(({ query, setQuery }) => ({
      query,
      setQuery,
    }))
  );

  const jobPositions = useGetJobPositionListQuery(query);
  const setActive = useSetActiveJobPositionMutation();

  const columns: ColumnDef<IGetJobPositionListResponse>[] = [
    {
      accessorKey: "job_id",
      header: "Job Position ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.job_id}
          </span>
        );
      },
    },
    { accessorKey: "job_name", header: "Job Position Name" },
    { accessorKey: "job_position_type", header: "Job Type" },
    { accessorKey: "department_name", header: "Department" },
    { accessorKey: "job_function", header: "Job Function" },
    {
      accessorKey: "level",
      header: "Starting Learning Level",
    },
    {
      accessorKey: "is_need_neop",
      header: "Need NEOP?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_neop
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "is_need_welcoming_kit",
      header: "Need Welcoming Kit?",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_welcoming_kit
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "starter_module_priority",
      header: "Starter Module Priority",
    },
    { accessorKey: "entity_name", header: "Entity" },

    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell: (props) => {
        return (
          <span>
            {props.row.original.last_updated
              ? dayjs(props.row.original.last_updated).format(
                  "DD MMM YYYY HH:mm"
                )
              : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "is_active",
      header: "Status",
      cell({ row }) {
        return (
          <BaseSwitch
            checked={row.original.is_active ?? false}
            onCheckedChange={(value) => handleSetActive(row.original.id, value)}
          />
        );
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => {
                setCurrentData(row.original.id);
                setOpenAddJobPosition(true);
              }}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => {
                setCurrentData(row.original.id);
                setOpenDeleteJobPosition(true);
              }}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (page: number) => {
    setQuery({ ...query, page });
  };

  const handleSetActive = (id: number, isActive: boolean) => {
    setActive.mutate(
      {
        is_active: isActive,
        params: {
          id,
        },
      },
      {
        onSuccess: (data) => {
          toast.success(data.message);
          jobPositions.refetch();
        },
        onError: (data) => {
          toast.error(data.message);
        },
      }
    );
  };

  const form = useForm();

  return (
    <FormProvider {...form}>
      <form
        onSubmit={form.handleSubmit(() => {})}
        className="flex flex-col gap-4"
      >
        <div className="bg-white p-2 rounded-md flex flex-col gap-4">
          <ManageJobTableHeader />

          <div className="flex flex-col justify-center items-center w-full h-[49dvh]  bg-gray-100 text-gray-400 rounded-md">
            <span>No Job Position selected</span>
            <span>Please select the Job Position first</span>
          </div>

          {/* <DataTable
            columns={columns}
            data={jobPositions.data?.data ?? []}
            pagination={jobPositions.data?.pagination}
            onPageChange={handlePageChange}
          /> */}
        </div>
        <AddLearningCodeFooter />
      </form>
      <AddLearningCodeModal />
    </FormProvider>
  );
};

export default AddLearningCodeTable;
