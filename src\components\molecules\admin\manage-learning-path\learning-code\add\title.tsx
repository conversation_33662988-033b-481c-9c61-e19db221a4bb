"use client";

import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

const AddLearningCodeTitle = () => {
  const router = useRouter();

  return (
    <div className="flex justify-between gap-4 w-full">
      <button
        className="bg-white text-[#3C3C3C] p-3 font-semibold rounded-lg flex-none cursor-pointer hover:bg-gray-200"
        onClick={() => router.back()}
      >
        <ArrowLeft />
      </button>
      <div className="bg-white text-[#3C3C3C] grow p-3 font-semibold rounded-lg w-full">
        Add Learning Code
      </div>
    </div>
  );
};

export default AddLearningCodeTitle;
