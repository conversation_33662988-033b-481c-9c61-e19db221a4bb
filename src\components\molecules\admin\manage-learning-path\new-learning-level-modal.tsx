import { BaseButton } from "@/components/atoms/button";
import {
  BaseCommand,
  BaseCommandEmpty,
  BaseCommandGroup,
  BaseCommandInput,
  BaseCommandItem,
  BaseCommandList,
} from "@/components/atoms/command";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BasePopover,
  BasePopoverContent,
  BasePopoverTrigger,
} from "@/components/atoms/popover";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";
import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";
import {
  createCategoryBodySchema,
  createSubCategoryBodySchema,
  ICreateCategoryBody,
  ICreateSubCategoryBody,
} from "@/interfaces/admin/manage-category/new";
import {
  createLearningLevelFormSchema,
  ICreateLearningLevelForm,
} from "@/interfaces/admin/manage-learning-path/new";
import { cn } from "@/lib/utils";

import { useManageLearningPathModal } from "@/store/admin/manage-learning-path/modal";
import { isNumberInput } from "@/utils/common/number";
import { yupResolver } from "@hookform/resolvers/yup";
import { Check, ChevronsUpDown } from "lucide-react";
import React from "react";
import {
  useForm,
  useFormContext,
  Path,
  FormProvider,
  Controller,
} from "react-hook-form";
import { useShallow } from "zustand/react/shallow";

const ManageLearningLevelNewModal = () => {
  const { setOpenAddLearningLevelModal, openAddLearningLevelModal } =
    useManageLearningPathModal(
      useShallow(
        ({ openAddLearningLevelModal, setOpenAddLearningLevelModal }) => ({
          setOpenAddLearningLevelModal,
          openAddLearningLevelModal,
        })
      )
    );

  const title = "Add Learning Level";

  const form = useForm({
    resolver: yupResolver(createLearningLevelFormSchema),
  });

  const handleOpenChangeModal = (state: boolean) => {
    // if (!state) {
    //   setOpenedCategory(null);
    //   setOpenedSubCategory(null);
    // }

    setOpenAddLearningLevelModal(state);
  };

  return (
    <BaseDialog
      open={openAddLearningLevelModal}
      onOpenChange={handleOpenChangeModal}
    >
      <BaseDialogContent className="min-w-2/5">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{title}</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <FormProvider {...form}>
          <form className="flex flex-col gap-6">
            <div className="grid grid-cols-2 gap-4">
              <InputNumber id="level" label="Level" placeholder="Input level" />
              <InputSelect
                id="status_id"
                label="Status"
                placeholder="Select status"
                options={[
                  { value: "1", label: "Active" },
                  { value: "2", label: "Inactive" },
                ]}
              />
              <div className="col-span-2">
                <InputString
                  id="level_name"
                  label="Level Name"
                  placeholder="Input level name"
                />
              </div>
            </div>
            <BaseSeparator className="mt-4 -mb-2" />
            <div className="flex justify-end gap-3 -mb-3">
              <DialogClose asChild>
                <BaseButton
                  className="h-11 w-32"
                  variant={"outline"}
                  // onClick={() => setOpenAddUser(false)}
                >
                  Cancel
                </BaseButton>
              </DialogClose>
              <BaseButton
                className="h-11 w-32"
                type="submit"
                disabled={
                  Object.keys(form.formState.errors).length > 0 ||
                  !form.formState.isValid
                }
              >
                {/* {currentData ? "Save and Verify" : "Add User"} */}
                Add Level
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: ICategory | ISubCategory | null;
  onCloseModal: VoidFunction;
}

const InputString = <T extends ICreateLearningLevelForm>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputNumber = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateLearningLevelForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateLearningLevelForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
        onKeyDown={(e) => {
          if (isNumberInput(e)) e.preventDefault();
        }}
        type="string"
      />
    </div>
  );
};

const InputSelect = ({
  label,
  id,
  placeholder,
  options,
  optional = false,
}: {
  label: string;
  id: Path<ICreateLearningLevelForm>;
  placeholder?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateLearningLevelForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>

      <Controller
        name={id as any}
        control={form.control}
        render={({ field }) => {
          return (
            <BasePopover>
              <BasePopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className="w-full min-h-11 justify-between"
                >
                  {field.value != null
                    ? options.find((o) => o.value === String(field.value))
                        ?.label ?? placeholder
                    : placeholder}

                  <ChevronsUpDown className="opacity-50" />
                </BaseButton>
              </BasePopoverTrigger>
              <BasePopoverContent className="w-full p-0">
                <BaseCommand>
                  <BaseCommandInput
                    placeholder="Search data..."
                    className="h-9"
                  />
                  <BaseCommandList>
                    <BaseCommandEmpty>No data found.</BaseCommandEmpty>
                    <BaseCommandGroup>
                      {options.map((option) => (
                        <BaseCommandItem
                          key={`${id}-${option.value}`}
                          value={`${option.label}__${option.value}`}
                          onSelect={(currentValue) => {
                            field.onChange(currentValue.split("__")[1]);
                          }}
                        >
                          {option.label}
                          <Check
                            className={cn(
                              "ml-auto",
                              field.value === option.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </BaseCommandItem>
                      ))}
                    </BaseCommandGroup>
                  </BaseCommandList>
                </BaseCommand>
              </BasePopoverContent>
            </BasePopover>
          );
        }}
      />
    </div>
  );
};

export default ManageLearningLevelNewModal;
