import React from "react";
import ManageLearningPathTableHeaderSearch from "./search";
import ManageLearningPathTableHeaderFilter from "./filter";
import ManageLearningPathFilterInput from "./filter-input";

const ManageLearningPathTableHeader = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ManageLearningPathTableHeaderSearch />
        <ManageLearningPathTableHeaderFilter />
      </div>
      <ManageLearningPathFilterInput />
    </div>
  );
};

export default ManageLearningPathTableHeader;
