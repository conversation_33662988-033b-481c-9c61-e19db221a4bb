"use client";

import React from "react";
import { DataTable } from "../../global/table";
import ManageLearningPathTableHeader from "./table-header";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import {
  useGetListCategoryQuery,
  useGetListSubCategoryQuery,
} from "@/services/query/admin/manage-category";
import {
  getColumnsManageLearningCode,
  getColumnsManageSubCategory,
} from "./column";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useShallow } from "zustand/react/shallow";
import { ISubCategory } from "@/interfaces/admin/manage-category/list";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import ManageCategoryDeleteConfirmationModal from "./delete-category-confirmation-modal";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";

const ManageLearningPathTable = () => {
  const activeTab = useManageLearningPathTabStore((state) => state.activeTab);
  const {
    setOpenAddModal,
    setOpenedCategory,
    setOpenedSubCategory,
    setOpenDeleteModal,
  } = useManageCategoryModal(
    useShallow(
      ({
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
        setOpenDeleteModal,
      }) => ({
        setOpenAddModal,
        setOpenedCategory,
        setOpenedSubCategory,
        setOpenDeleteModal,
      })
    )
  );
  const {
    categoryQuery,
    subCategoryQuery,
    setCategoryQuery,
    setSubCategoryQuery,
  } = useManageCategoryQueryStore(
    useShallow(
      ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      }) => ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      })
    )
  );

  const category = useGetListCategoryQuery(
    categoryQuery,
    activeTab === "learning-code"
  );

  const subCategory = useGetListSubCategoryQuery(
    subCategoryQuery,
    activeTab === "learning-level"
  );

  const learningPathColumns = React.useMemo(
    () =>
      getColumnsManageLearningCode({
        onEdit: (data) => {
          // setOpenedCategory(data);
          // setOpenAddModal(true);
        },
        onDelete: (data) => {
          // setOpenedCategory(data);
          // setOpenDeleteModal(true);
        },
      }),
    [category.data]
  );

  const learningLevelColumns = React.useMemo(
    () =>
      getColumnsManageSubCategory({
        onEdit: (data) => {
          // setOpenedSubCategory(data as ISubCategory);
          // setOpenAddModal(true);
        },
        onDelete: (data) => {
          // setOpenedSubCategory(data as ISubCategory);
          // setOpenDeleteModal(true);
        },
      }),
    [subCategory.data]
  );

  return (
    <div className="flex flex-col gap-4 h-full">
      <ManageLearningPathTableHeader />
      <ManageCategoryDeleteConfirmationModal
        fitur={
          activeTab === "learning-code" ? "Learning Code" : "Learning Level"
        }
      />

      {activeTab === "learning-code" ? (
        <DataTable
          columns={learningPathColumns}
          data={[]}
          pagination={category.data?.pagination}
          onPageChange={(page) => setCategoryQuery({ page })}
        />
      ) : (
        <DataTable
          columns={learningLevelColumns}
          data={[]}
          pagination={subCategory.data?.pagination}
          onPageChange={(page) => setSubCategoryQuery({ page })}
        />
      )}
    </div>
  );
};

export default ManageLearningPathTable;
