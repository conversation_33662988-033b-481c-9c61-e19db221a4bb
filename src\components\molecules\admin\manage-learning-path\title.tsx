"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { useManageLearningPathTabStore } from "@/store/admin/manage-learning-path/tab";
import { useShallow } from "zustand/react/shallow";

const ManageLearningPathTitle = () => {
  const { activeTab, setActiveTab } = useManageLearningPathTabStore(
    useShallow(({ activeTab, setActiveTab }) => ({ activeTab, setActiveTab }))
  );

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Learning Path
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={activeTab}
          onValueChange={(val) => {
            setActiveTab(val as "learning-code" | "learning-level");
          }}
        >
          <BaseTabsList className="w-full h-12">
            <BaseTabsTrigger value="learning-code">
              Learning Code
            </BaseTabsTrigger>
            <BaseTabsTrigger value="learning-level">
              Learning Level
            </BaseTabsTrigger>
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default ManageLearningPathTitle;
