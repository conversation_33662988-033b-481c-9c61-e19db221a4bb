import { BaseButton } from "@/components/atoms/button";
import { IMaterial } from "@/interfaces/admin/manage-material/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2, Download, ChevronDown, FileText } from "lucide-react";
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import IconMaterialVideo from "@/assets/icons/IconMaterialVideo";
import PillDropdown from "./form/pill-dropdown";

interface Props {
  onEdit: (material: IMaterial) => void;
  onDelete: (material: IMaterial) => void;
  onDownload: (material: IMaterial) => void;
  onPreview?: (material: IMaterial) => void;
  onUpdateField?: (id: string, field: keyof IMaterial, value: any) => void;
}

export const getMaterialColumns = ({
  onEdit,
  onDelete,
  onDownload,
  onPreview,
  onUpdateField,
}: Props): ColumnDef<IMaterial>[] => {
  // Sample data for dropdowns
  const categories = ["Learning Material", "Assessment", "Tutorial", "Guide"];
  const levels = ["Beginner", "Intermediate", "Advanced"];

  return [
    {
      accessorKey: "document_id",
      header: "Document ID",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">
            {row.original.document_id}
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.category}
            options={categories.map((category) => ({
              value: category,
              id: row.original.id,
            }))}
            onUpdateField={onUpdateField}
            id={row.original.id}
          />
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.level}
            options={levels.map((level) => ({
              value: level,
              id: row.original.id,
            }))}
            onUpdateField={onUpdateField}
            id={row.original.id}
          />
        );
      },
    },
    {
      accessorKey: "document_name",
      header: "Document Name",
      cell({ row }) {
        return (
          <div className="flex items-center gap-2 group">
            <IconMaterialVideo />
            <button
              onClick={() => onPreview?.(row.original)}
              className="text-xs underline text-blue-600 hover:text-blue-800 cursor-pointer transition-colors"
            >
              {row.original.document_name}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: "size",
      header: "Size",
      cell({ row }) {
        return <div className="text-muted-foreground">{row.original.size}</div>;
      },
    },
    {
      accessorKey: "associated_sections",
      header: "Associated Sections",
      cell({ row }) {
        const sections = row.original.associated_sections || [];
        return (
          <PillDropdown
            selected={`${row.original.associated_sections.length} Sections`}
            options={sections.map((section) => ({
              value: section,
              id: row.original.id,
            }))}
            onUpdateField={onUpdateField}
            id={row.original.id}
          />
        );
      },
    },
    {
      accessorKey: "uploaded_at",
      header: "Uploaded At",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground">
            {row.original.uploaded_at
              ? dayjs(row.original.uploaded_at).format("DD MMM YYYY")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "uploaded_by",
      header: "Uploaded By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.uploaded_by || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "updated_at",
      header: "Last Updated",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground">
            {row.original.updated_at
              ? dayjs(row.original.updated_at).format("DD MMM YYYY")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "updated_by",
      header: "Updated By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.updated_by || "-"}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-end gap-1.5">
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onDownload(row.original)}
              title="Download"
            >
              <Download className="h-4 w-4 text-gray-500" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onEdit(row.original)}
              title="Edit"
            >
              <Pencil className="h-4 w-4 text-gray-500" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10"
              onClick={() => onDelete(row.original)}
              title="Delete"
            >
              <Trash2 className="h-4 w-4" />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
