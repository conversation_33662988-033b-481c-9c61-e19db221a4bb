import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";
import React from "react";

export interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  title: string;
  description: string;
  isLoading?: boolean;
}

const DeleteConfirmationModal = ({
  isOpen,
  onOpenChange,
  onConfirm,
  title,
  description,
  isLoading = false,
}: DeleteConfirmationModalProps) => {
  const handleDelete = () => {
    console.log("Deleting material...");
    onConfirm();
  };

  return (
    <BaseDialog open={isOpen} onOpenChange={onOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            {title}
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            {description}
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Batal
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? "Menghapus..." : "Hapus"}
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default DeleteConfirmationModal;
