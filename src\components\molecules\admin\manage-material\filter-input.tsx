"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { cn } from "@/utils/common";
import { useState } from "react";
import { BaseButton } from "@/components/atoms/button";

interface IFilter {
  category: string | undefined;
  level: string | undefined;
}

// Dummy data based on tab
const filterOptions = {
  // For "Materi" tab
  video: {
    categories: [
      { value: "all", label: "All Categories" },
      { value: "safety", label: "Safety" },
      { value: "technical", label: "Technical" },
      { value: "soft_skills", label: "Soft Skills" },
    ],
    levels: [
      { value: "all", label: "All Levels" },
      { value: "beginner", label: "Beginner" },
      { value: "intermediate", label: "Intermediate" },
      { value: "advanced", label: "Advanced" },
    ],
  },
  // For "Kuis" tab
  audio: {
    categories: [
      { value: "all", label: "All Categories" },
      { value: "pre_test", label: "Pre-Test" },
      { value: "post_test", label: "Post-Test" },
      { value: "evaluation", label: "Evaluation" },
    ],
    levels: [
      { value: "all", label: "All Difficulties" },
      { value: "easy", label: "Easy" },
      { value: "medium", label: "Medium" },
      { value: "hard", label: "Hard" },
    ],
  },
  // For "Ujian" tab
  document: {
    categories: [
      { value: "all", label: "All Types" },
      { value: "mid_term", label: "Mid Term" },
      { value: "final", label: "Final Exam" },
      { value: "certification", label: "Certification" },
    ],
    levels: [
      { value: "all", label: "All Levels" },
      { value: "basic", label: "Basic" },
      { value: "intermediate", label: "Intermediate" },
      { value: "advanced", label: "Advanced" },
    ],
  },
};

interface Props {
  activeTab: string;
  onFilterChange: (filters: { category?: string; level?: string }) => void;
}

const ManageMaterialFilterInput = ({ activeTab, onFilterChange }: Props) => {
  const [filter, setFilter] = useState<IFilter>({
    category: "all",
    level: "all",
  });

  const handleFilterChange = (key: keyof IFilter, value: string) => {
    const newFilter = {
      ...filter,
      [key]: value,
    };
    setFilter(newFilter);
  };

  const handleReset = () => {
    const resetFilter = {
      category: "all",
      level: "all",
    };
    setFilter(resetFilter);
  };

  const currentOptions =
    filterOptions[activeTab as keyof typeof filterOptions] ||
    filterOptions.video;

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton
            variant="outline"
            className="text-blue-600 border-blue-600 hover:text-blue-600 h-9"
            onClick={() => {
              onFilterChange(filter);
            }}
          >
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Category Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Category</BaseLabel>
          <BaseSelect
            value={filter.category}
            onValueChange={(value) => handleFilterChange("category", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select category" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {currentOptions.categories.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Level Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Level</BaseLabel>
          <BaseSelect
            value={filter.level}
            onValueChange={(value) => handleFilterChange("level", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select level" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {currentOptions.levels.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>
  );
};

export default ManageMaterialFilterInput;
