"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ManageMaterialNewModal from "./new-modal";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import FilterButton from "@/components/atoms/filter-button";
import { MaterialType } from "./form/file-uploader";

const TITLE = {
  video: "Video",
  audio: "Audio",
  document: "Document",
  scorm: "SCORM",
};

interface Props {
  filterOpen: boolean;
  setFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ManageMaterialTableHeaderFilter = ({
  filterOpen,
  setFilterOpen,
}: Readonly<Props>) => {
  const { activeTab } = useManageMaterialTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );

  const { setOpenAddModal, openAddModal } = useManageCategoryModal(
    useShallow(({ setOpenAddModal, openAddModal }) => ({
      setOpenAddModal,
      openAddModal,
    }))
  );

  return (
    <div className="flex items-center justify-end gap-3">
      <FilterButton
        className="h-12"
        active={filterOpen}
        onClick={() => setFilterOpen((prev) => !prev)}
      />
      <BaseButton className="h-12 px-5" onClick={() => setOpenAddModal(true)}>
        <div className="flex items-center gap-2">
          <Plus />
          Add New {TITLE[activeTab]}
        </div>
      </BaseButton>
      <ManageMaterialNewModal
        isOpen={openAddModal}
        onClose={() => setOpenAddModal(false)}
        type={activeTab as MaterialType}
      />
    </div>
  );
};

export default ManageMaterialTableHeaderFilter;
