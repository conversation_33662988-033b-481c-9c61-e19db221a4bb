"use client";

import { BaseLabel } from "@/components/atoms/label";
import { Controller } from "react-hook-form";
import MultipleSelector from "@/components/atoms/multiselect";
interface CategorySelectorProps {
  onSelectAll: () => void;
  form: any;
}

// Dummy data
export const DUMMY_CATEGORIES = [
  { id: "1", name: "DI Yogyakarta" },
  { id: "2", name: "Bali" },
  { id: "3", name: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: "4", name: "Jawa Barat" },
  { id: "5", name: "Jawa Tengah" },
  { id: "6", name: "<PERSON><PERSON> Timur" },
];

const CategorySelector = ({ onSelectAll, form }: CategorySelectorProps) => {
  const selectedCategories = form.watch("categories");
  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">Category</BaseLabel>

      <Controller
        name="categories"
        control={form.control}
        render={({ field }) => (
          <MultipleSelector
            value={field.value}
            options={DUMMY_CATEGORIES.map((category) => ({
              label: category.name,
              value: category.id,
            }))}
            onChange={field.onChange}
            placeholder="Select Tag"
            badgeClassName="bg-base-gray-20 text-comp-content-primary"
            loadingIndicator={
              <p className="py-2 text-center text-lg leading-10 text-muted-foreground">
                loading...
              </p>
            }
            emptyIndicator={
              <p className="w-full text-center text-lg leading-10 text-muted-foreground">
                no results found.
              </p>
            }
          />
        )}
      />
      {/* Select All Checkbox */}
      <label className="flex items-center gap-2 cursor-pointer">
        <input
          type="checkbox"
          checked={selectedCategories.length === DUMMY_CATEGORIES.length}
          onChange={onSelectAll}
          className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
        />
        <span className="text-sm text-gray-700">Select All Category</span>
      </label>
    </div>
  );
};

export default CategorySelector;
