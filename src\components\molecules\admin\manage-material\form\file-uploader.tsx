"use client";

import { BaseLabel } from "@/components/atoms/label";
import React from "react";
import { Upload } from "lucide-react";

export const FILE_CONFIGS = {
  video: {
    title: "Add New Video",
    uploadLabel: "Upload Video",
    acceptedTypes: ".mp4",
    maxSize: "120MB",
    supportedText: "Supported file types: MP4, max. size 120MB",
    icon: "🎥",
  },
  audio: {
    title: "Add New Audio",
    uploadLabel: "Upload Audio",
    acceptedTypes: ".mp3",
    maxSize: "50MB",
    supportedText: "Supported file types: MP3, max. size 50MB",
    icon: "🎵",
  },
  document: {
    title: "Add New Document",
    uploadLabel: "Upload Document",
    acceptedTypes: ".pdf",
    maxSize: "10MB",
    supportedText: "Supported file types: PDF, max. size 10MB",
    icon: "📄",
  },
  scorm: {
    title: "Add New SCORM",
    uploadLabel: "Upload SCORM",
    acceptedTypes: ".zip",
    maxSize: "10MB",
    supportedText: "Supported file types: ZIP, max. size 10MB",
    icon: "📦",
  },
};

export type MaterialType = keyof typeof FILE_CONFIGS;

interface FileUploaderProps {
  type: MaterialType;
  config: (typeof FILE_CONFIGS)[MaterialType];
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const FileUploader = ({ type, config, onFileUpload }: FileUploaderProps) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">
        {config.uploadLabel}
      </BaseLabel>

      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
        <div className="flex flex-col items-center gap-4">
          <Upload className="h-12 w-12 text-gray-400" />
          <div className="space-y-2">
            <p className="text-gray-600">
              <span className="font-medium">Drag & drop</span> your files or{" "}
              <button
                type="button"
                onClick={handleBrowseClick}
                className="text-orange-500 hover:text-orange-600 font-medium underline"
              >
                browse
              </button>
            </p>
            <p className="text-sm text-gray-500">{config.supportedText}</p>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept={config.acceptedTypes}
          multiple
          onChange={onFileUpload}
          className="hidden"
        />
      </div>
    </div>
  );
};

export default FileUploader;
