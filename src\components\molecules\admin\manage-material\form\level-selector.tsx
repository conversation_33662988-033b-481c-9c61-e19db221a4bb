"use client";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";

interface LevelSelectorProps {
  form: any;
}

const DUMMY_LEVELS = [
  { id: "beginner", name: "<PERSON><PERSON><PERSON>" },
  { id: "intermediate", name: "Intermediate" },
  { id: "advanced", name: "Advanced" },
];

const LevelSelector = ({ form }: LevelSelectorProps) => {
  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">Level</BaseLabel>
      <BaseSelect
        value={form.watch("level")}
        onValueChange={(value) => form.setValue("level", value)}
      >
        <BaseSelectTrigger className="w-full h-12">
          <BaseSelectValue placeholder="Select Level" />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {DUMMY_LEVELS.map((level) => (
            <BaseSelectItem key={level.id} value={level.id}>
              {level.name}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};

export default LevelSelector;
