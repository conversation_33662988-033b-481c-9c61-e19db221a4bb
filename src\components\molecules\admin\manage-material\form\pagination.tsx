interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalFiles: number;
  onPageChange: (page: number) => void;
}

const Pagination = ({
  currentPage,
  totalPages,
  totalFiles,
  onPageChange,
}: PaginationProps) => {
  const startEntry = (currentPage - 1) * 3 + 1;
  const endEntry = Math.min(currentPage * 3, totalFiles);

  return (
    <div className="flex items-center justify-between pt-4">
      <p className="text-sm text-gray-700">
        Showing {startEntry} to {endEntry} of {totalFiles} entries
      </p>

      <div className="flex items-center gap-1">
        <button
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
        >
          ←
        </button>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`px-3 py-1 rounded ${
              page === currentPage
                ? "bg-gray-200 text-gray-900"
                : "text-gray-600 hover:bg-gray-100"
            }`}
          >
            {page}
          </button>
        ))}

        <span className="px-2 text-gray-400">...</span>
        <button className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded">
          {totalPages}
        </button>

        <button
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
        >
          →
        </button>
      </div>
    </div>
  );
};

export default Pagination;
