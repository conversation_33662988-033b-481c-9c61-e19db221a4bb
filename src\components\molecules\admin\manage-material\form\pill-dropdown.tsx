import { IMaterial } from "@/interfaces/admin/manage-material/list";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const PillDropdown = ({
  selected,
  options,
  onUpdateField,
}: {
  selected: string;
  options: Array<{
    value: string;
    id: string;
  }>;
  id: string;
  onUpdateField?: (id: string, field: keyof IMaterial, value: any) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-1 cursor-pointer bg-[#DEDEDE] px-2 py-1 text-xs text-comp-content-primary max-w-max rounded-full">
          <span>{selected}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onSelect={() =>
              onUpdateField?.(option.id, "category", option.value)
            }
          >
            {option.value}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PillDropdown;
