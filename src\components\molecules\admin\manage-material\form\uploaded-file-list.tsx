"use client";
import { BaseLabel } from "@/components/atoms/label";
import { RotateCcw, Trash2 } from "lucide-react";

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: "uploading" | "success" | "error";
  progress?: number;
  uploadDate?: string;
}

interface UploadedFilesListProps {
  files: UploadedFile[];
  onRetry: (fileId: string) => void;
  onDelete: (fileId: string) => void;
}

const UploadedFilesList = ({
  files,
  onRetry,
  onDelete,
}: UploadedFilesListProps) => {
  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">Uploaded Files</BaseLabel>

      <div className="space-y-3">
        {files.map((file) => (
          <div
            key={file.id}
            className="flex items-center gap-3 p-3 border rounded-lg"
          >
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-purple-100 rounded flex items-center justify-center">
                <span className="text-purple-600 text-lg">🎥</span>
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate max-w-[320px] overflow-ellipsis">
                {file.name}
              </p>
              {file.status === "uploading" && (
                <div className="mt-1">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${file.progress || 0}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-500">
                      {file.progress || 0}%
                    </span>
                  </div>
                </div>
              )}
              {file.status === "error" && (
                <p className="text-xs text-red-500 mt-1">
                  Error uploading file
                </p>
              )}
              {file.status === "success" && file.uploadDate && (
                <p className="text-xs text-gray-500 mt-1">
                  {file.uploadDate} • {Math.round(file.size / 1024)} KB
                </p>
              )}
            </div>

            <div className="flex items-center gap-2">
              {file.status === "uploading" && (
                <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
              )}
              {file.status === "error" && (
                <button
                  type="button"
                  onClick={() => onRetry(file.id)}
                  className="p-1 text-orange-500 hover:bg-orange-50 rounded"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              )}
              <button
                type="button"
                onClick={() => onDelete(file.id)}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UploadedFilesList;
