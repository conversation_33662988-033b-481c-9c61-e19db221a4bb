import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import React from "react";
import { useForm, FormProvider } from "react-hook-form";
import LevelSelector from "./form/level-selector";
import CategorySelector, { DUMMY_CATEGORIES } from "./form/category-selector";
import UploadedFilesList, { UploadedFile } from "./form/uploaded-file-list";
import FileUploader, { FILE_CONFIGS, MaterialType } from "./form/file-uploader";
import Pagination from "./form/pagination";

interface Option {
  value: string;
  label: string;
}
interface FormData {
  categories: Option[];
  level: string;
  files: UploadedFile[];
}

// Main Modal Component
interface ManageMaterialNewModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: MaterialType;
}

const ManageMaterialNewModal = ({
  isOpen,
  onClose,
  type,
}: ManageMaterialNewModalProps) => {
  const config = FILE_CONFIGS[type];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="w-full max-w-[600px] sm:max-w-[808px] max-h-[90vh] overflow-y-auto">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex items-center justify-between">
            <span>{config.title}</span>
          </BaseDialogTitle>
        </BaseDialogHeader>
        <MaterialForm type={type} onClose={onClose} />
      </BaseDialogContent>
    </BaseDialog>
  );
};

// Form Component
interface MaterialFormProps {
  type: MaterialType;
  onClose: () => void;
}

const MaterialForm = ({ type, onClose }: MaterialFormProps) => {
  const form = useForm<FormData>({
    defaultValues: {
      categories: [],
      level: "",
      files: [],
    },
  });

  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const filesPerPage = 3;

  const config = FILE_CONFIGS[type];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    files.forEach((file) => {
      const newFile: UploadedFile = {
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        status: "uploading",
        progress: 0,
      };

      setUploadedFiles((prev) => [...prev, newFile]);

      // Simulate upload progress
      const interval = setInterval(() => {
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === newFile.id
              ? {
                  ...f,
                  progress: Math.min((f.progress || 0) + 10, 100),
                  status: (f.progress || 0) >= 90 ? "success" : "uploading",
                  uploadDate:
                    (f.progress || 0) >= 90 ? "22 Jun, 2022" : undefined,
                }
              : f
          )
        );
      }, 200);

      setTimeout(() => clearInterval(interval), 2000);
    });
  };

  const handleRetryUpload = (fileId: string) => {
    setUploadedFiles((prev) =>
      prev.map((f) =>
        f.id === fileId ? { ...f, status: "uploading", progress: 0 } : f
      )
    );
  };

  const handleDeleteFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleSelectAllCategories = () => {
    const isAllSelected =
      form.watch("categories").length === DUMMY_CATEGORIES.length;
    form.setValue(
      "categories",
      isAllSelected
        ? []
        : DUMMY_CATEGORIES.map((cat) => ({
            value: cat.id,
            label: cat.name,
          }))
    );
  };

  const onSubmit = (data: FormData) => {
    const submitData = {
      ...data,
      categories: form.watch("categories"),
      files: uploadedFiles,
    };

    console.log("Form submitted:", submitData);
    onClose();
  };

  const totalFiles = uploadedFiles.length;
  const totalPages = Math.ceil(totalFiles / filesPerPage);
  const startIndex = (currentPage - 1) * filesPerPage;
  const endIndex = startIndex + filesPerPage;
  const currentFiles = uploadedFiles.slice(startIndex, endIndex);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Category Selection */}
        <CategorySelector onSelectAll={handleSelectAllCategories} form={form} />

        {/* Level Selection */}
        <LevelSelector form={form} />

        {/* File Upload */}
        <FileUploader
          type={type}
          config={config}
          onFileUpload={handleFileUpload}
        />

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <UploadedFilesList
            files={currentFiles}
            onRetry={handleRetryUpload}
            onDelete={handleDeleteFile}
          />
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalFiles={totalFiles}
            onPageChange={setCurrentPage}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <DialogClose asChild>
            <BaseButton variant="outline" className="px-8">
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            type="submit"
            className="px-8 bg-orange-500 hover:bg-orange-600"
          >
            {config.title.replace("Add New ", "Add ")}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

export default ManageMaterialNewModal;
