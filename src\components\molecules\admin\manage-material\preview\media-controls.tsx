import React from "react";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  MoreVertical,
} from "lucide-react";

interface MediaControlsProps {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  progressPercentage: number;
  isMuted: boolean;
  onTogglePlay: () => void;
  onToggleMute: () => void;
  onTimeUpdate: (e: React.MouseEvent<HTMLDivElement>) => void;
  onFullscreen?: () => void;
  showFullscreen?: boolean;
}

export const MediaControls = ({
  isPlaying,
  currentTime,
  duration,
  progressPercentage,
  isMuted,
  onTogglePlay,
  onToggleMute,
  onTimeUpdate,
  onFullscreen,
  showFullscreen = true,
}: MediaControlsProps) => {
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="flex items-center gap-4 p-4 bg-white border-t">
      <button
        onClick={onTogglePlay}
        className="flex-shrink-0 flex items-center justify-centertransition-colors"
      >
        {isPlaying ? (
          <Pause className="h-5 w-5 fill-black" />
        ) : (
          <Play className="h-5 w-5 fill-black" />
        )}
      </button>

      <div className="flex-1 flex items-center gap-3">
        {/* <span className="text-xs text-gray-500 w-10 text-right">
          {formatTime(currentTime)}
        </span> */}
        <div
          className="flex-1 h-2 bg-gray-200 rounded-full cursor-pointer"
          onClick={onTimeUpdate}
        >
          <div
            className="h-full bg-orange-500 rounded-full transition-all duration-150"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        {/* <span className="text-xs text-gray-500 w-10">
          {formatTime(duration)}
        </span> */}
      </div>

      <div className="flex items-center gap-2">
        <button
          onClick={onToggleMute}
          className="text-gray-600 hover:text-orange-500 transition-colors p-1"
        >
          {isMuted ? (
            <VolumeX className="h-5 w-5" />
          ) : (
            <Volume2 className="h-5 w-5" />
          )}
        </button>

        {showFullscreen && onFullscreen && (
          <button
            onClick={onFullscreen}
            className="text-gray-600 hover:text-orange-500 transition-colors p-1"
          >
            <Maximize className="h-5 w-5" />
          </button>
        )}

        <button className="text-gray-600 hover:text-orange-500 transition-colors p-1">
          <MoreVertical className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default MediaControls;
