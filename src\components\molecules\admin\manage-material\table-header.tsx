"use client";
import React from "react";
import ManageMaterialTableHeaderSearch from "./search";
import ManageMaterialTableHeaderFilter from "./filter";
import ManageMaterialFilterInput from "./filter-input";
import { useState } from "react";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useShallow } from "zustand/react/shallow";

const ManageMaterialTableHeader = () => {
  const [filterOpen, setFilterOpen] = useState(false);
  const { activeTab } = useManageMaterialTabStore(
    useShallow(({ activeTab }) => ({ activeTab }))
  );

  const onFilterChange = (filters: { category?: string; level?: string }) => {
    console.log("Filter changed:", filters);
    // TODO: Apply filters to the table
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ManageMaterialTableHeaderSearch />
        <ManageMaterialTableHeaderFilter
          filterOpen={filterOpen}
          setFilterOpen={setFilterOpen}
        />
      </div>
      {filterOpen && (
        <ManageMaterialFilterInput
          activeTab={activeTab}
          onFilterChange={onFilterChange}
        />
      )}
    </div>
  );
};

export default ManageMaterialTableHeader;
