"use client";

import React from "react";
import { DataTable } from "../../global/table";

import { useShallow } from "zustand/react/shallow";
import { getMaterialColumns } from "./column";
import { IMaterial } from "@/interfaces/admin/manage-material/list";
import ManageMaterialTableHeader from "./table-header";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useManageMaterialModal } from "@/store/admin/manage-material/modal";
import DeleteConfirmationModal from "./delete-material-confirmation-modal";
import EditMaterialModal from "./edit-modal";
import PreviewMaterialModal from "./preview-modal";

// Dummy data for the materials table
const dummyMaterials: IMaterial[] = [
  {
    id: "1",
    document_id: "DOC-001",
    category: "Learning Material",
    level: "Beginner",
    document_name: "Introduction to React",
    size: "2.5 MB",
    associated_sections: ["Frontend", "React"],
    uploaded_at: "2023-09-15T10:30:00Z",
    uploaded_by: "<PERSON>",
    updated_at: "2023-09-16T08:45:00Z",
    updated_by: "<PERSON>",
  },
  {
    id: "2",
    document_id: "DOC-002",
    category: "Assessment",
    level: "Intermediate",
    document_name: "React Hooks Guide",
    size: "1.8 MB",
    associated_sections: ["Frontend", "React", "Hooks"],
    uploaded_at: "2023-09-14T14:20:00Z",
    uploaded_by: "Alex Johnson",
    updated_at: "2023-09-14T14:20:00Z",
    updated_by: "Alex Johnson",
  },
  {
    id: "3",
    document_id: "DOC-003",
    category: "Tutorial",
    level: "Advanced",
    document_name: "State Management with Redux",
    size: "3.2 MB",
    associated_sections: ["Frontend", "React", "Redux"],
    uploaded_at: "2023-09-13T09:15:00Z",
    uploaded_by: "Sarah Williams",
    updated_at: "2023-09-15T16:30:00Z",
    updated_by: "Mike Brown",
  },
  {
    id: "4",
    document_id: "DOC-004",
    category: "Cheat Sheet",
    level: "Beginner",
    document_name: "JavaScript Basics",
    size: "1.1 MB",
    associated_sections: ["Frontend", "JavaScript"],
    uploaded_at: "2023-09-12T11:45:00Z",
    uploaded_by: "Emma Davis",
    updated_at: "2023-09-12T11:45:00Z",
    updated_by: "Emma Davis",
  },
  {
    id: "5",
    document_id: "DOC-005",
    category: "Guide",
    level: "Intermediate",
    document_name: "Responsive Design Patterns",
    size: "2.8 MB",
    associated_sections: ["Frontend", "CSS", "Responsive Design"],
    uploaded_at: "2023-09-11T13:20:00Z",
    uploaded_by: "David Wilson",
    updated_at: "2023-09-13T10:10:00Z",
    updated_by: "Lisa Chen",
  },
];

const DUMMY_PAGINATION = {
  current_page: 1,
  total_page: 1,
  total_data: 1,
  next: null,
  prev: null,
};

const ManageMaterialTable = () => {
  const activeTab = useManageMaterialTabStore((state) => state.activeTab);
  const {
    setOpenedMaterial,
    setOpenDeleteModal,
    openDeleteModal,
    openedMaterial,
  } = useManageMaterialModal(
    useShallow(
      ({
        setOpenedMaterial,
        setOpenDeleteModal,
        openDeleteModal,
        openedMaterial,
      }) => ({
        setOpenedMaterial,
        setOpenDeleteModal,
        openDeleteModal,
        openedMaterial,
      })
    )
  );

  const [openEditModal, setOpenEditModal] = React.useState(false);
  const [openPreviewModal, setOpenPreviewModal] = React.useState(false);

  const columns = React.useMemo(
    () =>
      getMaterialColumns({
        onEdit(material) {
          setOpenedMaterial(material);
          setOpenEditModal(true);
        },
        onDownload(material) {
          console.log("Downloading:", material);
        },
        onDelete(material) {
          setOpenedMaterial(material);
          setOpenDeleteModal(true);
        },
        onPreview(material) {
          setOpenedMaterial(material);
          setOpenPreviewModal(true);
        },
      }),
    []
  );

  //   const subCategoryColumns = React.useMemo(
  //     () =>
  //       getColumnsManageSubMaterial({
  //         onEdit: (data) => {
  //           setOpenedSubCategory(data as ISubCategory);
  //           setOpenAddModal(true);
  //         },
  //         onDelete: (data) => {
  //           setOpenedSubCategory(data as ISubCategory);
  //           setOpenDeleteModal(true);
  //         },
  //       }),
  //     [subCategory.data]
  //   );

  // Determine material type based on activeTab or material data
  const getMaterialType = (): "video" | "audio" | "document" => {
    if (activeTab === "video") return "video";
    if (activeTab === "audio") return "audio";
    return "document";
  };

  return (
    <div className="flex flex-col gap-4 h-full">
      <ManageMaterialTableHeader />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={openDeleteModal}
        onOpenChange={() => setOpenDeleteModal(false)}
        onConfirm={() => console.log("Deleting material...")}
        title="Hapus Material"
        description="Apakah Anda yakin ingin menghapus material ini?"
      />

      {/* Edit Material Modal */}
      <EditMaterialModal
        isOpen={openEditModal}
        onClose={() => {
          setOpenEditModal(false);
          setOpenedMaterial(null);
        }}
        material={openedMaterial}
        type={getMaterialType()}
      />

      {/* Preview Material Modal */}
      <PreviewMaterialModal
        isOpen={openPreviewModal}
        onClose={() => {
          setOpenPreviewModal(false);
          setOpenedMaterial(null);
        }}
        material={openedMaterial}
        type={getMaterialType()}
      />

      <DataTable
        key={activeTab}
        columns={columns}
        data={dummyMaterials}
        pagination={DUMMY_PAGINATION}
        onPageChange={(page) => console.log(page)}
      />
    </div>
  );
};

export default ManageMaterialTable;
