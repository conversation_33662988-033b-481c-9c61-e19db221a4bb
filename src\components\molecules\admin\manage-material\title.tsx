"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useShallow } from "zustand/react/shallow";

const TABS = [
  { value: "video", label: "Manage Video" },
  { value: "audio", label: "Manage Audio" },
  { value: "document", label: "Manage Document" },
  { value: "scorm", label: "Manage SCORM" },
];

const ManageMaterialTitle = () => {
  const { activeTab, setActiveTab } = useManageMaterialTabStore(
    useShallow(({ activeTab, setActiveTab }) => ({ activeTab, setActiveTab }))
  );

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Material
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={activeTab}
          onValueChange={(val) => {
            setActiveTab(val as "video" | "audio" | "document" | "scorm");
          }}
        >
          <BaseTabsList className="w-full h-12">
            {TABS.map((tab) => (
              <BaseTabsTrigger key={tab.value} value={tab.value}>
                {tab.label}
              </BaseTabsTrigger>
            ))}
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default ManageMaterialTitle;
