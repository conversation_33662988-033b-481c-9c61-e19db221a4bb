import { BaseButton } from "@/components/atoms/button";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";
import { Section } from "@/interfaces/admin/manage-section/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2 } from "lucide-react";

interface Props {
  onEdit: (section: Section) => void;
  onDelete: (section: Section) => void;
}

export const getSectionColumns = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<Section>[] => {
  return [
    {
      accessorKey: "sectionId",
      header: "Section ID",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">
            {row.original.sectionId}
          </div>
        );
      },
    },
    {
      accessorKey: "sectionName",
      header: "Section Name",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">
            {row.original.sectionName}
          </div>
        );
      },
    },
    {
      accessorKey: "sectionType",
      header: "Section Type",
      cell({ row }) {
        return <div className="font-medium">{row.original.sectionType}</div>;
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.duration}</div>
        );
      },
    },
    {
      accessorKey: "technicalCompetencies",
      header: "Technical Competencies",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.technicalCompetencies || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "softCompetencies",
      header: "Soft Competencies",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.softCompetencies || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "attachedSubModule",
      header: "Attached to Sub-Module",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.attachedSubModule}
            options={[
              { value: "Sub-Module 1", id: "1" },
              { value: "Sub-Module 2", id: "2" },
              { value: "Sub-Module 3", id: "3" },
            ]}
            onSelect={(option) => console.log(option)}
          />
        );
      },
    },
    {
      accessorKey: "lastUpdated",
      header: "Last Updated",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {dayjs(row.original.lastUpdated).format("DD/MM/YYYY")}
          </div>
        );
      },
    },
    {
      accessorKey: "updatedBy",
      header: "Updated By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">{row.original.updatedBy}</div>
        );
      },
    },
    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center gap-2">
            <BaseButton
              variant="ghost"
              size="sm"
              onClick={() => onEdit(row.original)}
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Pencil className="h-4 w-4 text-blue-600" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="sm"
              onClick={() => onDelete(row.original)}
              className="h-8 w-8 p-0 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
