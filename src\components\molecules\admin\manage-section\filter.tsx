"use client";

import FilterButton from "@/components/atoms/filter-button";
import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";

interface Props {
  filterOpen: boolean;
  setFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ManageSectionTableHeaderFilter = ({
  filterOpen,
  setFilterOpen,
}: Readonly<Props>) => {
  return (
    <div className="flex items-center justify-end gap-3">
      <FilterButton
        active={filterOpen}
        onClick={() => setFilterOpen((prev) => !prev)}
        className="h-12"
      />
      <BaseButton className="h-12 bg-orange-500 hover:bg-orange-600">
        <Plus size={16} className="mr-2" />
        Add New Section
      </BaseButton>
    </div>
  );
};

export default ManageSectionTableHeaderFilter;
