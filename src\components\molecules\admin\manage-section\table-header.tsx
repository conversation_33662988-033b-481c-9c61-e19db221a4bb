"use client";
import React, { useState } from "react";
import ManageSectionTableHeaderSearch from "./search";
import ManageSectionTableHeaderFilter from "./filter";
import ManageSectionFilterInput from "./filter-input";

const ManageSectionTableHeader = () => {
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ManageSectionTableHeaderSearch />
        <ManageSectionTableHeaderFilter
          filterOpen={filterOpen}
          setFilterOpen={setFilterOpen}
        />
      </div>
      {filterOpen && <ManageSectionFilterInput />}
    </div>
  );
};

export default ManageSectionTableHeader;
