"use client";

import React from "react";
import ManageSliderContentItem from "./item";
import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import ManageSliderNewModal from "./new-modal";
import { useManageSliderModal } from "@/store/admin/manage-slider/modal";
import { useShallow } from "zustand/react/shallow";
import { useGetSliderListQuery } from "@/services/query/manage-slider/list";
import ManageSliderDeleteConfirmationModal from "./delete-confirmation-modal";

const ManageSliderContent = () => {
  const sliders = useGetSliderListQuery();

  return (
    <div className="flex flex-col gap-5 mb-8">
      {sliders.data?.data?.map((item) => (
        <ManageSliderContentItem key={item.id} data={item} />
      ))}

      <AddButton disabled={(sliders.data?.data?.length ?? 0) >= 4} />
      <ManageSliderNewModal />
      <ManageSliderDeleteConfirmationModal />
    </div>
  );
};

const AddButton = ({ disabled }: { disabled?: boolean }) => {
  const { setOpenAddSlider } = useManageSliderModal(
    useShallow(({ setOpenAddSlider }) => ({
      setOpenAddSlider,
    }))
  );

  return (
    <BaseButton
      className="h-12 w-full text-sm"
      variant={"outline"}
      onClick={() => setOpenAddSlider(true)}
      disabled={disabled}
    >
      <Plus /> Add New Slider
    </BaseButton>
  );
};

export default ManageSliderContent;
