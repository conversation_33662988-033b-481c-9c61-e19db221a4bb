import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useDeleteSliderMutation } from "@/services/mutation/manage-slider/delete";
import { useGetSliderListQuery } from "@/services/query/manage-slider/list";
import { useManageSliderModal } from "@/store/admin/manage-slider/modal";
import { Trash2 } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const ManageSliderDeleteConfirmationModal = () => {
  const {
    currentData,
    openDeleteSliderConfirmation,
    setOpenDeleteSliderConfirmation,
  } = useManageSliderModal(
    useShallow(
      ({
        openDeleteSliderConfirmation,
        setOpenDeleteSliderConfirmation,
        currentData,
      }) => ({
        openDeleteSliderConfirmation,
        setOpenDeleteSliderConfirmation,
        currentData,
      })
    )
  );

  const sliders = useGetSliderListQuery();
  const deleteSlider = useDeleteSliderMutation();

  const handleDeleteUser = async () => {
    if (currentData) {
      deleteSlider.mutate(
        {
          id: currentData,
          slider_name:
            sliders.data?.data?.find((item) => item.id === currentData)
              ?.slider_name ?? "",
        },
        {
          onSuccess: (data) => {
            sliders.refetch();
            toast.success(data.message);
            setOpenDeleteSliderConfirmation(false);
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to delete user");
          },
        }
      );
    }
  };

  return (
    <BaseDialog
      open={openDeleteSliderConfirmation}
      onOpenChange={setOpenDeleteSliderConfirmation}
    >
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus Slider?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Slider yang dipilih akan dihapus secara permanen. Tindakan ini tidak
            bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={handleDeleteUser}
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ManageSliderDeleteConfirmationModal;
