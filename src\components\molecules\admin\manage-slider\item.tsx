import { IGetSliderListResponse } from "@/interfaces/admin/manage-slider/list";
import { useGetFileQuery } from "@/services/query/file/get";
import { useManageSliderModal } from "@/store/admin/manage-slider/modal";
import { bufferToFile } from "@/utils/common/file";
import { Pencil, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect } from "react";
import { useShallow } from "zustand/react/shallow";

const ManageSliderContentItem = ({
  data,
}: Readonly<{ data: IGetSliderListResponse }>) => {
  const { setOpenDeleteSliderConfirmation, setCurrentData, setOpenAddSlider } =
    useManageSliderModal(
      useShallow(
        ({
          setOpenDeleteSliderConfirmation,
          setCurrentData,
          setOpenAddSlider,
        }) => ({
          setOpenDeleteSliderConfirmation,
          setCurrentData,
          setOpenAddSlider,
        })
      )
    );

  const [imageUrl, setImageUrl] = React.useState<string | null>(null);

  const image = useGetFileQuery({
    path: data.slider_desktop ?? "",
  });

  useEffect(() => {
    if (image.data) {
      const file = bufferToFile(
        image.data,
        data.slider_desktop ?? "",
        "image/webp"
      );

      const url = URL.createObjectURL(file);

      setImageUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [image.data, data.slider_desktop]);

  return (
    <div className="bg-white w-full border border-gray-200 rounded-xl p-4 flex flex-col gap-2 shadow-2xs">
      <div className="grid grid-cols-10">
        <div className="col-span-2 flex flex-col text-xs">
          <span className="text-gray-400">Slider Name</span>
          <span>{data.slider_name}</span>
        </div>
        <div className="col-span-6 flex flex-col text-xs">
          <span className="text-gray-400">Link</span>
          <Link
            href={data.link ?? ""}
            target="_blank"
            className="whitespace-nowrap overflow-hidden text-ellipsis max-w-2xl underline cursor-pointer"
          >
            {data.link}
          </Link>
        </div>
        <div className="col-span-2 flex justify-end items-center gap-2">
          <button
            className="w-6 h-6 rounded-sm  border border-gray-300 flex items-center justify-center hover:bg-gray-100 cursor-pointer"
            onClick={() => {
              setCurrentData(data.id);
              setOpenAddSlider(true);
            }}
          >
            <Pencil size={13} className="text-gray-600" />
          </button>
          <button
            className="w-6 h-6 rounded-sm  border border-gray-300 flex items-center justify-center hover:bg-gray-100 cursor-pointer"
            onClick={() => {
              setCurrentData(data.id);
              setOpenDeleteSliderConfirmation(true);
            }}
          >
            <Trash2 size={13} className="text-red-500" />
          </button>
        </div>
      </div>
      {imageUrl && (
        <div className="relative rounded-xl w-full h-40 overflow-hidden">
          <Image
            alt="slider"
            src={imageUrl ?? ""}
            fill
            className="object-cover"
          />
        </div>
      )}
    </div>
  );
};

export default ManageSliderContentItem;
