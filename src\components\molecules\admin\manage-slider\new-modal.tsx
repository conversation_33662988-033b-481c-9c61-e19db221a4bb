import { BaseButton } from "@/components/atoms/button";
import {
  Base<PERSON>ialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";

import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";

import {
  ICreateSliderForm,
  createSliderFormSchema,
} from "@/interfaces/admin/manage-slider/new";
import { useCreateSliderMutation } from "@/services/mutation/manage-slider/new";
import { useUpdateSliderMutation } from "@/services/mutation/manage-slider/update";
import { useGetFileQuery } from "@/services/query/file/get";
import { useGetSliderDetailQuery } from "@/services/query/manage-slider/detail";
import { useGetSliderListQuery } from "@/services/query/manage-slider/list";
import { useManageSliderModal } from "@/store/admin/manage-slider/modal";
import { bufferToFile } from "@/utils/common/file";
import { yupResolver } from "@hookform/resolvers/yup";
import { RotateCcw, Upload } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useForm, useFormContext, Path, FormProvider } from "react-hook-form";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const defaultValue = {
  slider_name: "",
  slider_desktop: undefined,
  slider_mobile: undefined,
  link: "",
};

const ManageSliderNewModal = () => {
  const { openAddSlider, setOpenAddSlider, currentData, setCurrentData } =
    useManageSliderModal(
      useShallow(
        ({ openAddSlider, setOpenAddSlider, currentData, setCurrentData }) => ({
          openAddSlider,
          setOpenAddSlider,
          currentData,
          setCurrentData,
        })
      )
    );

  const createSlider = useCreateSliderMutation();
  const updateSlider = useUpdateSliderMutation();
  const sliders = useGetSliderListQuery();
  const slider = useGetSliderDetailQuery({ id: currentData });

  const desktopImage = useGetFileQuery({
    path: slider.data?.data?.slider_desktop ?? "",
  });
  const mobileImage = useGetFileQuery({
    path: slider.data?.data?.slider_mobile ?? "",
  });

  useEffect(() => {
    if (slider.data) {
      form.reset({
        slider_name: slider.data.data.slider_name ?? "",
        link: slider.data.data.link ?? "",
      });
    }

    if (desktopImage.data) {
      const file = bufferToFile(
        desktopImage.data,
        slider.data?.data?.slider_desktop ?? "",
        "image/webp"
      );
      form.setValue("slider_desktop", file, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    }
    if (mobileImage.data) {
      const file = bufferToFile(
        mobileImage.data,
        slider.data?.data?.slider_desktop ?? "",
        "image/webp"
      );
      form.setValue("slider_mobile", file, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    }
  }, [slider.data, desktopImage.data, mobileImage.data]);

  const form = useForm({
    resolver: yupResolver(createSliderFormSchema),
    defaultValues: defaultValue,
    mode: "all",
  });

  const handleClose = (value: boolean) => {
    setOpenAddSlider(value);
    if (!value) {
      form.reset(defaultValue);
      setCurrentData(null);
    }
  };

  const handleSubmit = (data: ICreateSliderForm) => {
    if (currentData) {
      updateSlider.mutate(
        { id: currentData, body: data },
        {
          onSuccess: (data) => {
            toast.success(data?.message ?? "Slider updated successfully");
            sliders.refetch();
            setOpenAddSlider(false);
            form.reset(defaultValue);
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to update slider");
          },
        }
      );
    } else {
      createSlider.mutate(data, {
        onSuccess: (data) => {
          toast.success(data?.message ?? "Slider created successfully");
          sliders.refetch();
          setOpenAddSlider(false);
          form.reset(defaultValue);
        },
        onError: (data) => {
          toast.error(data?.message ?? "Failed to create slider");
        },
      });
    }
  };

  return (
    <BaseDialog open={openAddSlider} onOpenChange={handleClose}>
      <BaseDialogContent>
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>{currentData ? "Edit Slider" : "Add New Slider"}</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>
        <FormProvider {...form}>
          <form
            className="flex flex-col gap-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <InputString
              label="Slider's Name"
              id="slider_name"
              placeholder="Type Slider"
              required
            />
            <InputFile id="slider_desktop" label="Desktop" required />
            <InputFile id="slider_mobile" label="Mobile" required isMobile />
            <InputString
              label="Section Link"
              id="link"
              placeholder="Type Link"
              required
            />

            <BaseSeparator className="mt-1 -mb-2" />
            <div className="flex justify-end gap-3 -mb-3">
              <DialogClose asChild>
                <BaseButton className="h-11 w-32" variant={"outline"}>
                  Cancel
                </BaseButton>
              </DialogClose>
              <BaseButton
                className="h-11 w-32"
                type="submit"
                disabled={!form.formState.isValid}
              >
                {currentData ? "Update Slider" : "Add Slider"}
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </BaseDialogContent>
    </BaseDialog>
  );
};

const InputString = <T extends ICreateSliderForm>({
  label,
  id,
  placeholder,
  optional = false,
  required = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
  required?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
        {required && <span className="text-xs text-red-500 -ml-1">*</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11"
      />
    </div>
  );
};

const InputFile = ({
  label,
  id,
  placeholder,
  required = false,
  isMobile = false,
}: {
  label: string;
  id: keyof ICreateSliderForm;
  placeholder?: string;
  required?: boolean;
  isMobile?: boolean;
}) => {
  const form = useFormContext<ICreateSliderForm>();

  const image = form.watch(id as Path<ICreateSliderForm>); // expect File | undefined
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (image instanceof File) {
      const url = URL.createObjectURL(image);
      setPreviewUrl((prev) => {
        if (prev?.startsWith("blob:")) URL.revokeObjectURL(prev);
        return url;
      });
    } else {
      setPreviewUrl((prev) => {
        if (prev?.startsWith("blob:")) URL.revokeObjectURL(prev);
        return null;
      });
    }
  }, [image]);

  // ambil hanya name & ref dari register (hindari onChange bawaan RHF)
  const reg = form.register(id as Path<ICreateSliderForm>);

  return (
    <div className="flex flex-col">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {required && <span className="text-xs text-red-500 -ml-1">*</span>}
      </BaseLabel>

      <BaseLabel
        className="w-full py-1 rounded-md border border-gray-300 flex between items-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-sm p-3"
        htmlFor={id as string}
      >
        <div className="flex flex-col gap-0.5 text-xs text-gray-500 w-3/4">
          {previewUrl && image instanceof File ? (
            <div className="flex items-center gap-2 mt-2 text-gray-600 text-xs">
              <div className="relative w-40 h-24 overflow-hidden rounded-md border">
                <img
                  src={previewUrl}
                  alt="preview"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          ) : (
            <>
              <span className="font-light">
                File allowed{" "}
                <span className="font-semibold">.jpg/.jpeg/.png</span> dengan
                ukuran maks. <span className="font-semibold">10 MB</span>
              </span>
              <span className="text-gray-400 font-light">
                {isMobile ? "343 x 114" : "1128 x 150"} px
              </span>
            </>
          )}
        </div>

        {image instanceof File ? (
          <div className="flex items-center justify-end w-1/4 gap-1 text-gray-600">
            <RotateCcw size={12} className="text-gray-700" />
            Reupload
          </div>
        ) : (
          <div className="flex items-center justify-end w-1/4 gap-1 text-gray-600">
            <Upload size={12} className="text-gray-700" />
            Upload
          </div>
        )}
      </BaseLabel>

      {form.formState.errors[id as Path<ICreateSliderForm>] && (
        <span className="text-red-500 text-xs mt-1.5">
          File tidak dapat diunggah. Batas maksimal ukuran file adalah 10MB.
        </span>
      )}

      <BaseInput
        id={id as string}
        name={reg.name}
        ref={reg.ref}
        placeholder={placeholder}
        type="file"
        accept="image/png,image/jpeg,image/jpg,image/webp"
        className="sr-only"
        onChange={(e) => {
          const file = e.currentTarget.files?.[0] ?? undefined;
          form.setValue(id as Path<ICreateSliderForm>, file as any, {
            shouldDirty: true,
            shouldValidate: true,
          });
          // agar memilih file yang sama dua kali tetap memicu onChange
          e.currentTarget.value = "";
        }}
      />
    </div>
  );
};

export default ManageSliderNewModal;
