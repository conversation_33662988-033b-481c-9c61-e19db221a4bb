"use client";

import { BaseLabel } from "@/components/atoms/label";
import React from "react";
import { CloudUpload, Upload } from "lucide-react";

interface FileUploaderProps {
  title: string;
  acceptedTypes: string;
  supportedText: string;
  banner?: React.ReactNode;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const FileUploader = ({
  title,
  acceptedTypes,
  supportedText,
  banner,
  onFileUpload,
}: FileUploaderProps) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">{title}</BaseLabel>

      {banner}

      <button
        onClick={handleBrowseClick}
        className="block w-full outline-none border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
      >
        <div className="flex items-center justify-center gap-3">
          <CloudUpload className="text-[#C0C0C0]" size={44} />

          <div className="space-y-2">
            <p className="text-gray-600 text-sm">
              <span className="font-medium">Drag & drop</span> your files or{" "}
              <button
                type="button"
                onClick={handleBrowseClick}
                className="text-orange-500 hover:text-orange-600 font-medium underline"
              >
                browse
              </button>
            </p>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes}
          multiple
          onChange={onFileUpload}
          className="hidden"
        />
      </button>
      <p className="text-sm text-gray-500">{supportedText}</p>
    </div>
  );
};

export default FileUploader;
