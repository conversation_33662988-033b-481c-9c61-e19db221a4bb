import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";

export const InputString = ({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
  onChange,
}: {
  label: string;
  id: string;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
  onChange?: (value: string) => void;
}) => {
  return (
    <div className="flex flex-col gap-1 w-full">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        className="h-11 disabled:bg-gray-100"
        onChange={(e) => onChange?.(e.currentTarget.value)}
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};
