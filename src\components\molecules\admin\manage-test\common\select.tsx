import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";

export const InputSelect = ({
  label,
  placeholder,
  options,
  value,
  onChange,
}: {
  label?: string;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
  onChange?: (value: string) => void;
}) => {
  return (
    <div className="flex flex-col gap-1 w-full">
      {label ? <BaseLabel className="text-sm">{label}</BaseLabel> : null}
      <BaseSelect
        value={value}
        onValueChange={(val) => {
          if (!val) return;
          onChange?.(val);
        }}
      >
        <BaseSelectTrigger className="w-full min-h-11 bg-white">
          <BaseSelectValue placeholder={placeholder} />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {options.map((option) => (
            <BaseSelectItem key={option.value} value={option.value}>
              {option.label}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};
