"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { usePathname, useRouter } from "next/navigation";

const ManageTestTitle = () => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Manage Test
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={pathname}
          onValueChange={(val) => router.push(val)}
        >
          <BaseTabsList className="w-full h-12">
            <BaseTabsTrigger value="/admin/manage-test">
              Question Bank
            </BaseTabsTrigger>
            <BaseTabsTrigger value="/admin/manage-test/question-template">
              Question Template
            </BaseTabsTrigger>
            <BaseTabsTrigger value="/admin/manage-test/image-repository">
              Image Repository
            </BaseTabsTrigger>
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default ManageTestTitle;
