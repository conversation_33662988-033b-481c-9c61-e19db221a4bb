import { BaseButton } from "@/components/atoms/button";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Download, Pencil, Trash2 } from "lucide-react";
import PillDropdown from "../common/dropdown";
import { IImageRepository } from "@/interfaces/admin/manage-test/image-repository/list";

interface Props {
  onEdit: (id: IImageRepository) => void;
  onDelete: (id: IImageRepository) => void;
  onDownload: (id: IImageRepository) => void;
}

export const getColumnsImageRepository = ({
  onEdit,
  onDelete,
  onDownload,
}: Props): ColumnDef<IImageRepository>[] => {
  const categories = ["Learning Material", "Assessment", "Tutorial", "Guide"];
  const levels = ["Beginner", "Intermediate", "Advanced"];

  return [
    {
      accessorKey: "id",
      header: "Image ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.category}
            options={categories.map((category) => ({
              value: category,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.level}
            options={levels.map((level) => ({
              value: level,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "image_name",
      header: "Image Name",
      cell({ row }) {
        return (
          <div className="w-[160px] text-wrap line-clamp-2">
            {row.original.image_name}
          </div>
        );
      },
    },
    { accessorKey: "format", header: "Format" },
    { accessorKey: "size", header: "Size" },
    {
      accessorKey: "associated_question",
      header: "Associated Question",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.associated_question}
            options={levels.map((level) => ({
              value: level,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "uploaded_at",
      header: "Uploaded At",
      cell(props) {
        const createdAt = props.row.original.uploaded_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "uploaded_by", header: "Uploaded By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start">
            <BaseButton
              variant={"ghost"}
              className="border-none"
              onClick={() => onDownload(row.original)}
            >
              <Download size={20} />
            </BaseButton>
            <BaseButton
              variant={"ghost"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={20}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"ghost"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={20} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
