"use client";

import React from "react";
import { BaseButton } from "@/components/atoms/button";
import { BaseSeparator } from "@/components/atoms/separator";
import MultipleSelectorComponent from "@/components/atoms/multiple-selector";
import { Option } from "@/components/atoms/multiselect";
import { InputSelect } from "../common/select";

const DUMMY_CATEGORIES: Option[] = [
  { value: "1", label: "DI Yogyakarta" },
  { value: "2", label: "Bali" },
  { value: "3", label: "Kepulauan Bangka Belitung" },
  { value: "4", label: "Jawa Barat" },
  { value: "5", label: "Jawa Tengah" },
  { value: "6", label: "Jawa Timur" },
];

const DUMMY_LEVEL: Option[] = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
];

const QUESTION_TYPE_OPTION: Option[] = [
  { value: "pilihan_ganda", label: "Benar Salah" },
  { value: "benar_salah", label: "Pilihan Ganda" },
  { value: "isian", label: "Isian" },
];

const ImageRepositoryTableHeaderFilter = () => {
  return (
    <div className="bg-white rounded-[8px]">
      <div className="flex items-center justify-between p-4">
        <span className="font-semibold text-comp-content-primary">Filter</span>

        <div className="flex items-center gap-2">
          <BaseButton
            className="px-5 w-[95px] text-destructive border border-destructive hover:text-destructive hover:border-destructive h-[40px] text-xs font-medium"
            variant="outline"
          >
            Reset
          </BaseButton>

          <BaseButton className="px-5 w-[95px] h-[40px] text-xs font-medium">
            Apply
          </BaseButton>
        </div>
      </div>

      <BaseSeparator />

      <div className="p-4 flex items-center gap-3">
        <MultipleSelectorComponent
          onSelectAll={() => {}}
          // value={}
          options={DUMMY_CATEGORIES}
          title="Category"
          placeholder="Select Category"
        />

        <InputSelect
          label="Level"
          placeholder="Select Level"
          value=""
          options={DUMMY_LEVEL}
        />
      </div>
    </div>
  );
};

export default ImageRepositoryTableHeaderFilter;
