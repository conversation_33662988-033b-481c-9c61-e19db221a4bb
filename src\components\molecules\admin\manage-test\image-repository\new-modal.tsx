"use client";

import { BaseButton } from "@/components/atoms/button";
import {
  Base<PERSON>ialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import MultipleSelectorComponent from "@/components/atoms/multiple-selector";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { DialogClose } from "@/components/ui/dialog";
import { IImageRepository } from "@/interfaces/admin/manage-test/image-repository/list";
import {
  useInsertCategoryMutation,
  useUpdateCategoryMutation,
} from "@/services/mutation/admin/manage-category";
import { useImageRepositoryModal } from "@/store/admin/manage-test/image-repository/modal";
import { yupResolver } from "@hookform/resolvers/yup";
import React from "react";
import { useForm, useFormContext, Path, FormProvider } from "react-hook-form";
import { useShallow } from "zustand/react/shallow";
import { Option } from "@/components/atoms/multiselect";
import {
  createImageRepositoryBodySchema,
  ICreateImageRepositoryBody,
} from "@/interfaces/admin/manage-test/image-repository/form";
import FileUploader from "../common/file-uploader";
import { Info } from "lucide-react";

const ImageRepositoryNewModal = () => {
  const {
    openedImageRepository,
    openAddModal,
    setOpenAddModal,
    setOpenedImageRepository,
  } = useImageRepositoryModal(
    useShallow(
      ({
        openedImageRepository,
        openAddModal,
        setOpenAddModal,
        setOpenedImageRepository,
      }) => ({
        openedImageRepository,
        openAddModal,
        setOpenAddModal,
        setOpenedImageRepository,
      })
    )
  );

  const handleOpenChangeModal = (state: boolean) => {
    if (!state) {
      setOpenedImageRepository(null);
    }

    setOpenAddModal(state);
  };

  return (
    <BaseDialog open={openAddModal} onOpenChange={handleOpenChangeModal}>
      <BaseDialogContent className="sm:max-w-[800px]">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>Add New Image</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>

        <span className="font-medium">Image Information</span>

        <NewImageRepositoryForm
          isOpen={openAddModal}
          data={openedImageRepository}
          onCloseModal={() => handleOpenChangeModal(false)}
        />
      </BaseDialogContent>
    </BaseDialog>
  );
};

interface IFormProps {
  isOpen: boolean;
  data: IImageRepository | null;
  onCloseModal: VoidFunction;
}

const DUMMY_CATEGORIES: Option[] = [
  { value: "1", label: "DI Yogyakarta" },
  { value: "2", label: "Bali" },
  { value: "3", label: "Kepulauan Bangka Belitung" },
  { value: "4", label: "Jawa Barat" },
  { value: "5", label: "Jawa Tengah" },
  { value: "6", label: "Jawa Timur" },
];

const DUMMY_LEVEL: Option[] = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
];

const NewImageRepositoryForm = ({ data, isOpen, onCloseModal }: IFormProps) => {
  const form = useForm({
    resolver: yupResolver(createImageRepositoryBodySchema),
  });

  const insertCategory = useInsertCategoryMutation();
  const updateCategory = useUpdateCategoryMutation();

  React.useEffect(() => {
    if (isOpen && data) {
      form.reset({
        id: data.id.toString(),
        name: data.image_name ?? "",
      });
    }

    if (!isOpen)
      form.reset({
        id: undefined,
        name: "",
      });
  }, [isOpen, data]);

  const onSubmit = (body: ICreateImageRepositoryBody) => {
    if (!data) {
      // insertCategory.mutate(
      //   {
      //     category_name: body.name,
      //   },
      //   {
      //     onSuccess: () => {
      //       onCloseModal();
      //       toast.success("Category added successfully");
      //     },
      //     onError: () => {
      //       toast.error("Failed to add Category");
      //     },
      //   }
      // );
      return;
    }

    // updateCategory.mutate(
    //   {
    //     id: data.id,
    //     body: {
    //       category_name: body.name,
    //     },
    //   },
    //   {
    //     onSuccess: () => {
    //       onCloseModal();
    //       toast.success("Category updated successfully");
    //     },
    //     onError: () => {
    //       toast.error("Failed to update Category");
    //     },
    //   }
    // );
  };

  return (
    <FormProvider {...form}>
      <form
        className="flex flex-col gap-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <InputString<ICreateImageRepositoryBody>
          label="Image Name"
          id="name"
          placeholder="Input image name"
        />

        <MultipleSelectorComponent
          onSelectAll={() =>
            form.setValue("category", DUMMY_CATEGORIES, {
              shouldValidate: true,
            })
          }
          value={form.watch("category") as Option[]}
          options={DUMMY_CATEGORIES}
          title="Category"
          placeholder="Select Category"
          selectAllTitle="Select All Category"
        />

        <InputSelect<ICreateImageRepositoryBody>
          label="Level"
          id="level"
          placeholder="Select level"
          value={form.watch("level")}
          options={DUMMY_LEVEL}
        />

        {/* File Upload */}
        <FileUploader
          title="Upload Image"
          supportedText="Supported file types: PNG, JPG, JPEG, SVG max. size 2 MB."
          acceptedTypes="image/*"
          onFileUpload={(img) =>
            form.setValue("image", img, { shouldValidate: true })
          }
          banner={
            <div className="border-[#F8A644] border bg-[#FEF4E9] h-[52px] px-4 rounded-[8px] w-full flex items-center gap-2">
              <Info size={20} fill="#F8A644" stroke="white" />
              <span className="text-sm text-[#767676]">
                Please adjust your image aspect ratio to 4:3 with minimum
                resolutions in 640 x 480 pixel for better quality.
              </span>
            </div>
          }
        />

        <BaseSeparator className="mt-4 -mb-2" />
        <div className="flex justify-end gap-3 -mb-3">
          <DialogClose asChild>
            <BaseButton className="h-11 w-32" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="h-11 w-32"
            type="submit"
            disabled={insertCategory.isPending || updateCategory.isPending}
          >
            {data ? "Save Changes" : "Add Image"}
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const InputString = <T extends ICreateImageRepositoryBody>({
  label,
  id,
  placeholder,
  optional = false,
  readonly = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  optional?: boolean;
  readonly?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        {...form.register(id as Path<T>)}
        className="h-11 disabled:bg-gray-100"
        readOnly={readonly}
        disabled={readonly}
      />
    </div>
  );
};

const InputSelect = <T extends ICreateImageRepositoryBody>({
  label,
  id,
  placeholder,
  options,
  value,
  optional = false,
}: {
  label: string;
  id: keyof T;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<T>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id as string}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseSelect
        {...form.register(id as Path<T>)}
        value={value}
        onValueChange={(val) => {
          if (!val) return;
          form.setValue(id as Path<T>, val as any, {
            shouldValidate: true,
          });
        }}
      >
        <BaseSelectTrigger className="w-full min-h-11" id={id as string}>
          <BaseSelectValue placeholder={placeholder} />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {options.map((option) => (
            <BaseSelectItem key={option.value} value={option.value}>
              {option.label}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};

export default ImageRepositoryNewModal;
