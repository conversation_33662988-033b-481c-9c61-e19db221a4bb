"use client";

import React from "react";
import ImageRepositoryTableHeaderSearch from "./search";
import ImageRepositoryTableHeaderAction from "./action";
import ImageRepositoryTableHeaderFilter from "./filter";

const ImageRepositoryTableHeader = () => {
  const [openFilter, setOpenFilter] = React.useState<boolean>(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ImageRepositoryTableHeaderSearch />
        <ImageRepositoryTableHeaderAction onOpenFilter={setOpenFilter} />
      </div>

      {openFilter ? <ImageRepositoryTableHeaderFilter /> : null}
    </div>
  );
};

export default ImageRepositoryTableHeader;
