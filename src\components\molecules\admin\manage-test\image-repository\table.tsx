"use client";

import React from "react";
import { DataTable } from "../../../global/table";
import { getColumnsImageRepository } from "./column";
import { useShallow } from "zustand/react/shallow";
import dayjs from "dayjs";
import { IImageRepository } from "@/interfaces/admin/manage-test/image-repository/list";
import { useImageRepositoryModal } from "@/store/admin/manage-test/image-repository/modal";

const dummyData: IImageRepository[] = [
  {
    id: 1,
    category: "2 Category",
    level: "4 Levels",
    image_name: "Image Question 1",
    format: "PNG",
    size: "2 MB",
    associated_question: "4 Sections",
    uploaded_at: dayjs().toISOString(),
    uploaded_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    id: 2,
    category: "2 Category",
    level: "4 Levels",
    image_name: "Image Question 1",
    format: "PNG",
    size: "2 MB",
    associated_question: "4 Sections",
    uploaded_at: dayjs().toISOString(),
    uploaded_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    id: 3,
    category: "2 Category",
    level: "4 Levels",
    image_name: "Image Question 1",
    format: "PNG",
    size: "2 MB",
    associated_question: "4 Sections",
    uploaded_at: dayjs().toISOString(),
    uploaded_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
];

const ImageRespositoryTable = () => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedImageRepository } =
    useImageRepositoryModal(
      useShallow(
        ({
          setOpenAddModal,
          setOpenedImageRepository,
          setOpenDeleteModal,
        }) => ({
          setOpenAddModal,
          setOpenedImageRepository,
          setOpenDeleteModal,
        })
      )
    );
  // const { setCategoryQuery } = useManageCategoryQueryStore(
  //   useShallow(
  //     ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     }) => ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     })
  //   )
  // );

  const columns = React.useMemo(
    () =>
      getColumnsImageRepository({
        onDownload: () => {},
        onEdit: (data) => {
          setOpenedImageRepository(data);
          setOpenAddModal(true);
        },
        onDelete: (data) => {
          setOpenedImageRepository(data);
          setOpenDeleteModal(true);
        },
      }),
    []
  );

  return (
    <DataTable
      columns={columns}
      data={dummyData}
      // pagination={category.data?.pagination}
      // onPageChange={(page) => setCategoryQuery({ page })}
    />
  );
};

export default ImageRespositoryTable;
