import { BaseButton } from "@/components/atoms/button";
import { IQuestionBank } from "@/interfaces/admin/manage-test/question-bank/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { CircleCheck, Pencil, Trash2 } from "lucide-react";
import PillDropdown from "../common/dropdown";
import { Checkbox } from "@/components/ui/checkbox";

interface Props {
  onEdit: (id: IQuestionBank) => void;
  onDelete: (id: IQuestionBank) => void;
  isSelectable?: boolean;
  onSelect?: (id: IQuestionBank[]) => void;
}

export const getColumnsQuestionBank = ({
  onEdit,
  onDelete,
  onSelect,
  isSelectable,
}: Props): ColumnDef<IQuestionBank>[] => {
  const categories = ["Learning Material", "Assessment", "Tutorial", "Guide"];
  const levels = ["Beginner", "Intermediate", "Advanced"];

  // selectable used for add question bank in question template feature
  const selectableCheckbox: ColumnDef<IQuestionBank>[] = isSelectable
    ? [
        {
          id: "select",
          header: ({ table }) => (
            <div className="flex items-center justify-center">
              <Checkbox
                checked={
                  table.getIsAllPageRowsSelected() ||
                  (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(checked) => {
                  if (checked) {
                    table.getSelectedRowModel().flatRows;
                    const allValues = table
                      .getRowModel()
                      .rows.map((it) => it.original);
                    onSelect?.(allValues);
                  } else {
                    onSelect?.([]);
                  }

                  table.toggleAllPageRowsSelected(!!checked);
                }}
                aria-label="Select all"
              />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex items-center justify-center">
              <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => {
                  onSelect?.([row.original]);
                  row.toggleSelected(!!value);
                }}
                aria-label="Select row"
              />
            </div>
          ),

          enableSorting: false,
          enableHiding: false,
        },
      ]
    : [];

  const actionButton: ColumnDef<IQuestionBank>[] = !isSelectable
    ? [
        {
          accessorKey: "id",
          header: "Action",
          cell({ row }) {
            return (
              <div className="flex items-center justify-start">
                <BaseButton
                  variant={"ghost"}
                  className="border-none"
                  onClick={() => onEdit(row.original)}
                >
                  <Pencil
                    size={20}
                    className="fill-gray-700 text-white"
                    strokeWidth={1}
                  />
                </BaseButton>
                <BaseButton
                  variant={"ghost"}
                  className="border-none"
                  onClick={() => onDelete(row.original)}
                >
                  <Trash2
                    size={20}
                    className="text-gray-700"
                    strokeWidth={2.5}
                  />
                </BaseButton>
              </div>
            );
          },
        },
      ]
    : [];

  return [
    ...selectableCheckbox,
    {
      accessorKey: "id",
      header: "Question ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.questionId}
          </span>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.category}
            options={categories.map((category) => ({
              value: category,
              id: row.original.questionId.toString(),
            }))}
            id={row.original.questionId.toString()}
          />
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.level}
            options={levels.map((level) => ({
              value: level,
              id: row.original.questionId.toString(),
            }))}
            id={row.original.questionId.toString()}
          />
        );
      },
    },
    { accessorKey: "question_type", header: "Question Type" },
    {
      accessorKey: "question",
      header: "Question",
      cell({ row }) {
        return (
          <div className="w-[320px] text-wrap line-clamp-2">
            {row.original.question}
          </div>
        );
      },
    },
    { accessorKey: "option_a", header: "Option A" },
    { accessorKey: "option_b", header: "Option B" },
    { accessorKey: "option_c", header: "Option C" },
    { accessorKey: "option_d", header: "Option D" },
    { accessorKey: "key_answer", header: "Key Answer" },
    {
      accessorKey: "with_image",
      header: "With Image?",
      cell(props) {
        return <CircleCheck fill="#F7941E" stroke="white" />;
      },
    },
    {
      accessorKey: "associated_section",
      header: "Associated Section",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.associated_section}
            options={levels.map((level) => ({
              value: level,
              id: row.original.questionId.toString(),
            }))}
            id={row.original.questionId.toString()}
          />
        );
      },
    },
    {
      accessorKey: "correct_answer_percentage",
      header: "Correct Answer Percentage",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    ...actionButton,
  ];
};
