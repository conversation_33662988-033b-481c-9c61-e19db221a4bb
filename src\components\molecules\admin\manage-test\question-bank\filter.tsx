"use client";

import React from "react";
import { BaseButton } from "@/components/atoms/button";
import { BaseSeparator } from "@/components/atoms/separator";
import MultipleSelectorComponent from "@/components/atoms/multiple-selector";
import { Option } from "@/components/atoms/multiselect";
import { InputSelect } from "../common/select";
import { cn } from "@/lib/utils";

const DUMMY_CATEGORIES: Option[] = [
  { value: "1", label: "DI Yogyakarta" },
  { value: "2", label: "Bali" },
  { value: "3", label: "Kepulauan Bangka Belitung" },
  { value: "4", label: "Jawa Barat" },
  { value: "5", label: "Jawa Tengah" },
  { value: "6", label: "Jawa Timur" },
];

const DUMMY_LEVEL: Option[] = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
];

const QUESTION_TYPE_OPTION: Option[] = [
  { value: "pilihan_ganda", label: "Benar Salah" },
  { value: "benar_salah", label: "Pilihan Ganda" },
  { value: "isian", label: "Isian" },
];

const WITH_IMAGE_OPTION: Option[] = [
  { value: "true", label: "Ya" },
  { value: "false", label: "Tidak" },
];

interface Props {
  className?: string;
}

const QuestionBankTableHeaderFilter = ({ className }: Readonly<Props>) => {
  return (
    <div className={cn("bg-white rounded-[8px]", className)}>
      <div className="flex items-center justify-between p-4">
        <span className="font-semibold text-comp-content-primary">Filter</span>

        <div className="flex items-center gap-2">
          <BaseButton
            className="px-5 w-[95px] text-destructive border border-destructive hover:text-destructive hover:border-destructive h-[40px] text-xs font-medium"
            variant="outline"
          >
            Reset
          </BaseButton>

          <BaseButton className="px-5 w-[95px] h-[40px] text-xs font-medium">
            Apply
          </BaseButton>
        </div>
      </div>

      <BaseSeparator />

      <div className="p-4 space-y-4">
        <MultipleSelectorComponent
          onSelectAll={() => {}}
          // value={}
          options={DUMMY_CATEGORIES}
          title="Category"
          placeholder="Select Category"
        />

        <div className="flex items-center gap-3">
          <InputSelect
            label="Level"
            placeholder="Select Level"
            value=""
            options={DUMMY_LEVEL}
          />

          <InputSelect
            label="Question Type"
            placeholder="Select Question Type"
            value=""
            options={QUESTION_TYPE_OPTION}
          />

          <InputSelect
            label="With Image?"
            placeholder="Select option"
            value=""
            options={WITH_IMAGE_OPTION}
          />
        </div>
      </div>
    </div>
  );
};

export default QuestionBankTableHeaderFilter;
