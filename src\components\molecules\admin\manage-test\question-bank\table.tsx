"use client";

import React from "react";
import { DataTable } from "../../../global/table";
import { getColumnsQuestionBank } from "./column";
import { useShallow } from "zustand/react/shallow";
import { IQuestionBank } from "@/interfaces/admin/manage-test/question-bank/list";
import dayjs from "dayjs";
import { useQuestionBankModal } from "@/store/admin/manage-test/question-bank/modal";
import { useQuestionBankTableListStore } from "@/store/admin/manage-test/question-bank/list";

const dummyData: IQuestionBank[] = [
  {
    questionId: 1,
    category: "2 Category",
    level: "4 Levels",
    question_type: "<PERSON>lihan Ganda",
    question:
      "Surveyor Ari bertanya ke satpam perumahan bahwa tidak mengenal customer. Informasi...",
    option_a: "Informasi Negatif",
    option_b: "Informasi Positif",
    option_c: "Informasi Tambahan",
    option_d: "Informasi <PERSON>",
    key_answer: "A",
    with_image: true,
    associated_section: "4 Sections",
    correct_answer_percentage: "80%",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    questionId: 2,
    category: "2 Category",
    level: "4 Levels",
    question_type: "Pilihan Ganda",
    question:
      "Surveyor Ari bertanya ke satpam perumahan bahwa tidak mengenal customer. Informasi...",
    option_a: "Informasi Negatif",
    option_b: "Informasi Positif",
    option_c: "Informasi Tambahan",
    option_d: "Informasi Pihak Ketiga",
    key_answer: "A",
    with_image: true,
    associated_section: "4 Sections",
    correct_answer_percentage: "80%",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    questionId: 3,
    category: "2 Category",
    level: "4 Levels",
    question_type: "Pilihan Ganda",
    question:
      "Surveyor Ari bertanya ke satpam perumahan bahwa tidak mengenal customer. Informasi...",
    option_a: "Informasi Negatif",
    option_b: "Informasi Positif",
    option_c: "Informasi Tambahan",
    option_d: "Informasi Pihak Ketiga",
    key_answer: "A",
    with_image: true,
    associated_section: "4 Sections",
    correct_answer_percentage: "80%",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    questionId: 4,
    category: "2 Category",
    level: "4 Levels",
    question_type: "Pilihan Ganda",
    question:
      "Surveyor Ari bertanya ke satpam perumahan bahwa tidak mengenal customer. Informasi...",
    option_a: "Informasi Negatif",
    option_b: "Informasi Positif",
    option_c: "Informasi Tambahan",
    option_d: "Informasi Pihak Ketiga",
    key_answer: "A",
    with_image: true,
    associated_section: "4 Sections",
    correct_answer_percentage: "80%",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    questionId: 5,
    category: "2 Category",
    level: "4 Levels",
    question_type: "Pilihan Ganda",
    question:
      "Surveyor Ari bertanya ke satpam perumahan bahwa tidak mengenal customer. Informasi...",
    option_a: "Informasi Negatif",
    option_b: "Informasi Positif",
    option_c: "Informasi Tambahan",
    option_d: "Informasi Pihak Ketiga",
    key_answer: "A",
    with_image: true,
    associated_section: "4 Sections",
    correct_answer_percentage: "80%",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
];

interface Props {
  isSelectable?: boolean;
}

const QuestionBankTable = ({ isSelectable }: Readonly<Props>) => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedQuestionBank } =
    useQuestionBankModal(
      useShallow(
        ({ setOpenAddModal, setOpenedQuestionBank, setOpenDeleteModal }) => ({
          setOpenAddModal,
          setOpenedQuestionBank,
          setOpenDeleteModal,
        })
      )
    );
  const { setSelectedQuestionBanks } = useQuestionBankTableListStore(
    useShallow(({ setSelectedQuestionBanks }) => ({
      setSelectedQuestionBanks,
    }))
  );

  // const { setCategoryQuery } = useManageCategoryQueryStore(
  //   useShallow(
  //     ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     }) => ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     })
  //   )
  // );

  const columns = React.useMemo(
    () =>
      getColumnsQuestionBank({
        isSelectable,
        onSelect: (question) => setSelectedQuestionBanks(question),
        onEdit: (data) => {
          setOpenedQuestionBank(data);
          setOpenAddModal(true);
        },
        onDelete: (data) => {
          setOpenedQuestionBank(data);
          setOpenDeleteModal(true);
        },
      }),
    []
  );

  return (
    <DataTable
      columns={columns}
      data={dummyData}
      // pagination={category.data?.pagination}
      // onPageChange={(page) => setCategoryQuery({ page })}
    />
  );
};

export default QuestionBankTable;
