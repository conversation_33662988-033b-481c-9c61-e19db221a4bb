"use client";

import {
  createTemplateQuestionBodySchema,
  ICreateTemplateQuestionBody,
} from "@/interfaces/admin/manage-test/question-template/mutation";
import { yupResolver } from "@hookform/resolvers/yup";
import React from "react";
import { FormProvider, useForm, useFormContext } from "react-hook-form";
import { InputString } from "../common/input";
import { Option } from "@/components/atoms/multiselect";
import { InputSelect } from "../common/select";
import QuestionTemplateTableSearchQuestion from "./search-question";
import { BaseButton } from "@/components/atoms/button";
import { Plus } from "lucide-react";
import ListQuestionBankModal from "./question-bank-modal";
import { useQuestionBankTableListStore } from "@/store/admin/manage-test/question-bank/list";
import { useShallow } from "zustand/react/shallow";
import SelectedQuestionBankTable from "./table";

const QuestionTemplateMutationForm = () => {
  const form = useForm({
    resolver: yupResolver(createTemplateQuestionBodySchema),
  });

  const onSubmit = () => {};

  return (
    <FormProvider {...form}>
      <form
        className="bg-white rounded-[8px] p-5 space-y-5 relative"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <p className="font-medium">Template Information</p>

        <div className="flex items-center gap-4">
          <InputString
            id="name"
            label="Template Name"
            placeholder="Input template name"
            onChange={(val) =>
              form.setValue("name", val, { shouldValidate: true })
            }
          />
          <div className="w-[60%]">
            <InputString
              id="id"
              label="Template ID"
              placeholder="ID"
              readonly
            />
          </div>
        </div>

        <QuestionForm />

        <div className="p-5 bg-white flex justify-end gap-2">
          <BaseButton className="h-12 px-5" variant="outline">
            Cancel
          </BaseButton>

          <BaseButton className="h-12 px-5" type="submit">
            Add Template
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};

const QUESTION_TYPE_OPTION: Option[] = [
  { value: "pilihan_ganda", label: "Benar Salah" },
  { value: "benar_salah", label: "Pilihan Ganda" },
  { value: "isian", label: "Isian" },
];

const QuestionForm = ({}: Readonly<{}>) => {
  const { selectedQuestionBanksFinal } = useQuestionBankTableListStore(
    useShallow(({ selectedQuestionBanksFinal }) => ({
      selectedQuestionBanksFinal,
    }))
  );

  const form = useFormContext<ICreateTemplateQuestionBody>();
  const [isOpenQuestionBankModal, setIsOpenQuestionBankModal] =
    React.useState<boolean>(false);

  return (
    <div className="space-y-5">
      <p className="font-medium">Template Question</p>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 w-[55%]">
          <div className="w-1/2">
            <InputSelect
              placeholder="Select Question Type"
              options={QUESTION_TYPE_OPTION}
            />
          </div>
          <QuestionTemplateTableSearchQuestion />
        </div>

        <BaseButton
          className="h-12 px-5"
          onClick={() => setIsOpenQuestionBankModal(true)}
        >
          <div className="flex items-center gap-2">
            <Plus />
            Add Question
          </div>
        </BaseButton>
      </div>

      {selectedQuestionBanksFinal.length ? (
        <SelectedQuestionBankTable />
      ) : (
        <div className="h-[240px] rounded-[8px] bg-[#F5F5F5] p-2 text-[#767676] text-sm border border-[#DEDEDE] flex flex-col justify-center items-center gap-1">
          <span>No question added</span>
          <span>Please select the question from Question Bank first</span>
        </div>
      )}

      <ListQuestionBankModal
        isOpen={isOpenQuestionBankModal}
        setOpenChange={setIsOpenQuestionBankModal}
      />
    </div>
  );
};

export default QuestionTemplateMutationForm;
