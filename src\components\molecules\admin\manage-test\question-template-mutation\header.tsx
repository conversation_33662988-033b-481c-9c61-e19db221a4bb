"use client";

import { BaseButton } from "@/components/atoms/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

const QuestionTemplateMutationHeader = () => {
  const router = useRouter();

  return (
    <div className="flex items-center gap-5">
      <BaseButton
        className="h-11 w-11"
        variant="outline"
        onClick={() => router.back()}
      >
        <ArrowLeft size={28} />
      </BaseButton>

      <div className="bg-white h-11 px-3 py-2.5 rounded-[8px] w-full">
        <span className="font-bold text-comp-content-primary">
          Add New Template
        </span>
      </div>
    </div>
  );
};

export default QuestionTemplateMutationHeader;
