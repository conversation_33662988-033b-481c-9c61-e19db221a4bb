"use client";

import {
  BaseDialog,
  <PERSON><PERSON><PERSON>og<PERSON>ontent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseSeparator } from "@/components/atoms/separator";
import { IQuestionBank } from "@/interfaces/admin/manage-test/question-bank/list";
import React from "react";
import QuestionTemplateTableSearchQuestion from "./search-question";
import { BaseButton } from "@/components/atoms/button";
import { Settings2 } from "lucide-react";
import QuestionBankTable from "../question-bank/table";
import QuestionBankTableHeaderFilter from "../question-bank/filter";
import { useQuestionBankTableListStore } from "@/store/admin/manage-test/question-bank/list";
import { useShallow } from "zustand/react/shallow";

interface Props {
  isOpen: boolean;
  setOpenChange: React.Dispatch<React.SetStateAction<boolean>>;
}

const ListQuestionBankModal = ({ isOpen, setOpenChange }: Readonly<Props>) => {
  const { setSelectedQuestionBanksFinal } = useQuestionBankTableListStore(
    useShallow(({ setSelectedQuestionBanksFinal }) => ({
      setSelectedQuestionBanksFinal,
    }))
  );

  const [isOpenFilter, setIsOpenFilter] = React.useState<boolean>(false);

  const handleOpenChangeModal = (state: boolean) => {
    setOpenChange(state);
  };

  const handleAddQuestion = () => {
    setSelectedQuestionBanksFinal();
    handleOpenChangeModal(false);
  };

  return (
    <BaseDialog open={isOpen} onOpenChange={handleOpenChangeModal}>
      <BaseDialogContent className="sm:max-w-[1282px] overflow-y-auto max-h-[95%]">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>Add Question from Question Bank</span>
            <BaseSeparator />
          </BaseDialogTitle>
        </BaseDialogHeader>

        <div className="min-h-[400px] space-y-5">
          <div className="flex justify-between items-center">
            <div className="w-[30%]">
              <QuestionTemplateTableSearchQuestion />
            </div>
            <BaseButton
              className="h-11 px-1"
              variant="outline"
              onClick={() => setIsOpenFilter((prev) => !prev)}
            >
              <div className="flex items-center gap-1 rounded-[8px] py-[13px] px-4">
                <Settings2 className="h-4 w-4" />
                <span className="text-sm font-medium">Filter</span>
              </div>
            </BaseButton>
          </div>

          {isOpenFilter ? (
            <QuestionBankTableHeaderFilter className="bg-[#F5F5F5]" />
          ) : null}

          <div className="overflow-auto max-w-[1232px]">
            <QuestionBankTable isSelectable />
          </div>

          <BaseSeparator />

          <div className="bg-white flex justify-end gap-2">
            <BaseButton
              className="h-12 px-5"
              variant="outline"
              onClick={() => handleOpenChangeModal(false)}
            >
              Cancel
            </BaseButton>

            <BaseButton className="h-12 px-5" onClick={handleAddQuestion}>
              Add Question
            </BaseButton>
          </div>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ListQuestionBankModal;
