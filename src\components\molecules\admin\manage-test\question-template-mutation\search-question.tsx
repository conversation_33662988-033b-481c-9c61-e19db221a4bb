"use client";

import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import { Search } from "lucide-react";
import React from "react";
import lodash from "lodash";

const QuestionTemplateTableSearchQuestion = () => {
  // const { setCategoryQuery } = useManageCategoryQueryStore(
  //   useShallow(
  //     ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     }) => ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     })
  //   )
  // );

  const handleQueryChange = (
    query: Partial<IGetListCategoryQuery | IGetListSubCategoryQuery>
  ) => {
    // setCategoryQuery(query as Partial<IGetListCategoryQuery>);
  };

  return (
    <div className="text-[#3C3C3C] rounded-lg flex items-center gap-1 relative bg-white border px-3 w-full">
      <div>
        <BaseSelect
          value={"question_type"}
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger size="md" className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            <BaseSelectItem value="question_type">Question Type</BaseSelectItem>
            <BaseSelectItem value="question">Question</BaseSelectItem>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        placeholder="Search by"
        className="border-none h-12 w-full focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default QuestionTemplateTableSearchQuestion;
