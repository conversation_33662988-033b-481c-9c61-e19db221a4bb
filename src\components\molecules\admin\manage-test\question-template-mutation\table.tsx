"use client";

import React from "react";
import { DataTable } from "../../../global/table";
import { useShallow } from "zustand/react/shallow";
import { useQuestionBankTableListStore } from "@/store/admin/manage-test/question-bank/list";
import { getColumnsSelectedQuestionBank } from "./column";

const SelectedQuestionBankTable = () => {
  const { selectedQuestionBanksFinal, deleteFinalQuestionBank } =
    useQuestionBankTableListStore(
      useShallow(({ selectedQuestionBanksFinal, deleteFinalQuestionBank }) => ({
        selectedQuestionBanksFinal,
        deleteFinalQuestionBank,
      }))
    );

  const columns = React.useMemo(
    () =>
      getColumnsSelectedQuestionBank({
        onDelete: (data) => {
          deleteFinalQuestionBank(data.questionId);
        },
      }),
    []
  );

  return (
    <DataTable
      columns={columns}
      data={selectedQuestionBanksFinal}
      // pagination={category.data?.pagination}
      // onPageChange={(page) => setCategoryQuery({ page })}
    />
  );
};

export default SelectedQuestionBankTable;
