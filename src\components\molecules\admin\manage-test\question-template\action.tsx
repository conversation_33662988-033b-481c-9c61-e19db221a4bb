"use client";

import { BaseButton } from "@/components/atoms/button";
import { Plus, Settings2 } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

interface Props {
  onOpenFilter: React.Dispatch<React.SetStateAction<boolean>>;
}

const QuestionTemplateTableHeaderAction = ({
  onOpenFilter,
}: Readonly<Props>) => {
  const router = useRouter();

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        className="h-12 px-5"
        variant="outline"
        onClick={() => onOpenFilter((prev) => !prev)}
      >
        <div className="flex items-center gap-1 rounded-[8px] py-[13px] px-4">
          <Settings2 className="h-4 w-4" />
          <span className="text-sm font-medium">Filter</span>
        </div>
      </BaseButton>

      <BaseButton
        className="h-12 px-5"
        onClick={() =>
          router.push("/admin/manage-test/question-template/mutation")
        }
      >
        <div className="flex items-center gap-2">
          <Plus />
          Add New Template
        </div>
      </BaseButton>
    </div>
  );
};

export default QuestionTemplateTableHeaderAction;
