import { BaseButton } from "@/components/atoms/button";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2 } from "lucide-react";
import PillDropdown from "../common/dropdown";
import { IQuestionTemplate } from "@/interfaces/admin/manage-test/question-template/list";

interface Props {
  onEdit: (id: IQuestionTemplate) => void;
  onDelete: (id: IQuestionTemplate) => void;
}

export const getColumnsQuestionTemplate = ({
  onEdit,
  onDelete,
}: Props): ColumnDef<IQuestionTemplate>[] => {
  const categories = ["Learning Material", "Assessment", "Tutorial", "Guide"];
  const levels = ["Beginner", "Intermediate", "Advanced"];

  return [
    {
      accessorKey: "id",
      header: "Question Template ID",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.id}
          </span>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.category}
            options={categories.map((category) => ({
              value: category,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "level",
      header: "Level",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.level}
            options={levels.map((level) => ({
              value: level,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "template_name",
      header: "Template Name",
      cell({ row }) {
        return (
          <div className="w-[320px] text-wrap line-clamp-2">
            {row.original.template_name}
          </div>
        );
      },
    },
    {
      accessorKey: "questions",
      header: "Questions",
      cell({ row }) {
        return (
          <PillDropdown
            selected={row.original.level}
            options={levels.map((level) => ({
              value: level,
              id: row.original.id.toString(),
            }))}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell(props) {
        const createdAt = props.row.original.created_at;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "created_by", header: "Created By" },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell(props) {
        const updatedAt = props.row.original.last_updated;

        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY HH:mm") : "-"}
          </span>
        );
      },
    },
    { accessorKey: "updated_by", header: "Updated By" },

    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start">
            <BaseButton
              variant={"ghost"}
              className="border-none"
              onClick={() => onEdit(row.original)}
            >
              <Pencil
                size={20}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"ghost"}
              className="border-none"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 size={20} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
