"use client";

import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useQuestionTemplateModal } from "@/store/admin/manage-test/question-template/modal";
import { Trash2 } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";

const QuestionTemplateDeleteConfirmationModal = () => {
  const {
    // openedQuestionTemplate,
    openDeleteModal,
    setOpenDeleteModal,
    setOpenedQuestionTemplate,
  } = useQuestionTemplateModal(
    useShallow(
      ({
        openedQuestionTemplate,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedQuestionTemplate,
      }) => ({
        openedQuestionTemplate,
        openDeleteModal,
        setOpenDeleteModal,
        setOpenedQuestionTemplate,
      })
    )
  );

  const handleDeleteData = () => {
    //  updateCategory.mutate(
    //       {
    //         id: openedQuestionTemplate!.id,
    //         body: {
    //           category_name: openedQuestionTemplate?.template_name!,
    //           is_deleted: true,
    //         },
    //       },
    //       {
    //         onSuccess: () => {
    //           handleOpenChange(false);
    //           toast.success("Question Template successfully deleted");
    //         },
    //         onError: () => {
    //           toast.error("Failed to delete Question Template");
    //         },
    //       }
    //     );
  };

  const handleOpenChange = (state: boolean) => {
    if (!state) {
      setOpenedQuestionTemplate(null);
    }
    setOpenDeleteModal(state);
  };

  return (
    <BaseDialog open={openDeleteModal} onOpenChange={handleOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus Question Template?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Question Template yang dipilih akan dihapus secara permanen.
            Tindakan ini tidak bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={() => handleDeleteData()}
            // disabled={updateCategory.isPending || updateSubCategory.isPending}
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionTemplateDeleteConfirmationModal;
