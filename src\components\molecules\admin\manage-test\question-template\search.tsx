"use client";

import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import { Search } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import lodash from "lodash";

const QuestionTemplateTableHeaderSearch = () => {
  const { setCategoryQuery } = useManageCategoryQueryStore(
    useShallow(
      ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      }) => ({
        categoryQuery,
        subCategoryQuery,
        setCategoryQuery,
        setSubCategoryQuery,
      })
    )
  );

  const handleQueryChange = (
    query: Partial<IGetListCategoryQuery | IGetListSubCategoryQuery>
  ) => {
    setCategoryQuery(query as Partial<IGetListCategoryQuery>);
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={"template_type"}
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            <BaseSelectItem value="template_type">Template Type</BaseSelectItem>
            <BaseSelectItem value="template_name">Template Name</BaseSelectItem>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default QuestionTemplateTableHeaderSearch;
