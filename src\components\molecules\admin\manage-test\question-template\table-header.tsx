"use client";

import React from "react";
import QuestionTemplateTableHeaderAction from "./action";
import QuestionTemplateTableHeaderSearch from "./search";
import QuestionTemplateTableHeaderFilter from "./filter";

const QuestionTemplateTableHeader = () => {
  const [openFilter, setOpenFilter] = React.useState<boolean>(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <QuestionTemplateTableHeaderSearch />
        <QuestionTemplateTableHeaderAction onOpenFilter={setOpenFilter} />
      </div>

      {openFilter ? <QuestionTemplateTableHeaderFilter /> : null}
    </div>
  );
};

export default QuestionTemplateTableHeader;
