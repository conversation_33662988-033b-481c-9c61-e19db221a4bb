"use client";

import React from "react";
import { DataTable } from "../../../global/table";
import { getColumnsQuestionTemplate } from "./column";
import { useManageCategoryModal } from "@/store/admin/manage-category/modal";
import { useShallow } from "zustand/react/shallow";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import dayjs from "dayjs";
import { IQuestionTemplate } from "@/interfaces/admin/manage-test/question-template/list";
import { useQuestionTemplateModal } from "@/store/admin/manage-test/question-template/modal";

const dummyData: IQuestionTemplate[] = [
  {
    id: 1,
    template_name: "Module A Learning Development System",
    template_type: "Pilihan Ganda",
    category: "2 Category",
    level: "4 Levels",
    questions: "a",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    id: 2,
    template_name: "Module A Learning Development System",
    template_type: "Pilihan Ganda",
    category: "2 Category",
    level: "4 Levels",
    questions: "a",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    id: 3,
    template_name: "Module A Learning Development System",
    template_type: "Pilihan Ganda",
    category: "2 Category",
    level: "4 Levels",
    questions: "a",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
  {
    id: 4,
    template_name: "Module A Learning Development System",
    template_type: "Pilihan Ganda",
    category: "2 Category",
    level: "4 Levels",
    questions: "a",
    created_at: dayjs().toISOString(),
    created_by: "System",
    last_updated: dayjs().toISOString(),
    updated_by: "System",
  },
];

const QuestionTemplateTable = () => {
  const { setOpenAddModal, setOpenDeleteModal, setOpenedQuestionTemplate } =
    useQuestionTemplateModal(
      useShallow(
        ({
          setOpenAddModal,
          setOpenedQuestionTemplate,
          setOpenDeleteModal,
        }) => ({
          setOpenAddModal,
          setOpenedQuestionTemplate,
          setOpenDeleteModal,
        })
      )
    );

  // const { setCategoryQuery } = useManageCategoryQueryStore(
  //   useShallow(
  //     ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     }) => ({
  //       categoryQuery,
  //       subCategoryQuery,
  //       setCategoryQuery,
  //       setSubCategoryQuery,
  //     })
  //   )
  // );

  const columns = React.useMemo(
    () =>
      getColumnsQuestionTemplate({
        onEdit: (data) => {
          setOpenedQuestionTemplate(data);
          setOpenAddModal(true);
        },
        onDelete: (data) => {
          setOpenedQuestionTemplate(data);
          setOpenDeleteModal(true);
        },
      }),
    []
  );

  return (
    <DataTable
      columns={columns}
      data={dummyData}
      // pagination={category.data?.pagination}
      // onPageChange={(page) => setCategoryQuery({ page })}
    />
  );
};

export default QuestionTemplateTable;
