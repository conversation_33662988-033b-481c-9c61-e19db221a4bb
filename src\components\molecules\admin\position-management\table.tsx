"use client";

import React from "react";
import PositionManagementTableHeader from "./table-header";
import { DataTable } from "../../global/table";
import { ColumnDef } from "@tanstack/react-table";
import { IPositionManagement } from "@/interfaces/admin/position-management-ai/list";
import dayjs from "dayjs";
import { BaseButton } from "@/components/atoms/button";
import { Pencil, Trash2 } from "lucide-react";
import { cn } from "@/utils/common";

export const columns: ColumnDef<IPositionManagement>[] = [
  {
    accessorKey: "job_position_id",
    header: "Job Position ID",
  },
  {
    accessorKey: "job_position_name",
    header: "Job Position Name",
  },
  {
    accessorKey: "job_type",
    header: "Job Type",
  },
  {
    accessorKey: "department",
    header: "Department",
  },
  {
    accessorKey: "job_function",
    header: "Job Function",
  },
  {
    accessorKey: "starting_learning_level",
    header: "Starting Learning Level",
  },
  {
    accessorKey: "need_neo",
    header: "Need NEO",
    cell({ row }) {
      return row.original.need_neo ? "Yes" : "No";
    },
  },
  {
    accessorKey: "id",
    header: "Action",
    cell({ row }) {
      return (
        <div className="flex gap-2">
          <BaseButton size="sm" variant="outline">
            <Pencil className="h-4 w-4" />
          </BaseButton>
          <BaseButton size="sm" variant="outline">
            <Trash2 className="h-4 w-4" />
          </BaseButton>
        </div>
      );
    },
  },
];

const sampleData: IPositionManagement[] = [
  {
    id: 1,
    job_position_id: "JP001",
    job_position_name: "Software Engineer",
    job_type: "Full-Time",
    department: "Engineering",
    job_function: "Development",
    starting_learning_level: "Junior",
    need_neo: false,
  },
  {
    id: 2,
    job_position_id: "JP002",
    job_position_name: "Product Manager",
    job_type: "Full-Time",
    department: "Product",
    job_function: "Management",
    starting_learning_level: "Mid-Level",
    need_neo: true,
  },
];

const PositionManagementTable = () => {
  return (
    <div className="flex flex-col gap-4">
      <PositionManagementTableHeader />
      <DataTable columns={columns} data={sampleData} />
    </div>
  );
};

export default PositionManagementTable;