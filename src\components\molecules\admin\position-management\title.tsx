import { BaseButton } from "@/components/atoms/button";
import React from "react";

const PositionManagementTitle = () => {
  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        Position Management
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseButton className="h-9 w-1/2">Export Data</BaseButton>
        <BaseButton className="h-9 w-1/2" variant="default">
          Add New
        </BaseButton>
      </div>
    </div>
  );
};

export default PositionManagementTitle;