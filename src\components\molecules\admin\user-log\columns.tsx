import { IUserLog } from "@/interfaces/admin/user-log/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";

export const getColumnsUserLogLogin = (): ColumnDef<IUserLog>[] => {
  return [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "npk",
      header: "NPK",
    },
    {
      accessorKey: "start_date",
      header: "Start Date",
      cell(props) {
        const startDate = props.row.original.start_date;
        return (
          <span>
            {startDate ? dayjs(startDate).format("DD MMM YYYY HH:mm:ss") : "-"}
          </span>
        );
      },
    },
    {
      accessorKey: "end_date",
      header: "End Date",
      cell(props) {
        const endDate = props.row.original.end_date;
        return (
          <span>
            {endDate ? dayjs(endDate).format("DD MMM YYYY HH:mm:ss") : "-"}
          </span>
        );
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
    },
    {
      accessorKey: "created_at",
      header: "Created Date",
      cell(props) {
        const createdAt = props.row.original.created_date;
        return (
          <span>
            {createdAt ? dayjs(createdAt).format("DD MMM YYYY") : "-"}
          </span>
        );
      },
    },
    {
      accessorKey: "updated_at",
      header: "Updated Date",
      cell(props) {
        const updatedAt = props.row.original.updated_date;
        return (
          <span>
            {updatedAt ? dayjs(updatedAt).format("DD MMM YYYY") : "-"}
          </span>
        );
      },
    },
  ];
};
