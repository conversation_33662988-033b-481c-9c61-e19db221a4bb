"use client";

import { BaseButton } from "@/components/atoms/button";
import { useDownloadListLogUserLoginFileMutation } from "@/services/mutation/admin/user-log-login";
import { useUserLogLoginFilterStore } from "@/store/admin/log-user/filter";
import { Download } from "lucide-react";

const UserLogTableHeaderFilter = () => {
  const { userLogLoginQuery } = useUserLogLoginFilterStore();
  const donwloadFile = useDownloadListLogUserLoginFileMutation();

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        className="h-12 px-5"
        onClick={() => donwloadFile.mutate(userLogLoginQuery)}
        disabled={donwloadFile.isPending}
      >
        <div className="flex items-center gap-2">
          <Download />
          Download CSV
        </div>
      </BaseButton>
    </div>
  );
};

export default UserLogTableHeaderFilter;
