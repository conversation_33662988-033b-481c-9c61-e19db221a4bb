import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { useUserLogLoginFilterStore } from "@/store/admin/log-user/filter";
import { Search } from "lucide-react";
import React from "react";
import lodash from "lodash";

const UserLogTableHeaderSearch = () => {
  const { userLogLoginQuery, setUserLogLoginQuery } =
    useUserLogLoginFilterStore();

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <BaseSelect
        value={userLogLoginQuery.search_by}
        onValueChange={(value) =>
          setUserLogLoginQuery({ search_by: value as any })
        }
      >
        <BaseSelectTrigger className="w-32 border-none px-1">
          <BaseSelectValue placeholder="Search By" />
        </BaseSelectTrigger>
        <BaseSelectContent>
          <BaseSelectItem value="npk">NPK</BaseSelectItem>
        </BaseSelectContent>
      </BaseSelect>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        onChange={lodash.debounce(
          (e) => setUserLogLoginQuery({ page: 1, search: e?.target?.value }),
          800
        )}
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default UserLogTableHeaderSearch;
