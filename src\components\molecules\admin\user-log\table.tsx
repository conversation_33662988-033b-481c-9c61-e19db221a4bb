"use client";

import React from "react";
import { DataTable } from "../../global/table";
import UserLogTableHeader from "./table-header";
import { getColumnsUserLogLogin } from "./columns";
import { useGetListUserLogLoginQuery } from "@/services/query/admin/user-log-login";
import { useUserLogLoginFilterStore } from "@/store/admin/log-user/filter";

const UserLogTable = () => {
  const { userLogLoginQuery, setUserLogLoginQuery } =
    useUserLogLoginFilterStore();

  const logLogin = useGetListUserLogLoginQuery(userLogLoginQuery);

  const logLoginColumns = React.useMemo(
    () => getColumnsUserLogLogin(),
    [logLogin.data]
  );

  return (
    <div className="flex flex-col gap-4">
      <UserLogTableHeader />
      <DataTable
        columns={logLoginColumns}
        data={logLogin.data?.data ?? []}
        pagination={logLogin.data?.pagination}
        onPageChange={(page) => setUserLogLoginQuery({ page })}
      />
    </div>
  );
};

export default UserLogTable;
