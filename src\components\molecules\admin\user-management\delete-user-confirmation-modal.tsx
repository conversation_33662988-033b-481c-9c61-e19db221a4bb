import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useDeleteUserMutation } from "@/services/mutation/user-management/delete";
import { useGetUserListQuery } from "@/services/query/user-management/list";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { Trash2 } from "lucide-react";
import React from "react";
import toast from "react-hot-toast";
import { useShallow } from "zustand/react/shallow";

const UserManagementDeleteConfirmationModal = () => {
  const { openDeleteUser, setOpenDeleteUser, currentData } =
    useUserManagementModalStore(
      useShallow(({ openDeleteUser, setOpenDeleteUser, currentData }) => ({
        openDeleteUser,
        setOpenDeleteUser,
        currentData,
      }))
    );

  const { query } = useUserManagementFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const userList = useGetUserListQuery(query);

  const deleteUser = useDeleteUserMutation();

  const handleDeleteUser = async () => {
    if (currentData) {
      deleteUser.mutate(
        { id: currentData },
        {
          onSuccess: (data) => {
            userList.refetch();
            toast.success(data.message);
            setOpenDeleteUser(false);
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to delete user");
          },
        }
      );
    }
  };

  return (
    <BaseDialog open={openDeleteUser} onOpenChange={setOpenDeleteUser}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-red-200 w-fit p-2 rounded-full border-8 border-red-100 bg">
            <Trash2 className="text-red-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Hapus User?
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            User yang dipilih akan dihapus secara permanen. Tindakan ini tidak
            bisa dibatalkan.
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={"destructive"}
            onClick={handleDeleteUser}
          >
            Hapus
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default UserManagementDeleteConfirmationModal;
