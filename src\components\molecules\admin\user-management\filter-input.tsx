import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { useGetUserMasterEntity } from "@/services/query/user-management/user-entity";
import { useGetUserMasterRole } from "@/services/query/user-management/user-role";
import { useGetUserMasterType } from "@/services/query/user-management/user-type";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { cn } from "@/utils/common";
import React, { useState } from "react";
import { useShallow } from "zustand/react/shallow";

interface IFilter {
  user_type_id: number | undefined;
  user_entity_id: number | undefined;
  user_role_id: number | undefined;
  is_neop: boolean | undefined;
}

const UserManagementFilterInput = () => {
  const [filter, setFilter] = useState<IFilter>({
    user_type_id: undefined,
    user_entity_id: undefined,
    user_role_id: undefined,
    is_neop: undefined,
  });

  const { query, setQuery, openFilter } = useUserManagementFilterStore(
    useShallow(({ setQuery, query, openFilter }) => ({
      setQuery,
      query,
      openFilter,
    }))
  );

  const userTypes = useGetUserMasterType({});
  const userEntities = useGetUserMasterEntity({});
  const userRoles = useGetUserMasterRole({});

  const handleChange = (
    key: keyof IFilter,
    value: number | boolean | undefined
  ) => {
    setFilter({ ...filter, [key]: value });
  };

  const handleApply = () => {
    setQuery({
      ...query,
      type_id: filter.user_type_id,
      entity_id: filter.user_entity_id,
      role_id: filter.user_role_id,
      is_neop: filter.is_neop,
      page: 1,
    });
  };

  const handleReset = () => {
    setFilter({
      user_type_id: undefined,
      user_entity_id: undefined,
      user_role_id: undefined,
      is_neop: undefined,
    });

    setQuery({
      ...query,
      type_id: undefined,
      entity_id: undefined,
      role_id: undefined,
      is_neop: undefined,
      page: 1,
    });
  };

  return (
    <div
      hidden={!openFilter}
      className={cn("flex flex-col gap-4 w-full bg-white rounded-lg p-3")}
    >
      <div className="flex items-center justify-between">
        <span className="mt-1 font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            className="w-24 h-10 text-red-600 border-red-600 hover:text-red-600"
            variant={"outline"}
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton className="w-24 h-10" onClick={handleApply}>
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="flex gap-4 justify-between">
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">User Type</BaseLabel>
          <BaseSelect
            value={filter.user_type_id?.toString() ?? ""}
            onValueChange={(value) => handleChange("user_type_id", +value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {userTypes.data?.data.map((item) => (
                <BaseSelectItem
                  value={item?.type_id ? item.type_id.toString() : ""}
                  key={`user-type-${item.type_id}`}
                >
                  {item.type_name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">User Role</BaseLabel>
          <BaseSelect
            value={filter.user_role_id?.toString() ?? ""}
            onValueChange={(value) => handleChange("user_role_id", +value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {userRoles.data?.data.map((item) => (
                <BaseSelectItem
                  value={item?.role_id ? item.role_id.toString() : ""}
                  key={`user-role-${item.role_id}`}
                >
                  {item.role_name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs">Entity</BaseLabel>
          <BaseSelect
            onValueChange={(value) => handleChange("user_entity_id", +value)}
            value={filter.user_entity_id?.toString() ?? ""}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {userEntities.data?.data.map((item) => (
                <BaseSelectItem
                  value={item?.entity_id ? item.entity_id.toString() : ""}
                  key={`user-entity-${item.entity_id}`}
                >
                  {item.entity_name}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>
        <div className="flex flex-col gap-1 w-1/4">
          <BaseLabel className="text-xs" htmlFor="neop">
            Is Need NEOP or Welcoming Kit
          </BaseLabel>
          <BaseSelect
            name="neop"
            value={filter.is_neop?.toString() ?? ""}
            onValueChange={(value) => handleChange("is_neop", value === "true")}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select..." />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="true">Yes</BaseSelectItem>
              <BaseSelectItem value="false">No</BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>
  );
};

export default UserManagementFilterInput;
