"use client";

import { BaseButton } from "@/components/atoms/button";
import { useUserManagementTabStore } from "@/store/admin/user-management/tab";
import { cn } from "@/utils/common";
import {
  Check,
  DownloadCloud,
  Plus,
  RefreshCw,
  Settings2,
  UploadCloud,
} from "lucide-react";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { useShallow } from "zustand/react/shallow";
import UserManagementNewUserModal from "./new-user-modal";
import UserManagementVerifyConfirmationModal from "./verify-user-confirmation-modal";
import {
  BaseTooltip,
  BaseTooltipContent,
  BaseTooltipTrigger,
} from "@/components/atoms/tooltip";
import UserManagementUploadHistoryModal from "./upload-history-modal";

import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from "@/components/atoms/dropdown";
import UserManagementUploadExcelModal from "./upload-excel-modal";
import { useGetUserListQuery } from "@/services/query/user-management/list";

const UserManagementTableHeaderFilter = () => {
  const { activeTab } = useUserManagementTabStore(
    useShallow((state) => ({ activeTab: state.activeTab }))
  );
  const { openFilter, setOpenFilter } = useUserManagementFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  const { query } = useUserManagementFilterStore(
    useShallow(({ query }) => ({ query }))
  );

  const list = useGetUserListQuery(query);

  const {
    setOpenAddUser,
    setOpenVerifyUserConfirmation,
    setOpenUploadHistory,
    setOpenUploadExcel,
  } = useUserManagementModalStore(
    useShallow(
      ({
        setOpenAddUser,
        setOpenVerifyUserConfirmation,
        setOpenUploadHistory,
        setOpenUploadExcel,
      }) => ({
        setOpenAddUser,
        setOpenVerifyUserConfirmation,
        setOpenUploadHistory,
        setOpenUploadExcel,
      })
    )
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseDropdownMenu>
        <BaseDropdownMenuTrigger asChild>
          <BaseButton variant={"outline"} size={"icon"} className="size-12">
            <DownloadCloud />
          </BaseButton>
        </BaseDropdownMenuTrigger>
        <BaseDropdownMenuContent>
          <BaseDropdownMenuItem className="px-4">
            Download Excel Data
          </BaseDropdownMenuItem>
          <BaseDropdownMenuItem className="px-4">
            Download Excel Template
          </BaseDropdownMenuItem>
        </BaseDropdownMenuContent>
      </BaseDropdownMenu>
      <BaseDropdownMenu>
        <BaseDropdownMenuTrigger asChild>
          <BaseButton variant={"outline"} size={"icon"} className="size-12">
            <UploadCloud />
          </BaseButton>
        </BaseDropdownMenuTrigger>
        <BaseDropdownMenuContent>
          <BaseDropdownMenuItem
            className="px-4"
            onClick={() => setOpenUploadExcel(true)}
          >
            Upload Excel Data
          </BaseDropdownMenuItem>
          <BaseDropdownMenuItem className="px-4">
            Upload Excel Template
          </BaseDropdownMenuItem>
        </BaseDropdownMenuContent>
      </BaseDropdownMenu>
      <BaseTooltip>
        <BaseTooltipTrigger asChild>
          <BaseButton
            variant={"outline"}
            className="size-12"
            onClick={() => setOpenUploadHistory(true)}
          >
            <RefreshCw />
          </BaseButton>
        </BaseTooltipTrigger>
        <BaseTooltipContent side="bottom">
          <p>Upload Progress and History</p>
        </BaseTooltipContent>
      </BaseTooltip>
      <BaseButton
        variant={"outline"}
        className={cn("h-12 px-8", openFilter && "bg-gray-200")}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
      {activeTab === "regular" ? (
        <BaseButton className="h-12 px-5" onClick={() => setOpenAddUser(true)}>
          <div className="flex items-center gap-2">
            <Plus />
            Add New User
          </div>
        </BaseButton>
      ) : (
        <BaseButton
          className="h-12 px-5"
          onClick={() => setOpenVerifyUserConfirmation(true)}
        >
          <div className="flex items-center gap-2">
            <Check />
            Verify User{" "}
            {activeTab === "new" && !list.isLoading
              ? `(${list.data?.pagination?.total_data ?? 0})`
              : ""}
          </div>
        </BaseButton>
      )}
      <UserManagementNewUserModal />
      <UserManagementVerifyConfirmationModal />
      <UserManagementUploadHistoryModal />
      <UserManagementUploadExcelModal />
    </div>
  );
};

export default UserManagementTableHeaderFilter;
