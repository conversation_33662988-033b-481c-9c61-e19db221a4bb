import { <PERSON><PERSON><PERSON><PERSON>, Base<PERSON>lertDescription } from "@/components/atoms/alert";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  createUserFormSchema,
  ICreateUserForm,
} from "@/interfaces/admin/user-management/new";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { isNumberInput } from "@/utils/common/number";
import { yupResolver } from "@hookform/resolvers/yup";
import { DialogClose } from "@radix-ui/react-dialog";
import {
  Check,
  ChevronsUpDown,
  CircleAlert,
  CloudUpload,
  Download,
  Pencil,
  Trash2,
} from "lucide-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Controller,
  DefaultValues,
  FormProvider,
  Path,
  useForm,
  useFormContext,
} from "react-hook-form";
import { useShallow } from "zustand/react/shallow";
import UserManagementResetPasswordModal from "./reset-password-modal";
import { useGetMasterJobPosition } from "@/services/query/user-management/job-position";
import { useGetUserMasterEntity } from "@/services/query/user-management/user-entity";
import { useGetMasterForumTitle } from "@/services/query/user-management/forum-title";
import { useGetUserMasterRole } from "@/services/query/user-management/user-role";
import { useGetUserMasterType } from "@/services/query/user-management/user-type";
import { useGetMasterUserList } from "@/services/query/user-management/user-list";
import { useCreateNewUserMutation } from "@/services/mutation/user-management/new";
import toast from "react-hot-toast";
import { useUpdateUserMutation } from "@/services/mutation/user-management/update";
import { useGetUserDetailQuery } from "@/services/query/user-management/detail";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useGetUserListQuery } from "@/services/query/user-management/list";
import {
  BasePopover,
  BasePopoverContent,
  BasePopoverTrigger,
} from "@/components/atoms/popover";
import {
  BaseCommand,
  BaseCommandEmpty,
  BaseCommandGroup,
  BaseCommandInput,
  BaseCommandItem,
  BaseCommandList,
} from "@/components/atoms/command";
import { cn } from "@/lib/utils";
import { useGetFileQuery } from "@/services/query/file/get";
import { bufferToFile } from "@/utils/common/file";

const EMPTY: DefaultValues<ICreateUserForm> = {
  npk: "",
  name: "",
  email: "",
  second_email: "",
  phone_number: "",
  forum_title_id: undefined,
  job_name_id: undefined,
  user_type_id: undefined,
  user_role_id: undefined,
  supervisor_id: undefined,
  entity_id: undefined,
  is_need_neop: undefined,
  is_need_welcoming_kit: undefined,
  is_active: undefined,
  user_signature: undefined,
};

const UserManagementNewUserModal = () => {
  const {
    openAddUser,
    setOpenAddUser,
    setOpenResetPassword,
    setCurrentData,
    currentData,
  } = useUserManagementModalStore(
    useShallow(
      ({
        openAddUser,
        setOpenAddUser,
        setOpenResetPassword,
        setCurrentData,
        currentData,
      }) => ({
        openAddUser,
        setOpenAddUser,
        setOpenResetPassword,
        currentData,
        setCurrentData,
      })
    )
  );

  const { query } = useUserManagementFilterStore(
    useShallow(({ query }) => ({ query }))
  );
  const users = useGetUserListQuery(query);

  const jobs = useGetMasterJobPosition({});
  const userEntities = useGetUserMasterEntity({});
  const forumTitles = useGetMasterForumTitle({});
  const userRoles = useGetUserMasterRole({});
  const userTypes = useGetUserMasterType({});
  const userList = useGetMasterUserList({});

  const createNewUserMutation = useCreateNewUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const userDetail = useGetUserDetailQuery({ id: currentData });
  const currentImage = useGetFileQuery({
    path: userDetail.data?.data?.signature ?? "",
  });

  const form = useForm({
    resolver: yupResolver(createUserFormSchema),
    mode: "all",
  });

  const handleSubmit = (data: ICreateUserForm) => {
    if (!currentData) {
      createNewUserMutation.mutate(
        {
          form: data,
          forumTitles: forumTitles.data?.data ?? [],
          jobs: jobs.data?.data ?? [],
          userList: userList.data?.data ?? [],
        },
        {
          onSuccess: () => {
            toast.success("User created successfully");
            form.reset();
            setOpenAddUser(false);
            setCurrentData(null);
            users.refetch();
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to create user");
          },
        }
      );
    } else {
      updateUserMutation.mutate(
        {
          id: currentData,
          form: data,
          forumTitles: forumTitles.data?.data ?? [],
          jobs: jobs.data?.data ?? [],
          userList: userList.data?.data ?? [],
        },
        {
          onSuccess: () => {
            toast.success("User updated successfully");
            form.reset();
            setOpenAddUser(false);
            setCurrentData(null);
            users.refetch();
          },
          onError: (data) => {
            toast.error(data?.message ?? "Failed to update user");
          },
        }
      );
    }
  };

  useEffect(() => {
    console.log(form.formState.errors);
  }, [form.formState.errors]);

  const handleClose = (open: boolean) => {
    setOpenAddUser(open);
    if (!open) {
      setCurrentData(null);
      form.reset(EMPTY, {
        keepErrors: false,
        keepDirty: false,
        keepTouched: false,
      });
    }
  };

  useEffect(() => {
    if (userDetail.data) {
      form.reset({
        npk: userDetail.data.data.npk ?? undefined,
        name: userDetail.data.data.name ?? undefined,
        email: userDetail.data.data.email ?? undefined,
        second_email: userDetail.data.data.second_email ?? undefined,
        phone_number: userDetail.data.data.phone_number ?? undefined,
        forum_title_id: userDetail.data.data.forum_title_id ?? undefined,
        job_name_id: userDetail.data.data.job_name_id ?? undefined,
        user_type_id: userDetail.data.data.user_type_id ?? undefined,
        user_role_id: userDetail.data.data.user_role_id ?? undefined,
        supervisor_id: userDetail.data.data.supervisor_id ?? undefined,
        is_need_neop: userDetail.data.data.is_need_neop ?? undefined,
        is_need_welcoming_kit:
          userDetail.data.data.is_need_welcoming_kit ?? undefined,
        is_active: userDetail.data.data.is_active ?? undefined,
        entity_id: userDetail.data.data.entity_id ?? undefined,
      });
    }
    if (currentImage.data) {
      const file = bufferToFile(currentImage.data, "&.png", "image/png");
      form.setValue("user_signature", file, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    }
  }, [userDetail.data, currentImage.data]);

  return (
    <>
      <BaseDialog open={openAddUser} onOpenChange={handleClose}>
        <BaseDialogContent className="min-w-5xl overflow-y-auto overflow-x-hidden h-fit">
          <BaseDialogHeader>
            <BaseDialogTitle className="flex flex-col gap-4">
              <span>
                {currentData ? "Edit and Verify New User" : "Add New User"}
              </span>
              <BaseSeparator />
            </BaseDialogTitle>
          </BaseDialogHeader>
          <div className="flex flex-col gap-4">
            <FormProvider {...form} key={currentData}>
              <form
                className="flex flex-col gap-6"
                onSubmit={form.handleSubmit(handleSubmit)}
              >
                <div className="flex flex-col gap-2">
                  <span>User Information</span>
                  <div className="grid grid-cols-3 gap-4">
                    <InputString label="NPK" id="npk" placeholder="Input NPK" />
                    <InputString
                      label="Full Name"
                      id="name"
                      placeholder="Input full name"
                    />
                    <InputSelect
                      id="job_name_id"
                      label="Job Position"
                      placeholder="Select job position"
                      options={
                        jobs.data?.data?.map((job) => ({
                          label: job.job_name ?? "",
                          value: job.job_id?.toString() ?? "",
                        })) ?? []
                      }
                    />
                    <InputString
                      label="First Email"
                      id="email"
                      placeholder="Input first email"
                    />
                    <InputString
                      label="Second Email"
                      id="second_email"
                      placeholder="Input secondary email"
                      optional
                    />
                    <InputNumber
                      label="Phone Number"
                      id="phone_number"
                      placeholder="Input phone number"
                    />
                    <InputSelect
                      id="user_type_id"
                      label="User Type"
                      placeholder="Select user type"
                      options={
                        userTypes.data?.data?.map((type) => ({
                          label: type.type_name ?? "",
                          value: type.type_id?.toString() ?? "",
                        })) ?? []
                      }
                    />
                    <div className="col-span-2">
                      <InputSelect
                        id="supervisor_id"
                        label="Supervisor"
                        placeholder="Select supervisor"
                        options={
                          userList.data?.data?.map((user) => ({
                            label: `${user.user_npk} - ${user.user_name}`,
                            value: user.user_id?.toString() ?? "",
                          })) ?? []
                        }
                        optional
                      />
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <span>User Configuration</span>
                  <div className="grid grid-cols-3 gap-4">
                    <InputSelect
                      id="user_role_id"
                      label="User Role"
                      placeholder="Select user role"
                      options={
                        userRoles.data?.data?.map((role) => ({
                          label: role.role_name ?? "",
                          value: role.role_id?.toString() ?? "",
                        })) ?? []
                      }
                    />
                    <InputSelect
                      id="forum_title_id"
                      label="Forum Title"
                      placeholder="Select title"
                      options={
                        forumTitles.data?.data?.map((forumTitle) => ({
                          label: forumTitle.forum_name ?? "",
                          value: forumTitle.forum_id?.toString() ?? "",
                        })) ?? []
                      }
                    />
                    <InputSelect
                      id="is_need_neop"
                      label="Is User Need NEOP Module"
                      placeholder="Select option"
                      options={[
                        { label: "YES", value: "true" },
                        { label: "NO", value: "false" },
                      ]}
                    />
                    <InputSelect
                      id="entity_id"
                      label="User Entity"
                      placeholder="Select user entity"
                      options={
                        userEntities.data?.data?.map((entity) => ({
                          label: entity.entity_name ?? "",
                          value: entity.entity_id?.toString() ?? "",
                        })) ?? []
                      }
                    />
                    <InputSelect
                      id="is_active"
                      label="Status"
                      placeholder="Select option"
                      options={[
                        { label: "Active", value: "true" },
                        { label: "Inactive", value: "false" },
                      ]}
                    />
                    <InputSelect
                      id="is_need_welcoming_kit"
                      label="Is User Need Welcoming Kit Module"
                      placeholder="Select option"
                      options={[
                        { label: "YES", value: "true" },
                        { label: "NO", value: "false" },
                      ]}
                    />
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <span>
                    User Signature{" "}
                    <span className="text-xs text-gray-500">(optional)</span>
                  </span>
                  <BaseAlert className="py-4">
                    <CircleAlert strokeWidth={3} absoluteStrokeWidth />
                    <BaseAlertDescription className="text-gray-500 text-sm">
                      Please remove the image background first before uploading
                      the image
                    </BaseAlertDescription>
                  </BaseAlert>
                  <InputFile id="user_signature" />
                </div>
                <BaseSeparator className="mt-4 -mb-2" />
                <div className="flex justify-between">
                  <DialogClose asChild>
                    <BaseButton
                      variant={"outline"}
                      className="w-32 h-11"
                      onClick={() => setOpenResetPassword(true)}
                    >
                      Reset Password
                    </BaseButton>
                  </DialogClose>
                  <div className="flex justify-end gap-3">
                    <DialogClose asChild>
                      <BaseButton
                        className="h-11 w-32"
                        variant={"outline"}
                        onClick={() => setOpenAddUser(false)}
                      >
                        Cancel
                      </BaseButton>
                    </DialogClose>
                    <BaseButton
                      className="h-11 w-32"
                      type="submit"
                      disabled={
                        Object.keys(form.formState.errors).length > 0 ||
                        !form.formState.isValid
                      }
                    >
                      {currentData ? "Save and Verify" : "Add User"}
                    </BaseButton>
                  </div>
                </div>
              </form>
            </FormProvider>
          </div>
        </BaseDialogContent>
      </BaseDialog>
      <UserManagementResetPasswordModal />
    </>
  );
};

const InputFile = ({
  id,
  placeholder,
}: {
  id: Path<ICreateUserForm>;
  placeholder?: string;
}) => {
  const form = useFormContext<ICreateUserForm>();
  const inputRef = useRef<HTMLInputElement | null>(null);

  // ambil nilai saat ini dari RHF (bisa FileList/File/undefined tergantung setup)
  const currentValue = form.watch(id) as unknown;

  // ekstrak File tunggal dari value (support jika schema menyimpan FileList)
  const file: File | undefined = useMemo(() => {
    if (!currentValue) return undefined;
    if (currentValue instanceof File) return currentValue;
    if (typeof FileList !== "undefined" && currentValue instanceof FileList) {
      return currentValue[0];
    }
    return undefined;
  }, [currentValue]);

  // generate preview URL saat ada file
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  useEffect(() => {
    if (!file) {
      setPreviewUrl(null);
      return;
    }
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    return () => URL.revokeObjectURL(url);
  }, [file]);

  // handler pilih file
  const onPickFile: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;

    // validasi ringan (2MB, PNG) — sesuaikan kebutuhanmu
    if (f.size > 2 * 1024 * 1024) {
      form.setError(id, { type: "validate", message: "Max size 2MB" });
      e.target.value = "";
      return;
    }
    if (!f.type.includes("png")) {
      form.setError(id, { type: "validate", message: "Only PNG supported" });
      e.target.value = "";
      return;
    }

    form.clearErrors(id);
    // simpan File (bukan FileList) supaya enak dipakai ke API
    form.setValue(id, f as any, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const openPicker = () => inputRef.current?.click();
  const onRemove = () => {
    form.setValue(id, undefined as any, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
    form.clearErrors(id);
    if (inputRef.current) inputRef.current.value = "";
  };
  const onDownload = () => {
    if (!file) return;
    const url = URL.createObjectURL(file);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name || "file.png";
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };
  const err = (form.formState.errors as any)?.[id]?.message as
    | string
    | undefined;

  return (
    <div className="flex flex-col gap-1">
      {/* ========== MODE AWAL: dropzone label (TIDAK DIUBAH) ========== */}
      {!previewUrl && (
        <>
          <BaseLabel
            className="w-full py-12 rounded-md border border-dashed border-gray-300 bg-gray-50 flex justify-center items-center gap-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-lg"
            htmlFor={id as string}
            onClick={(e) => {
              e.preventDefault();
              openPicker();
            }}
          >
            <CloudUpload size={48} className="text-gray-300" />
            {"Drag & drop your files or"}
            <span className="text-orange-400">browse</span>
          </BaseLabel>
          <span className="text-sm text-gray-400 mt-1">
            Supported file types: PNG max. size 2MB.
          </span>
        </>
      )}

      {/* ========== MODE PREVIEW: gambar + tombol kanan-bawah ========== */}
      {previewUrl && (
        <div className="relative w-full rounded-md border border-gray-200 bg-gray-100 overflow-hidden">
          {/* aspect ratio mendekati contoh */}
          <div className="w-full" style={{ paddingTop: "30%" }} />
          <img
            src={previewUrl}
            alt="preview"
            className="absolute inset-0 w-full h-full object-cover"
          />

          <div className="absolute right-3 bottom-3 flex items-center gap-2">
            {/* Hapus */}
            <button
              type="button"
              onClick={onRemove}
              className="h-8 w-8 rounded-md bg-white/90 hover:bg-white shadow flex items-center justify-center"
              aria-label="Remove"
              title="Remove"
            >
              <Trash2 size={16} />
            </button>
            {/* Edit = buka file picker lagi */}
            <button
              type="button"
              onClick={openPicker}
              className="h-8 w-8 rounded-md bg-white/90 hover:bg-white shadow flex items-center justify-center"
              aria-label="Change"
              title="Change"
            >
              <Pencil size={16} />
            </button>
            {/* Download */}
            <button
              type="button"
              onClick={onDownload}
              className="h-8 w-8 rounded-md bg-white/90 hover:bg-white shadow flex items-center justify-center"
              aria-label="Download"
              title="Download"
            >
              <Download size={16} />
            </button>
          </div>
        </div>
      )}

      {/* INPUT asli (sr-only) — tetap ada di DOM untuk integrasi RHF */}
      <BaseInput
        id={id as string}
        placeholder={placeholder}
        type="file"
        accept="image/png"
        className="sr-only"
        ref={(el: any) => {
          inputRef.current = el;
          // forward ke RHF register supaya tetap terdaftar
          const r = form.register(id as any);
          if (typeof r.ref === "function") r.ref(el);
          else if (r.ref) (r.ref as any)(el);
        }}
        onChange={onPickFile}
        onBlur={() => form.trigger(id)}
      />

      {err && <p className="text-xs text-red-600 mt-1">{err}</p>}
    </div>
  );
};

const InputString = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: Path<ICreateUserForm>;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateUserForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
      />
    </div>
  );
};

const InputNumber = ({
  label,
  id,
  placeholder,
  optional = false,
}: {
  label: string;
  id: keyof ICreateUserForm;
  placeholder?: string;
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateUserForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>
      <BaseInput
        id={id}
        placeholder={placeholder}
        {...form.register(id)}
        className="h-11"
        onKeyDown={(e) => {
          if (isNumberInput(e)) e.preventDefault();
        }}
        type="string"
      />
    </div>
  );
};

const InputSelect = ({
  label,
  id,
  placeholder,
  options,
  optional = false,
}: {
  label: string;
  id: Path<ICreateUserForm>;
  placeholder?: string;
  options: { value: string; label: string }[];
  optional?: boolean;
}) => {
  const form = useFormContext<ICreateUserForm>();

  return (
    <div className="flex flex-col gap-1">
      <BaseLabel className="text-sm" htmlFor={id}>
        {label}
        {optional && <span className="text-xs text-gray-500">(optional)</span>}
      </BaseLabel>

      <Controller
        name={id as any}
        control={form.control}
        render={({ field }) => {
          return (
            <BasePopover>
              <BasePopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className="w-full min-h-11 justify-between"
                >
                  {field.value != null
                    ? options.find((o) => o.value === String(field.value))
                        ?.label ?? placeholder
                    : placeholder}

                  <ChevronsUpDown className="opacity-50" />
                </BaseButton>
              </BasePopoverTrigger>
              <BasePopoverContent className="w-full p-0">
                <BaseCommand>
                  <BaseCommandInput
                    placeholder="Search framework..."
                    className="h-9"
                  />
                  <BaseCommandList>
                    <BaseCommandEmpty>No framework found.</BaseCommandEmpty>
                    <BaseCommandGroup>
                      {options.map((option) => (
                        <BaseCommandItem
                          key={`${id}-${option.value}`}
                          value={`${option.label}__${option.value}`}
                          onSelect={(currentValue) => {
                            field.onChange(currentValue.split("__")[1]);
                          }}
                        >
                          {option.label}
                          <Check
                            className={cn(
                              "ml-auto",
                              field.value === option.value
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </BaseCommandItem>
                      ))}
                    </BaseCommandGroup>
                  </BaseCommandList>
                </BaseCommand>
              </BasePopoverContent>
            </BasePopover>
          );
        }}
      />
    </div>
  );
};

export default UserManagementNewUserModal;
