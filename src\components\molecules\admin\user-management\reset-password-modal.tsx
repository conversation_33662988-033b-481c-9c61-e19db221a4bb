import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { KeyRound } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";

const UserManagementResetPasswordModal = () => {
  const { openResetPassword, setOpenResetPassword } =
    useUserManagementModalStore(
      useShallow(({ openResetPassword, setOpenResetPassword }) => ({
        openResetPassword,
        setOpenResetPassword,
      }))
    );

  return (
    <BaseDialog open={openResetPassword} onOpenChange={setOpenResetPassword}>
      <BaseDialogContent className="h-fit min-w-2/5" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-orange-200 w-fit p-2 rounded-full border-8 border-orange-100 bg">
            <KeyRound className="text-orange-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Reset Password{" "}
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Select “First Email” to send the <NAME_EMAIL> or
            select “Second Email” to send the Password to
            <EMAIL>
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Second Email
            </BaseButton>
          </DialogClose>
          <DialogClose asChild>
            <BaseButton className="w-34 h-11">First Email</BaseButton>
          </DialogClose>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default UserManagementResetPasswordModal;
