import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { Search } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import { useDebouncedCallback } from "use-debounce";

const UserManagementTableHeaderSearch = () => {
  const { query, setQuery } = useUserManagementFilterStore(
    useShallow(({ setQuery, query }) => ({ setQuery, query }))
  );

  const handleSearch = useDebouncedCallback((value: string) => {
    setQuery({ ...query, search: value ?? undefined, page: 1 });
  }, 500);

  const handleSearchBy = useDebouncedCallback(
    (
      value:
        | "npk"
        | "name"
        | "job_name"
        | "email"
        | "second_email"
        | "phone_number"
        | "updated_by"
    ) => {
      setQuery({ ...query, search_by: value ?? undefined, page: 1 });
    },
    500
  );

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <div>
        <BaseSelect onValueChange={handleSearchBy}>
          <BaseSelectTrigger className="w-32 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            <BaseSelectItem value="npk">NPK</BaseSelectItem>
            <BaseSelectItem value="name">Name</BaseSelectItem>
            <BaseSelectItem value="job_name">Job Name</BaseSelectItem>
            <BaseSelectItem value="email">Email</BaseSelectItem>
            <BaseSelectItem value="second_email">Second Email</BaseSelectItem>
            <BaseSelectItem value="phone_number">Phone Number</BaseSelectItem>
            <BaseSelectItem value="updated_by">Updated By</BaseSelectItem>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        size={24}
        onChange={(e) => handleSearch(e.target.value)}
      />
      <Search />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default UserManagementTableHeaderSearch;
