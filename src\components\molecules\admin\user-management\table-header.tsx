import React from "react";
import UserManagementTableHeaderSearch from "./search";
import UserManagementTableHeaderFilter from "./filter";
import UserManagementFilterInput from "./filter-input";

const UserManagementTableHeader = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <UserManagementTableHeaderSearch />
        <UserManagementTableHeaderFilter />
      </div>
      <UserManagementFilterInput />
    </div>
  );
};

export default UserManagementTableHeader;
