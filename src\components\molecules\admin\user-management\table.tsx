"use client";

import React from "react";
import UserManagementTableHeader from "./table-header";
import { DataTable } from "../../global/table";
import { ColumnDef } from "@tanstack/react-table";
import { IGetUserListResponse } from "@/interfaces/admin/user-management/list";

import { BaseSwitch } from "@/components/atoms/switch";
import { Check, Pencil, Trash2 } from "lucide-react";
import { cn } from "@/utils/common";
import { BaseButton } from "@/components/atoms/button";
import { useGetUserListQuery } from "@/services/query/user-management/list";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useShallow } from "zustand/react/shallow";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { useSetUserActiveMutation } from "@/services/mutation/user-management/set-active";
import toast from "react-hot-toast";
import UserManagementDeleteConfirmationModal from "./delete-user-confirmation-modal";

const UserManagementTable = () => {
  const columns: ColumnDef<IGetUserListResponse>[] = [
    {
      accessorKey: "npk",
      header: "NPK",
      cell({ row }) {
        return (
          <span className="text-sm font-medium leading-none text-muted-foreground">
            {row.original.npk}
          </span>
        );
      },
    },
    { accessorKey: "name", header: "Name" },
    { accessorKey: "job_name", header: "Job Position" },
    { accessorKey: "email", header: "Primary Email" },
    { accessorKey: "second_email", header: "Secondary Email" },
    { accessorKey: "phone_number", header: "Phone" },
    { accessorKey: "user_type_name", header: "User Type" },
    { accessorKey: "user_role_name", header: "User Role" },
    { accessorKey: "entity_name", header: "Entity" },
    {
      accessorKey: "is_need_neop",
      header: "Neop",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_neop
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    {
      accessorKey: "is_need_welcoming_kit",
      header: "Welcoming Kit",
      cell({ row }) {
        return (
          <Check
            size={24}
            className={cn(
              row.original.is_need_welcoming_kit
                ? "bg-orange-400 text-white"
                : "bg-gray-400 text-white",
              "rounded-full p-1"
            )}
            strokeWidth={3}
            absoluteStrokeWidth
          />
        );
      },
    },
    { accessorKey: "last_updated", header: "Last Updated" },
    { accessorKey: "updated_by", header: "Updated By" },
    {
      accessorKey: "is_active",
      header: "Status",
      cell({ row }) {
        return (
          <BaseSwitch
            checked={row.original.is_active ?? false}
            onCheckedChange={(value) => {
              updateUserActive.mutate(
                {
                  id: row.original.id as number,
                  value,
                },
                {
                  onSuccess: (data) => {
                    toast.success(data.message);
                    userList.refetch();
                  },
                  onError: (data) => {
                    toast.error(data.message);
                  },
                }
              );
            }}
          />
        );
      },
    },
    {
      accessorKey: "id",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-start gap-2">
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => {
                setCurrentData(row.original.id);
                setOpenAddUser(true);
              }}
            >
              <Pencil
                size={24}
                className="fill-gray-700 text-white"
                strokeWidth={1}
              />
            </BaseButton>
            <BaseButton
              variant={"outline"}
              className="border-none"
              onClick={() => {
                setCurrentData(row.original.id);
                setOpenDeleteUser(true);
              }}
            >
              <Trash2 size={24} className="text-gray-700" strokeWidth={2.5} />
            </BaseButton>
          </div>
        );
      },
    },
  ];

  const { query, setQuery } = useUserManagementFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const { setOpenAddUser, setCurrentData, setOpenDeleteUser } =
    useUserManagementModalStore(
      useShallow(({ setOpenAddUser, setCurrentData, setOpenDeleteUser }) => ({
        setOpenAddUser,
        setCurrentData,
        setOpenDeleteUser,
      }))
    );

  const userList = useGetUserListQuery(query);
  const updateUserActive = useSetUserActiveMutation();

  const handlePageChange = (page: number) => {
    setQuery({ ...query, page });
  };

  return (
    <div className="flex flex-col gap-4">
      <UserManagementTableHeader />
      <DataTable
        columns={columns}
        data={userList.data?.data ?? []}
        pagination={userList.data?.pagination}
        onPageChange={handlePageChange}
      />
      <UserManagementDeleteConfirmationModal />
    </div>
  );
};

export default UserManagementTable;
