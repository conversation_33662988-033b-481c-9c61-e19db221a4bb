"use client";

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
} from "@/components/atoms/tabs";
import { useGetUserListQuery } from "@/services/query/user-management/list";
import { useUserManagementFilterStore } from "@/store/admin/user-management/filter";
import { useUserManagementTabStore } from "@/store/admin/user-management/tab";
import { useShallow } from "zustand/react/shallow";

const UserManagementTitle = () => {
  const { activeTab, setActiveTab } = useUserManagementTabStore();
  const { query, setQuery } = useUserManagementFilterStore(
    useShallow(({ query, setQuery }) => ({ query, setQuery }))
  );

  const list = useGetUserListQuery(query);

  const handleChange = (value: string) => {
    setQuery({ ...query, page: 1, is_new_user: value === "new" });
  };

  return (
    <div className="flex justify-between gap-4">
      <div className="bg-white text-[#3C3C3C] w-full p-3 font-semibold rounded-lg">
        User Management
      </div>
      <div className="text-[#3C3C3C] w-full flex items-center gap-2">
        <BaseTabs
          defaultValue={activeTab}
          onValueChange={(val) => {
            setActiveTab(val as "regular" | "new");
            handleChange(val);
          }}
        >
          <BaseTabsList className="w-full h-12">
            <BaseTabsTrigger value="regular">Regular User</BaseTabsTrigger>
            <BaseTabsTrigger value="new">
              New User{" "}
              {activeTab === "new" && !list.isLoading
                ? `(${list.data?.pagination?.total_data ?? 0})`
                : ""}
            </BaseTabsTrigger>
          </BaseTabsList>
        </BaseTabs>
      </div>
    </div>
  );
};

export default UserManagementTitle;
