import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { Check } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";

const UserManagementVerifyConfirmationModal = () => {
  const { openVerifyUserConfirmation, setOpenVerifyUserConfirmation } =
    useUserManagementModalStore(
      useShallow(
        ({ openVerifyUserConfirmation, setOpenVerifyUserConfirmation }) => ({
          openVerifyUserConfirmation,
          setOpenVerifyUserConfirmation,
        })
      )
    );

  return (
    <BaseDialog
      open={openVerifyUserConfirmation}
      onOpenChange={setOpenVerifyUserConfirmation}
    >
      <BaseDialogContent className="h-fit" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-orange-200 w-fit p-2 rounded-full border-8 border-orange-100 bg">
            <Check className="text-orange-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Verify User Account
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            You have selected 12 user account to verify. Are you sure you want
            to verify these 12 user accounts?
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant={"outline"}>
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton className="w-34 h-11">Verify User</BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default UserManagementVerifyConfirmationModal;
