import { <PERSON><PERSON><PERSON>t, BaseAlertDescription } from "@/components/atoms/alert";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseSeparator } from "@/components/atoms/separator";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { Download, Info } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import { DataTable } from "../../global/table";
import { ColumnDef } from "@tanstack/react-table";
import { IProgressHistory } from "@/interfaces/admin/user-management/list";
import dayjs from "dayjs";
import { BaseButton } from "@/components/atoms/button";

export const exampleProgressHistory: IProgressHistory[] = [
  {
    filename: "example.xlsx",
    status: "success",
    success: 10,
    failed: 0,
    uploaded_at: dayjs().format("DD MMM YYYY HH:mm"),
    uploaded_by: "AdminUser",
    failed_log: null,
  },
  {
    filename: "failed.xlsx",
    status: "failed",
    success: 0,
    failed: 5,
    uploaded_at: dayjs().subtract(1, "day").format("DD MMM YYYY HH:mm"),
    uploaded_by: "EditorUser",
    failed_log: "Error: invalid data",
  },
  {
    filename: "processing.xlsx",
    status: "processing",
    success: null,
    failed: null,
    uploaded_at: dayjs().subtract(2, "day").format("DD MMM YYYY HH:mm"),
    uploaded_by: "AdminUser",
    failed_log: null,
  },
];

const columns: ColumnDef<IProgressHistory>[] = [
  {
    accessorKey: "filename",
    header: "Filename",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "success",
    header: "Success",
    cell(props) {
      return <span>{props.row.original.success ?? "-"}</span>;
    },
  },
  {
    accessorKey: "failed",
    header: "Failed",
    cell(props) {
      return <span>{props.row.original.failed ?? "-"}</span>;
    },
  },
  {
    accessorKey: "uploaded_at",
    header: "Uploaded At",
  },
  {
    accessorKey: "uploaded_by",
    header: "Uploaded By",
  },
  {
    accessorKey: "failed_log",
    header: "Failed Log",
    cell(props) {
      const value = props.row.original.failed_log;

      if (!value) return "";

      return (
        <BaseButton className="w-10 h-10" variant={"ghost"}>
          <Download strokeWidth={3} />
        </BaseButton>
      );
    },
  },
];

const UserManagementUploadHistoryModal = () => {
  const { openUploadHistory, setOpenUploadHistory } =
    useUserManagementModalStore(
      useShallow(({ openUploadHistory, setOpenUploadHistory }) => ({
        openUploadHistory,
        setOpenUploadHistory,
      }))
    );

  return (
    <BaseDialog open={openUploadHistory} onOpenChange={setOpenUploadHistory}>
      <BaseDialogContent className="h-fit min-w-fit" showCloseButton={true}>
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4">
            <span>Upload Progress and History</span>
            <BaseSeparator />
          </BaseDialogTitle>
          <BaseAlert className="py-4 !border-1 border-blue-800 bg-gray-200 my-3">
            <Info
              strokeWidth={3}
              absoluteStrokeWidth
              className="!text-blue-800"
            />
            <BaseAlertDescription className="text-gray-500 text-sm">
              Please remove the image background first before uploading the
              image
            </BaseAlertDescription>
          </BaseAlert>
        </BaseDialogHeader>
        <DataTable columns={columns} data={exampleProgressHistory} />
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default UserManagementUploadHistoryModal;
