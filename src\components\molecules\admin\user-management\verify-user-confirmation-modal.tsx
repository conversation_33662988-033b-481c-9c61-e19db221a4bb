import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { useUserManagementModalStore } from "@/store/admin/user-management/modal";
import { Upload } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";

const UserManagementUploadExcelConfirmationModal = () => {
  const { openUploadExcelConfirmation, setOpenUploadExcelConfirmation } =
    useUserManagementModalStore(
      useShallow(
        ({ openUploadExcelConfirmation, setOpenUploadExcelConfirmation }) => ({
          openUploadExcelConfirmation,
          setOpenUploadExcelConfirmation,
        })
      )
    );

  return (
    <BaseDialog
      open={openUploadExcelConfirmation}
      onOpenChange={setOpenUploadExcelConfirmation}
    >
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div className="bg-orange-200 w-fit p-2 rounded-full border-8 border-orange-100 bg">
            <Upload className="text-orange-400" size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            Upload is Being Processed
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            Your file is being uploaded. Click the “Upload History” button to
            check the result
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11">Close</BaseButton>
          </DialogClose>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default UserManagementUploadExcelConfirmationModal;
