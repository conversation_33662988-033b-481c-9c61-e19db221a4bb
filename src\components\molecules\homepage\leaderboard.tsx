import {
  BaseAvatar,
  BaseAvatarFallback,
  BaseAvatarImage,
} from "@/components/atoms/avatar";
import { BaseButton } from "@/components/atoms/button";
import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

const HomePageLeaderBoard = () => {
  return (
    <div className="flex flex-col gap-2">
      <Header />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 max-w-dvw">
        <LeaderBoard />
        <LeaderBoard />
        <LeaderBoard />
      </div>
    </div>
  );
};

const Header = () => {
  return (
    <div className="flex justify-between items-center">
      <span className="font-semibold">Top Scorer Leaderboard Bulan Ini</span>
      <div className="flex justify-end gap-2">
        <BaseButton variant="outline" className="h-11">
          Se<PERSON><PERSON>
        </BaseButton>
      </div>
    </div>
  );
};

const LeaderBoard = () => {
  return (
    <div className="bg-white border border-gray-200 rounded-md w-full overflow-hidden shadow-sm p-3">
      <div className="w-full bg-[#FED906] h-20 flex items-center justify-center rounded-md">
        <div className="flex flex-col items-center justify-center ">
          <span className="font-light text-[#996F00] text-xs">
            Peringkat Teratas Pengguna
          </span>
          <span className="font-semibold text-[#996F00] text-md">
            Durasi Belajar Terbaik
          </span>
        </div>
      </div>
      <div className="grid grid-cols-3 place-items-center items-end mt-2 gap-3 h-64">
        {[2, 1, 3].map((item) => (
          <div
            className="flex flex-col gap-2 items-center w-full h-full"
            key={`leaderboard-${item}`}
          >
            <div className="mt-auto" />

            <BaseAvatar className="w-12 h-12">
              <BaseAvatarImage src="https://github.com/shadcn.png" />
              <BaseAvatarFallback>A</BaseAvatarFallback>
            </BaseAvatar>

            <span className="font-semibold">John Doe</span>
            <span>
              <span className="text-[#F7941E] font-semibold">88.8 </span>Jam
            </span>

            <div
              className={cn(
                "mt-3 bg-[#FEF9E9] border border-[#FCE5A6] flex justify-center items-center rounded-t-lg w-full font-semibold text-xl",
                `h-${30 / item}`
              )}
            >
              {item}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-5 flex flex-col gap-2">
        <span>Peringkat anda</span>
        <div className="bg-white border border-gray-200 rounded-md flex gap-3 justify-between items-center py-3 px-8">
          <div>
            <Image src="/icons/badge.svg" alt="badge" width={30} height={30} />
          </div>
          <div className="flex gap-2 items-center text-sm font-semibold grow">
            <BaseAvatar className="w-8 h-8">
              <BaseAvatarImage src="https://github.com/shadcn.png" />
              <BaseAvatarFallback>A</BaseAvatarFallback>
            </BaseAvatar>
            <span>John Doe</span>
          </div>
          <div>
            <span>
              <span className="text-[#F7941E] font-semibold">88.8 </span>Jam
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePageLeaderBoard;
