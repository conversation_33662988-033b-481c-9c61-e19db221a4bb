import { BaseBadge } from "@/components/atoms/badge";
import {
  <PERSON><PERSON>ef<PERSON>,
  ArrowR<PERSON>,
  ChevronDown,
  Clock,
  List,
  Star,
} from "lucide-react";
import Image from "next/image";
import React from "react";

const HomepageMyLearning = () => {
  return (
    <div className="flex flex-col gap-2">
      <Header />
      <div className="flex gap-4 min-h-fit max-w-dvw overflow-x-auto">
        <Item />
        <Item />
        <Item />
        <Item />
      </div>
    </div>
  );
};

const Header = () => {
  return (
    <div className="flex justify-between items-center">
      <span className="font-semibold">Lanjutkan Pembelajaran Kamu</span>
      <div className="hidden md:flex justify-end gap-2">
        <div className="p-1 rounded-full border border-gray-300 hover:bg-gray-100 cursor-pointer">
          <ArrowLeft size={16} strokeWidth={3} className="text-gray-600" />
        </div>
        <div className="p-1 rounded-full border border-gray-300 hover:bg-gray-100 cursor-pointer">
          <ArrowRight size={16} strokeWidth={3} className="text-gray-600" />
        </div>
      </div>
    </div>
  );
};

const Item = () => {
  return (
    <div className="bg-white border border-gray-200 rounded-md w-full overflow-hidden shadow-sm min-w-72">
      <div className="relative w-full h-44">
        <Image
          src={"/images/image.png"}
          alt="image"
          fill
          className="object-cover"
        />
        <div className="absolute top-0 left-0 w-full h-full text-white">
          <div className="flex justify-end mt-3 mr-3">
            <BaseBadge
              variant={"secondary"}
              className="text-red-500 border-white"
            >
              High Priority
            </BaseBadge>
          </div>
        </div>
      </div>
      <div className="p-4 flex flex-col gap-5">
        <div className="flex gap-3">
          <BaseBadge className="rounded-sm text-[#FB9223] bg-[#FFF4E9]">
            Core Module
          </BaseBadge>
          <BaseBadge className="rounded-sm text-[#2C598D] bg-[#EAEEF4]">
            Level 2
          </BaseBadge>
          <BaseBadge
            variant={"secondary"}
            className="rounded-sm flex justify-between gap-2"
          >
            <span>5 Tags</span>
            <span>
              <ChevronDown size={16} />
            </span>
          </BaseBadge>
        </div>
        <span className="font-semibold text-xl">
          The Basic of User Interface Design
        </span>
        <div className="flex gap-4 text-gray-600 mt-0.5 -mb-2">
          <div className="flex gap-2 items-center">
            <List size={16} />
            <span>8 Section</span>
          </div>
          <div className="flex gap-2 items-center">
            <Clock size={16} />
            <span>3 Jam 19 Menit</span>
          </div>
        </div>
        <Rating />
        <div className="flex flex-col gap-1 mt-4">
          <span className="text-gray-600 text-sm">Progress Belajar</span>
          <div className="w-full h-6 bg-gray-200 rounded-full">
            <div className="w-1/2 h-full bg-[#FB9223] rounded-full flex justify-end items-center text-white pr-2 text-sm">
              50%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const Rating = ({ value = 4.5, max = 5 }) => {
  return (
    <div className="flex gap-2">
      <span className="text-[#F7941E] font-semibold text-lg">4.5</span>
      <div className="flex items-center gap-1">
        {Array.from({ length: max }).map((_, i) => {
          const starValue = i + 1;
          const isFull = starValue <= Math.floor(value);
          const isHalf = value < starValue && value > starValue - 1;

          return (
            <div key={`star-${i + 1}`} className="relative">
              {/* full star (yellow) */}
              {isFull && (
                <Star
                  size={22}
                  className="fill-yellow-400 text-yellow-400 cursor-pointer"
                />
              )}

              {/* half star */}
              {isHalf && (
                <div className="relative w-6 h-6 cursor-pointer mt-0.5">
                  <Star size={22} className="text-gray-300 absolute" />
                  <div className="w-1/2 overflow-hidden absolute top-0 left-0">
                    <Star
                      size={22}
                      className="fill-yellow-400 text-yellow-400"
                    />
                  </div>
                </div>
              )}

              {/* empty star */}
              {!isFull && !isHalf && (
                <Star size={22} className="text-gray-300 cursor-pointer" />
              )}
            </div>
          );
        })}
      </div>
      <span className="text-gray-400 text-lg">(1278)</span>
    </div>
  );
};

export default HomepageMyLearning;
