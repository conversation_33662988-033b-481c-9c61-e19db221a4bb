import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import { Search } from "lucide-react";
import Image from "next/image";
import React from "react";

const HomepageSearch = () => {
  return (
    <div className="flex flex-col items-center bg-[#FEF9EE] rounded-md gap-7 max-w-dvw">
      <Image
        src={"/icons/search.svg"}
        alt="search"
        className="mt-5 w-72"
        width={100}
        height={100}
      />
      <span className="text-[#D57400] font-semibold text-2xl">Cari Module</span>
      <div className="w-full lg:w-[44rem] px-4">
        <div className="relative">
          <BaseInput
            className="w-full pl-4 pr-12 py-6.5"
            placeholder="Masukkan hal yang ingin dicari. Contoh “Basic Training”"
          />
          <Search className="absolute top-5 right-4 text-gray-500" size={16} />
        </div>
      </div>
      <div className="lg:w-[22rem] w-full px-4">
        <BaseButton size={"lg"} className="mt-2 h-12 mb-5 w-full">
          Cari Module
        </BaseButton>
      </div>
    </div>
  );
};

export default HomepageSearch;
