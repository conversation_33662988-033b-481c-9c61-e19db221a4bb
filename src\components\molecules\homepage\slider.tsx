import {
  BaseCarousel,
  BaseCarouselContent,
  BaseCarouselItem,
} from "@/components/atoms/carousel";
import Image from "next/image";

const HomepageSlider = () => {
  return (
    <BaseCarousel className="rounded-xl overflow-hidden">
      <BaseCarouselContent className="h-40">
        <BaseCarouselItem className="relative h-full basis-full">
          <Image
            alt="slider3"
            src="/images/slider-example.png"
            fill
            className="object-cover"
          />
        </BaseCarouselItem>
        <BaseCarouselItem className="relative h-full basis-full">
          <Image
            alt="slider1"
            src="/images/slider-example.png"
            fill
            className="object-cover"
          />
        </BaseCarouselItem>
      </BaseCarouselContent>
    </BaseCarousel>
  );
};

export default HomepageSlider;
