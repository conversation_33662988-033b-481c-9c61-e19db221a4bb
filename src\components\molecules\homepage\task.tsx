import { BaseBadge } from "@/components/atoms/badge";
import { BaseButton } from "@/components/atoms/button";
import { ArrowLeft, ArrowRight, BookOpenText } from "lucide-react";
import React from "react";

const HomePageTask = () => {
  return (
    <div className="flex flex-col gap-2">
      <Header />
      <div className="flex  gap-4 min-h-fit max-w-dvw overflow-x-auto">
        <Task />
        <Task />
        <Task />
      </div>
    </div>
  );
};

const Header = () => {
  return (
    <div className="flex justify-between items-center">
      <span className="font-semibold">Ayo Selesaikan Tugas Kamu!</span>
      <div className="hidden md:flex justify-end gap-2">
        <div className="p-1 rounded-full border border-gray-300 hover:bg-gray-100 cursor-pointer">
          <ArrowLeft size={16} strokeWidth={3} className="text-gray-600" />
        </div>
        <div className="p-1 rounded-full border border-gray-300 hover:bg-gray-100 cursor-pointer">
          <ArrowRight size={16} strokeWidth={3} className="text-gray-600" />
        </div>
      </div>
    </div>
  );
};

const Task = () => {
  const items = [
    "MBA in a Box: Business Lesson from CEO",
    "Business Strategy from a Consultant",
    "Business Strategy from a Consultant",
    "+15 more tasks",
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg min-w-72 overflow-hidden shadow-sm p-1 ">
      {/* Header */}
      <div className="relative bg-green-600  rounded-lg">
        <div className="relative z-10 h-full px-4 py-3 grid grid-cols-6 items-center gap-2 min-h-36">
          {/* icon */}
          <div className="col-span-1 flex items-start justify-end mr-2 mb-8">
            <div className="p-2.5 rounded-full bg-white shadow">
              <BookOpenText
                className="text-green-600"
                size={18}
                absoluteStrokeWidth
              />
            </div>
          </div>

          {/* title + badges */}
          <div className="col-span-5 flex flex-col justify-start">
            <div className="text-white font-semibold text-[20px]">
              Strategic Vision and Business Acumen
            </div>
            <div className="mt-2 flex items-center gap-2 flex-wrap">
              <BaseBadge className="rounded-md bg-white/20 text-white hover:bg-white/30 border border-white/30">
                HC Recommendation
              </BaseBadge>
              <BaseBadge className="rounded-md bg-white/20 text-white hover:bg-white/30 border border-white/30">
                LEMON&apos;s Choice
              </BaseBadge>
            </div>
          </div>
        </div>
      </div>

      {/* Body: timeline */}
      <div className="px-4 py-3">
        <ul className="space-y-3">
          {items.map((text, i) => (
            <li key={`task-item-${i + 1}`} className="relative pl-6">
              {/* bullet */}
              <span className="absolute left-0 top-1.5 h-3 w-3 rounded-full border border-gray-300 bg-white" />

              {/* garis ke item berikutnya (menjembatani gap) */}
              {i !== items.length - 1 && (
                <span className="absolute left-[6px] top-4 -bottom-6 w-px bg-gray-300" />
              )}

              {/* label */}
              <span className="text-sm text-gray-800 font-semibold">
                {text}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 px-2 py-3 flex justify-end w-full items-center">
        <BaseButton type="button" variant={"outline"} size={"lg"}>
          Lihat Tugas
        </BaseButton>
      </div>
    </div>
  );
};

export default HomePageTask;
