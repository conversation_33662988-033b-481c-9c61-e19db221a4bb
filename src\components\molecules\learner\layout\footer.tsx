'use client';

import { IconEnglishUS } from '@/assets/icons/IconEnglishUS';
import { IconIndonesia } from '@/assets/icons/IconIndonesia';
import React, { useCallback } from 'react';
import { useRouter } from 'next/navigation';

type MenuItem = {
  title: string;
  url: string;
};

const items: MenuItem[] = [
  { title: 'Homepage', url: '/homepage' },
  { title: 'Online Learning', url: '/online-learning' },
  { title: 'Calendar Training', url: '/calendar-training' },
  { title: 'InClass Training', url: '/inclass-training' },
  { title: 'ACCPedia', url: '/knowledge-center/accpedia' },
  { title: 'ACC Guava', url: '/knowledge-center/acc-guava' },
  { title: 'Forum', url: '/knowledge-center/forum' },
  { title: 'FAQ', url: '/knowledge-center/faq' },
  { title: 'About Lemon', url: '#' },
  { title: 'Contact Us', url: '#' },
  { title: 'Help and Support', url: '#' },
];

const LearnerFooter = () => {
  const router = useRouter();
  const handleNavigation = useCallback(
    (url: string) => router.push(url),
    [router]
  );

  return (
    <footer className="px-4 py-6 md:px-8 lg:px-16 md:py-4 bg-[#F5F6FA] md:bg-white border-t md:border border-[#DEDEDE] md:rounded-xl md:mt-[79px]">
      <div className="hidden md:flex md:flex-col lg:flex-row justify-between items-center">
        <div className="flex flex-row items-center gap-[30px]">
          <h5 className="text-[#F7941E] font-bold text-xl">LEMON</h5>
          <p className="text-sm text-[#B1B1B1]">
            Copyright 2019 Astra Credit Companies
          </p>
        </div>
        <div className="flex flex-row items-center gap-8">
          <div className="flex flex-row gap-2">
            <IconEnglishUS />
            <p className="text-xs text-[#3C3C3C]">English (US)</p>
          </div>
          <div className="flex flex-row gap-2">
            <IconIndonesia />
            <p className="text-xs text-[#3C3C3C]">Bahasa</p>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-2 md:hidden">
        {items.map((item) => (
          <button
            key={item.title}
            type="button"
            onClick={() => handleNavigation(item.url)}
            className="text-left text-xs text-[#8DA2B7] hover:underline focus:underline focus:outline-none cursor-pointer"
            aria-label={`Pergi ke ${item.title}`}
          >
            {item.title}
          </button>
        ))}

        <div className="flex flex-row items-center justify-between mt-8">
          <p
            className="text-2xl font-black text-[#8DA2B7]"
            style={{ fontFamily: 'var(--font-dm-sans)' }}
          >
            ACC LEMON
          </p>
          <p className="text-[10px] text-[#8DA2B7]">
            Copyright 2025 Astra Credit Companies
          </p>
        </div>
      </div>
    </footer>
  );
};

export default LearnerFooter;
