'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';

import LogoSmall from '@/assets/images/logo-small.png';
import VectorOne from '@/assets/images/vector-one.png';
import VectorTwo from '@/assets/images/vector-two.png';
import VectorThree from '@/assets/images/vector-three.png';
import VectorFour from '@/assets/images/vector-four.png';

import { BaseInput } from '@/components/atoms/input';
import { IconSearchBox } from '@/assets/icons/IconSearchBox';
import { IconBell } from '@/assets/icons/IconBell';
import { IconHamburger } from '@/assets/icons/IconHamburger';

import {
  BaseAvatar,
  BaseAvatarFallback,
  BaseAvatarImage,
} from '@/components/atoms/avatar';
import ProfileMenuDialog from '../../account-settings/profile-menu-dialog';
import ListMenuDialog from '../../account-settings/list-menu-dialog';

const dummyUsers = {
  name: '<PERSON>',
  role: 'UI/UX Designer',
};

const LearnerNavbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const mql = window.matchMedia('(min-width: 768px)');
    const handler = (e: MediaQueryListEvent) => {
      if (e.matches) setMobileNavOpen(false);
    };
    mql.addEventListener('change', handler);
    return () => mql.removeEventListener('change', handler);
  }, []);

  return (
    <div className="relative bg-white w-full p-4 md:p-6 border-b border-[#DEDEDE] md:border-none">
      <div className="relative md:bg-[#FFEFAE] md:rounded-2xl md:shadow-[0px_4px_24px_0px_rgba(255,191,0,0.12)] md:border md:border-[#FFE764] w-full flex justify-between items-center overflow-hidden">
        <Image
          src={VectorOne}
          alt="vector One"
          width={440}
          height={328}
          className="hidden md:block pointer-events-none absolute top-0 left-[52px]"
        />
        <Image
          src={VectorTwo}
          alt="vector Two"
          width={637}
          height={306}
          className="hidden md:block pointer-events-none absolute top-0 left-[248px]"
        />
        <Image
          src={VectorThree}
          alt="vector Three"
          width={726}
          height={77}
          className="hidden md:block pointer-events-none absolute top-0 left-[326px]"
        />
        <Image
          src={VectorFour}
          alt="vector Four"
          width={672}
          height={131}
          className="hidden md:block pointer-events-none absolute -top-0 -right-[66px]"
        />
        <div className="flex items-center flex-1 z-10">
          <div className="hidden md:flex items-center gap-[126px] py-4 pl-14 w-full">
            <Image
              alt="logo"
              src={LogoSmall}
              width={86}
              height={36}
            />
            <div className="w-full max-w-[424px]">
              <BaseInput
                id="search"
                type="text"
                placeholder="Masukkan hal yang ingin dicari. Contoh “Basic Training”"
                className="w-full h-11 text-sm px-4 py-[13px] border-none"
                rightIcon={<IconSearchBox />}
              />
            </div>
          </div>

          <button
            className="cursor-pointer md:hidden"
            onClick={() => setMobileNavOpen((v) => !v)}
          >
            <IconHamburger
              size={28}
              color="#3C3C3C"
            />
          </button>
        </div>
        <div className="flex items-center gap-3 md:px-4 z-10">
          <div className="hidden md:flex items-center gap-3">
            <button
              onClick={() => {}}
              className="cursor-pointer"
            >
              <IconBell
                size={36}
                color="#FFFFFF"
              />
            </button>

            <button
              className="bg-white rounded-xl flex flex-row items-center gap-[10px] p-[6px] pr-6 cursor-pointer"
              type="button"
              onClick={() => setMenuOpen(true)}
              aria-haspopup="dialog"
              aria-expanded={menuOpen}
              aria-controls="profile-menu-dialog"
            >
              <BaseAvatar className="w-10 h-10">
                <BaseAvatarImage src="https://github.com/shadcn.png" />
                <BaseAvatarFallback>KJ</BaseAvatarFallback>
              </BaseAvatar>
              <div className="flex flex-col gap-1 text-[#3C3C3C] max-w-[120px]">
                <span className="text-sm truncate">{dummyUsers.name}</span>
                <span className="text-sm truncate">{dummyUsers.role}</span>
              </div>
            </button>
          </div>

          <div className="flex items-center gap-3 md:hidden">
            <button
              onClick={() => {}}
              className="cursor-pointer"
            >
              <IconBell
                size={24}
                color="#343330"
              />
            </button>

            <BaseAvatar
              className="w-10 h-10 cursor-pointer"
              onClick={() => setMenuOpen(!menuOpen)}
            >
              <BaseAvatarImage src="https://github.com/shadcn.png" />
              <BaseAvatarFallback>KJ</BaseAvatarFallback>
            </BaseAvatar>
          </div>
        </div>
      </div>

      <ProfileMenuDialog
        open={menuOpen}
        onClose={() => setMenuOpen(false)}
        user={{
          name: dummyUsers.name,
          role: dummyUsers.role,
          avatarUrl: 'https://github.com/shadcn.png',
        }}
        className="absolute h-fit z-[60]"
      />

      <ListMenuDialog
        open={mobileNavOpen}
        onClose={() => setMobileNavOpen(false)}
        className="absolute z-[60]"
      />
    </div>
  );
};

export default LearnerNavbar;
