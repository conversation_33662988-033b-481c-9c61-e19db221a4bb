'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { ChevronRight, ChevronDown } from 'lucide-react';

import { IconBookmark } from '@/assets/icons/IconBookmark';
import { IconBookOpenText } from '@/assets/icons/IconBookOpenText';
import { IconBrain } from '@/assets/icons/IconBrain';
import { IconCalendar } from '@/assets/icons/IconCalendar';
import { IconChalkBoardTeacher } from '@/assets/icons/IconChalkBoardTeacher';
import { IconChats } from '@/assets/icons/IconChats';
import { IconHouse } from '@/assets/icons/IconHouse';
import { IconLightBulp } from '@/assets/icons/IconLightBulp';
import { IconPath } from '@/assets/icons/IconPath';
import { IconRanking } from '@/assets/icons/IconRanking';
import { IconSealQuestion } from '@/assets/icons/IconSealQuestion';
import { IconSignout } from '@/assets/icons/IconSignout';
import { IconTextAa } from '@/assets/icons/IconTextAa';
import { cn } from '@/lib/utils';

import {
  BaseSidebar,
  BaseSidebarContent,
  BaseSidebarGroup,
  BaseSidebarGroupContent,
  BaseSidebarMenu,
  BaseSidebarMenuButton,
  BaseSidebarMenuItem,
} from '@/components/atoms/sidebar';
import { useLogoutUserMutation } from '@/services/mutation/login/user';
import { notifyHotSuccess } from '../../toast/hot-toast';
import { clearSession } from '@/services/session/session';

type MenuItem = {
  title: string;
  url: string;
  icon: React.FC<{ size?: number; color?: string }>;
  children?: MenuItem[];
};

const items: MenuItem[] = [
  { title: 'Homepage', url: '/homepage', icon: IconHouse },
  { title: 'Online Learning', url: '/online-learning', icon: IconLightBulp },
  {
    title: 'In-Class Training',
    url: '/in-class-training',
    icon: IconChalkBoardTeacher,
  },
  { title: 'Calendar Training', url: '/calendar-training', icon: IconCalendar },
  { title: 'Career Path', url: '/career-path', icon: IconPath },
  { title: 'Bucket Learning', url: '/bucket-learning', icon: IconBookOpenText },
  { title: `Lemon's Leaderboard`, url: '/leaderboard', icon: IconRanking },
  {
    title: 'Knowledge Center',
    url: '#',
    icon: IconBrain,
    children: [
      { title: 'Forum', url: '/knowledge-center/forum', icon: IconChats },
      {
        title: 'ACC Guava',
        url: '/knowledge-center/acc-guava',
        icon: IconBookmark,
      },
      {
        title: 'ACCPedia',
        url: '/knowledge-center/accpedia',
        icon: IconTextAa,
      },
      { title: 'FAQ', url: '/knowledge-center/faq', icon: IconSealQuestion },
    ],
  },
];

const LearnerBaseSidebar = () => {
  const pathname = usePathname();
  const [hovered, setHovered] = useState<string | null>(null);
  const [open, setOpen] = useState<Record<string, boolean>>({});
  const { mutateAsync: logout } = useLogoutUserMutation();

  const hasActiveDescendant = (
    item: MenuItem,
    currentPath: string
  ): boolean => {
    return (
      item.url === currentPath ||
      (item.children ?? []).some((c) => hasActiveDescendant(c, currentPath))
    );
  };

  const toggle = (key: string) => setOpen((s) => ({ ...s, [key]: !s[key] }));
  const router = useRouter();
  const renderMenuButton = (
    item: MenuItem,
    opts: {
      isActive: boolean;
      isHovered: boolean;
      depth?: number;
      isLogout?: boolean;
      hasChildren?: boolean;
      isOpen?: boolean;
      onClick?: () => void;
    }
  ) => {
    const depth = opts.depth ?? 0;
    const isActiveOrHovered = opts.isActive || opts.isHovered;

    let iconColor = '#B1B1B1';
    if (opts.isLogout) {
      iconColor = '#D3483E';
    } else if (isActiveOrHovered) {
      iconColor = '#DA7300';
    }

    const panelId = `${item.title.toLowerCase().replace(/\s+/g, '-')}-${depth}`;

    let stateClasses = 'border-transparent text-[#808080]';
    if (opts.isLogout) {
      stateClasses = 'border-transparent text-[#D3483E] hover:text-[#D3483E]!';
    } else if (isActiveOrHovered) {
      stateClasses =
        'bg-[#FFECD3] border-[#F7941E] text-[#F7941E] hover:bg-[#FFECD3]! hover:border-[#F7941E]! hover:text-[#F7941E]!';
    }

    const Comp = opts.onClick ? 'button' : Link;

    return (
      <Comp
        href={item.url}
        onClick={async (e) => {
          if (opts.hasChildren) {
            e.preventDefault();
            toggle(item.title);
          }
          if (opts.isLogout) {
            e.preventDefault();
            try {
              const response = await logout();
              console.log(response);
              if (response.status) {
                await clearSession();
                notifyHotSuccess('Logout berhasil');
                router.push('/login');
              }
            } catch (error) {
              console.log(error);
            }
          }
        }}
        onMouseEnter={() => setHovered(item.title)}
        onMouseLeave={() => setHovered(null)}
        role={opts.hasChildren ? 'button' : undefined}
        aria-current={opts.isActive ? 'page' : undefined}
        aria-expanded={opts.hasChildren ? !!opts.isOpen : undefined}
        aria-controls={opts.hasChildren ? panelId : undefined}
        className={cn(
          'flex w-full min-w-0 items-center gap-2 px-3 py-2 rounded-lg border transition-colors cursor-pointer',
          depth > 0 && 'pl-8',
          stateClasses
        )}
      >
        <item.icon
          size={18}
          color={iconColor}
        />
        <span className="flex-1 truncate">{item.title}</span>
        {opts.hasChildren && (
          <span className="ml-2 shrink-0">
            {opts.isOpen ? (
              <ChevronDown
                size={18}
                color={iconColor}
              />
            ) : (
              <ChevronRight
                size={18}
                color={iconColor}
              />
            )}
          </span>
        )}
      </Comp>
    );
  };

  const renderMenuTree = (nodes: MenuItem[], depth = 0) =>
    nodes.map((item) => {
      const isActiveSelf = pathname === item.url;
      const isActiveBranch =
        !isActiveSelf &&
        (item.children ?? []).some(
          (child) =>
            pathname === child.url || hasActiveDescendant(child, pathname)
        );
      const isHovered = hovered === item.title;
      const isOpen = item.children ? open[item.title] ?? isActiveBranch : false;

      return (
        <BaseSidebarMenuItem key={`${item.title}-${depth}`}>
          <BaseSidebarMenuButton asChild>
            {renderMenuButton(item, {
              isActive: isActiveSelf || isActiveBranch,
              isHovered,
              depth,
              hasChildren: !!item.children?.length,
              isOpen,
            })}
          </BaseSidebarMenuButton>

          {item.children && item.children.length > 0 && isOpen && (
            <BaseSidebarMenu
              id={`${item.title.toLowerCase().replace(/\s+/g, '-')}-${depth}`}
              className="mt-3 gap-3"
            >
              {renderMenuTree(item.children, depth + 1)}
            </BaseSidebarMenu>
          )}
        </BaseSidebarMenuItem>
      );
    });

  return (
    <BaseSidebar
      variant="sidebar"
      collapsible="none"
      className="hidden bg-white md:flex w-full max-w-[250px] min-h-[calc(100vh-130px)] max-h-[calc(100vh-130px)] sticky top-20 z-50"
    >
      <BaseSidebarContent className="w-[250px] pt-8 p-4 border border-[#DEDEDE] rounded-2xl flex flex-col justify-between h-full overflow-x-hidden">
        {/* Menu utama */}
        <BaseSidebarGroup>
          <BaseSidebarGroupContent>
            <BaseSidebarMenu className="gap-3">
              {renderMenuTree(items)}
            </BaseSidebarMenu>
          </BaseSidebarGroupContent>
        </BaseSidebarGroup>

        {/* Menu Logout */}
        <BaseSidebarGroup className="pt-3 border-t border-[#DEDEDE]">
          <BaseSidebarGroupContent>
            <BaseSidebarMenu>
              <BaseSidebarMenuItem>
                <BaseSidebarMenuButton asChild>
                  {renderMenuButton(
                    { title: 'Logout', url: '/login', icon: IconSignout },
                    {
                      isActive: false,
                      isHovered: hovered === 'Logout',
                      isLogout: true,
                      onClick: () => logout(),
                    }
                  )}
                </BaseSidebarMenuButton>
              </BaseSidebarMenuItem>
            </BaseSidebarMenu>
          </BaseSidebarGroupContent>
        </BaseSidebarGroup>
      </BaseSidebarContent>
    </BaseSidebar>
  );
};

export default LearnerBaseSidebar;
