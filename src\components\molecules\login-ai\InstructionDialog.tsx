import { IconLightBulp } from '@/assets/icons/IconLightBulp';
import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogFooter,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';

const LoginAiInstructionDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  return (
    <BaseDialog
      open={open}
      onOpenChange={(val) => !val && onClose()}
    >
      <BaseDialogContent
        showCloseButton={false}
        className="fixed w-full max-w-none inset-x-0 bottom-0 top-auto rounded-t-2xl rounded-b-none md:rounded-2xl p-4 md:p-5 shadow-[0_-6px_24px_rgba(0,0,0,0.06)] translate-x-0 translate-y-0 md:inset-auto md:left-1/2 md:top-1/2 md:w-[min(428px,calc(100vw-2rem))] md:translate-x-[-50%] md:translate-y-[-50%] md:shadow-none"
        style={{ paddingBottom: 'max(env(safe-area-inset-bottom), 16px)' }}
      >
        <div className="flex justify-left">
          <div className="relative w-[56px] h-[56px] flex items-center justify-center rounded-full bg-[#F7941E]/10">
            <div className="w-[42px] h-[42px] rounded-full bg-[#F7941E]/10 flex items-center justify-center">
              <IconLightBulp
                size={28}
                color="#F7941E"
              />
            </div>
          </div>
        </div>
        <BaseDialogHeader className="gap-3 text-left">
          <BaseDialogTitle className="text-xl text-[#3C3C3C] font-medium">
            Multi-Factor Authentication Instructions
          </BaseDialogTitle>
          <BaseDialogDescription className="text-sm">
            <span className="text-[#3C3C3C]">
              Follow the instructions bellow to get the MFA code:
            </span>
            <br />
            <ol className="text-[#767676] list-decimal pl-5">
              <li>
                Open the Microsoft Authenticator App and scan the barcode. Input
                one-time
              </li>
              <li>
                Input one-time code that appears in the application process and
                click submit to complete the process.
              </li>
              <li>Copy the code appears on the app to the input field.</li>
            </ol>
          </BaseDialogDescription>
        </BaseDialogHeader>

        <BaseDialogFooter className="mt-5 md:mt-4 flex flex-row gap-4 md:gap-3 justify-center">
          <BaseButton
            onClick={() => onClose()}
            className="w-full md:w-[141px] h-12 md:h-11 bg-white border border-[#DEDEDE] rounded-md text-base md:text-sm text-[#3C3C3C] font-medium hover:bg-white hover:opacity-80 cursor-pointer"
          >
            Close
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default LoginAiInstructionDialog;
