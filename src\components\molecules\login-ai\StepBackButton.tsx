import { IconArrow } from '@/assets/icons/IconArrow';
import { useMediaQuery } from '@/hooks';

const LoginAiStepBackButton = ({
  onBack,
  text = 'Back to Previous Step',
}: {
  onBack: () => void;
  text?: string;
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');

  return (
    <button
      type="button"
      onClick={onBack}
      className="flex items-center justify-start gap-1 md:gap-2 mb-5 md:mb-6 cursor-pointer"
    >
      <IconArrow direction="left" size={isMobile ? 16 : 24} />
      <span className="text-[#F7941E] text-xs md:text-base">{text}</span>
    </button>
  );
};

export default LoginAiStepBackButton;