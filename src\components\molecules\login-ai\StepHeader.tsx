const LoginAiStepHeader = ({
  title,
  subtitle,
  addOn,
}: {
  title: string;
  subtitle?: string;
  addOn?: React.ReactNode;
}) => (
  <>
    <h4 className="text-xl md:text-[26px] md:leading-[30px] mb-3 text-[#3C3C3C] font-bold">
      {title}
    </h4>
    {subtitle && <p className="text-[#767676] text-sm mb-8">{subtitle}</p>}
    {addOn ? <div className="text-[#3C3C3C] text-sm mb-8">{addOn}</div> : null}
  </>
);

export default LoginAiStepHeader;