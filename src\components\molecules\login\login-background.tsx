import React from "react";
import Image from "next/image";
import LogoNew from "@/assets/images/logo-new.png";
import Pattern from "@/assets/images/pattern.png";
import Waves from "@/assets/images/waves.png";

const LoginBackground: React.FC = () => {
  return (
    <>
      {/* LEFT SIDE — DESKTOP ONLY */}
      <div
        className="hidden md:flex w-[57.5%] relative items-center justify-end overflow-hidden"
        style={{
          backgroundImage: `url(${Waves.src}), url(${Pattern.src})`,
          backgroundRepeat: "no-repeat, no-repeat",
          backgroundSize: "auto 100%, cover",
          backgroundPosition: "left center, center",
        }}
      >
        <div className="relative z-10 flex justify-center pb-[180px] pr-[50px]">
          <Image
            src={LogoNew}
            alt="Logo"
            width={2674}
            height={1414}
            priority
            className="h-auto"
            style={{ width: "clamp(300px, 46.4vw, 668px)" }}
          />
        </div>
      </div>

      {/* MOBILE HERO (background inside right side) */}
      <div
        className="absolute h-full inset-x-0 top-0 md:hidden overflow-hidden"
        style={{
          backgroundImage: `url(${Waves.src}), url(${Pattern.src})`,
          backgroundRepeat: "no-repeat, no-repeat",
          backgroundSize: "auto 175%, cover",
          backgroundPosition: "left bottom, center",
        }}
      >
        <div className="absolute top-4 left-0 right-0 flex justify-center">
          <Image
            src={LogoNew}
            alt="Logo"
            width={600}
            height={320}
            priority
            className="h-auto"
            style={{ width: "clamp(188px, 45vw, 260px)" }}
          />
        </div>
      </div>
    </>
  );
};

export default LoginBackground;
