import React from "react";
import { UseFormReturn } from "react-hook-form";
import { BaseInput } from "@/components/atoms/input";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import StepHeader from "./step-header";
import { Step1Values } from "@/hooks/useLoginFlow";
import Spinner from "@/components/atoms/spinner";

interface LoginStep1Props {
  form: UseFormReturn<Step1Values>;
  onSubmit: (data: Step1Values) => Promise<void>;
  isLoading: boolean;
}

const LoginStep1: React.FC<LoginStep1Props> = ({
  form,
  onSubmit,
  isLoading,
}) => {
  return (
    <>
      <StepHeader
        title="Welcome back!"
        subtitle="Feel the taste of knowledge with <PERSON>"
      />
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className={`${form.formState.errors.email ? "mb-5" : "mb-6"}`}>
          <BaseLabel
            htmlFor="email"
            className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
          >
            NPK, Email, or Phone Number
          </BaseLabel>
          <BaseInput
            id="email"
            type="text"
            placeholder="Input your NPK, Email, or Phone Number"
            {...form.register("email")}
            className={`w-full text-sm md:text-base ${
              form.formState.errors.email ? "border-[#EA2B1F]" : ""
            }`}
          />
          {form.formState.errors.email && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>
        <BaseButton
          type="submit"
          className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
          disabled={isLoading}
        >
          {isLoading ? <Spinner /> : "Continue"}
        </BaseButton>
      </form>
    </>
  );
};

export default LoginStep1;
