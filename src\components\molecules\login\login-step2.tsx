import React from "react";
import { UseFormReturn } from "react-hook-form";
import { BaseInput } from "@/components/atoms/input";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import StepBackButton from "./step-back-button";
import StepHeader from "./step-header";
import { Step2Values, SelectedUser } from "@/hooks/useLoginFlow";
import Spinner from "@/components/atoms/spinner";

interface LoginStep2Props {
  form: UseFormReturn<Step2Values>;
  onSubmit: (data: Step2Values) => Promise<void>;
  onBack: () => void;
  selectedUser: SelectedUser | null;
  isLoading: boolean;
  onOpenOtpMethodDialog: (type: "login" | "forgot_password") => void;
}

const LoginStep2: React.FC<LoginStep2Props> = ({
  form,
  onSubmit,
  onBack,
  selectedUser,
  isLoading,
  onOpenOtpMethodDialog,
}) => {
  return (
    <>
      <StepBackButton onBack={onBack} />
      <StepHeader
        title={`Welcome back, ${selectedUser?.name?.split(" ")[0]}`}
        subtitle="Please fill in your password"
      />
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className={`${form.formState.errors.password ? "mb-5" : "mb-6"}`}>
          <BaseLabel
            htmlFor="password"
            className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
          >
            Password
          </BaseLabel>
          <BaseInput
            id="password"
            type="password"
            placeholder="Input password"
            {...form.register("password")}
            className={`w-full text-sm md:text-base ${
              form.formState.errors.password ? "border-[#EA2B1F]" : ""
            }`}
          />
          {form.formState.errors.password && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>

        <BaseButton
          type="submit"
          className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer mb-4 md:mb-6"
          disabled={isLoading}
        >
          {isLoading ? <Spinner /> : "Login"}
        </BaseButton>

        <div className="flex justify-start items-center gap-2">
          <p className="text-[#3C3C3C] text-xs md:text-base font-medium">
            Don't remember your password?
          </p>
          <button
            type="button"
            className="text-[#F7941E] text-xs md:text-base cursor-pointer"
            onClick={() => onOpenOtpMethodDialog("forgot_password")}
          >
            Forgot Password
          </button>
        </div>
      </form>
    </>
  );
};

export default LoginStep2;
