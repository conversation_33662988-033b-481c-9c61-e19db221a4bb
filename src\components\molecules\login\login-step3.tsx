import React from "react";
import { UseFormReturn } from "react-hook-form";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import StepBackButton from "./step-back-button";
import StepHeader from "./step-header";
import OtpInput from "./otp-input";
import { Step3Values, SelectedUser } from "@/hooks/useLoginFlow";
import Spinner from "@/components/atoms/spinner";

interface LoginStep3Props {
  form: UseFormReturn<Step3Values>;
  onSubmit: (
    data: Step3Values,
    otpType: "login" | "forgot_password"
  ) => Promise<void>;
  onBack: () => void;
  onResendOtp: () => Promise<void>;
  selectedUser: SelectedUser | null;
  otpMethod: "phone" | "email" | null;
  resendTimer: number;
  formatTimer: (seconds: number) => string;
  isMfaOtp: boolean;
  isVerifyLoading: boolean;
  isResendLoading: boolean;
  otpType: "login" | "forgot_password";
}

const LoginStep3: React.FC<LoginStep3Props> = ({
  form,
  onSubmit,
  onBack,
  onResendOtp,
  selectedUser,
  otpMethod,
  resendTimer,
  formatTimer,
  isMfaOtp,
  isVerifyLoading,
  isResendLoading,
  otpType,
}) => {
  if (isMfaOtp) {
    return (
      <>
        <StepBackButton onBack={onBack} />
        <StepHeader
          title="2 Factor Authentication"
          subtitle="Copy the authentication code appears on your Microsoft Authentication app"
        />
        <form onSubmit={form.handleSubmit((data) => onSubmit(data, otpType))}>
          <div className={`${form.formState.errors.otp ? "mb-5" : "mb-6"}`}>
            <BaseLabel
              htmlFor="otp"
              className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
            >
              Authentication Code
            </BaseLabel>
            <OtpInput
              value={form.watch("otp")}
              onChange={(val) => form.setValue("otp", val)}
              error={form.formState.errors.otp?.message}
            />
            {form.formState.errors.otp && (
              <p className="text-[#EA2B1F] text-xs mt-1">
                {form.formState.errors.otp.message}
              </p>
            )}
          </div>

          <BaseButton
            type="submit"
            className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
            disabled={isVerifyLoading}
          >
            {isVerifyLoading ? <Spinner /> : "Verify OTP Code"}
          </BaseButton>
        </form>
      </>
    );
  }

  return (
    <>
      <StepBackButton onBack={onBack} />
      <StepHeader
        title={`Welcome to Lemon, ${selectedUser?.name?.split(" ")[0]}`}
        subtitle="Let's create a password for you"
        addOn={
          <>
            We have sent an OTP Code to{" "}
            <span className="font-bold">
              {selectedUser ? selectedUser[otpMethod || "phone"] : ""}
            </span>
            .
            <br />
            Please check your message and input the OTP Code here.
          </>
        }
      />
      <form onSubmit={form.handleSubmit((data) => onSubmit(data, otpType))}>
        <div className={`${form.formState.errors.otp ? "mb-5" : "mb-6"}`}>
          <OtpInput
            value={form.watch("otp")}
            onChange={(val) => form.setValue("otp", val)}
            error={form.formState.errors.otp?.message}
          />
          {form.formState.errors.otp && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.otp.message}
            </p>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <BaseButton
            type="submit"
            className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
            disabled={isVerifyLoading}
          >
            {isVerifyLoading ? <Spinner /> : "Verify OTP Code"}
          </BaseButton>
          <BaseButton
            type="button"
            onClick={onResendOtp}
            disabled={resendTimer > 0 || isResendLoading}
            className={`w-full text-xs md:text-base bg-white border border-[#DEDEDE] text-[#3C3C3C] h-[44px] md:h-[48px] rounded-[8px] hover:bg-white ${
              resendTimer > 0 || isResendLoading
                ? "bg-[#0202020A] border-[#A4A4A4] text-[#A4A4A4] cursor-not-allowed"
                : "hover:opacity-80 cursor-pointer"
            }`}
          >
            {isResendLoading
              ? "Sending..."
              : resendTimer > 0
              ? `Resend Code in ${formatTimer(resendTimer)}`
              : "Resend Code"}
          </BaseButton>
        </div>
      </form>
    </>
  );
};

export default LoginStep3;
