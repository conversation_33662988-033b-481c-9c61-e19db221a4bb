import React from "react";
import { UseFormReturn } from "react-hook-form";
import { BaseInput } from "@/components/atoms/input";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import StepBackButton from "./step-back-button";
import StepHeader from "./step-header";
import { Step5Values, SelectedUser } from "@/hooks/useLoginFlow";
import Spinner from "@/components/atoms/spinner";

interface LoginStep5Props {
  form: UseFormReturn<Step5Values>;
  onSubmit: (data: Step5Values) => Promise<void>;
  onBack: () => void;
  selectedUser: SelectedUser | null;
  isLoading: boolean;
}

const LoginStep5: React.FC<LoginStep5Props> = ({
  form,
  onSubmit,
  onBack,
  selectedUser,
  isLoading,
}) => {
  return (
    <>
      <StepBackButton onBack={onBack} />
      <StepHeader
        title={`Welcome back, ${selectedUser?.name?.split(" ")[0]}`}
        subtitle="Let's create a password for you"
      />
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div
          className={`${form.formState.errors.newPassword ? "mb-5" : "mb-6"}`}
        >
          <BaseLabel
            htmlFor="newPassword"
            className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
          >
            New Password
          </BaseLabel>
          <BaseInput
            id="newPassword"
            type="password"
            placeholder="New password"
            {...form.register("newPassword")}
            className={`w-full text-sm md:text-base ${
              form.formState.errors.newPassword ? "border-[#EA2B1F]" : ""
            }`}
          />
          {form.formState.errors.newPassword && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.newPassword.message}
            </p>
          )}
        </div>

        <div
          className={`${
            form.formState.errors.confirmPassword ? "mb-5" : "mb-6"
          }`}
        >
          <BaseLabel
            htmlFor="confirmPassword"
            className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
          >
            Confirm Password
          </BaseLabel>
          <BaseInput
            id="confirmPassword"
            type="password"
            placeholder="Confirm password"
            {...form.register("confirmPassword")}
            className={`w-full text-sm md:text-base ${
              form.formState.errors.confirmPassword ? "border-[#EA2B1F]" : ""
            }`}
          />
          {form.formState.errors.confirmPassword && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.confirmPassword.message}
            </p>
          )}
        </div>

        <BaseButton
          type="submit"
          className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
          disabled={isLoading}
        >
          {isLoading ? <Spinner /> : "Create Password & Login"}
        </BaseButton>
      </form>
    </>
  );
};

export default LoginStep5;
