'use client';

import { useMediaQuery } from '@/hooks';
import React from 'react';
import ReactOtpInput from 'react-otp-input';

type Props = {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  isMobileQuery?: string;
};

const OtpInput: React.FC<Props> = ({
  value,
  onChange,
  error,
  isMobileQuery,
}) => {
  const isMobile = useMediaQuery(isMobileQuery || '(max-width: 768px)');

  return (
    <ReactOtpInput
      value={value}
      onChange={onChange}
      numInputs={6}
      inputType="tel"
      shouldAutoFocus
      containerStyle="flex justify-between w-full gap-2 lg:gap-4"
      inputStyle={{
        flex: 1,
        height: `${isMobile ? '48px' : '64px'}`,
        fontSize: '20px',
        textAlign: 'center',
        borderRadius: '8px',
        border: `1px solid ${error ? '#EA2B1F' : '#ccc'}`,
        outline: 'none',
      }}
      renderInput={(props) => <input {...props} />}
    />
  );
};

export default OtpInput;
