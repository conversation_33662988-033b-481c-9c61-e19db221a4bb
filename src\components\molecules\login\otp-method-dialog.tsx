import { <PERSON>con<PERSON><PERSON> } from "@/assets/icons/IconKey";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogFooter,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";

const OtpMethodDialog = ({
  open,
  onClose,
  onSelect,
  isPending,
}: {
  open: boolean;
  onClose: () => void;
  onSelect: (method: "email" | "phone") => void;
  isPending: boolean;
}) => {
  return (
    <BaseDialog open={open} onOpenChange={(val) => !val && onClose()}>
      <BaseDialogContent
        className="fixed w-full max-w-none inset-x-0 bottom-0 top-auto rounded-t-2xl rounded-b-none md:rounded-2xl p-4 md:p-5 shadow-[0_-6px_24px_rgba(0,0,0,0.06)] translate-x-0 translate-y-0 md:inset-auto md:left-1/2 md:top-1/2 md:w-[min(428px,calc(100vw-2rem))] md:translate-x-[-50%] md:translate-y-[-50%] md:shadow-none"
        style={{ paddingBottom: "max(env(safe-area-inset-bottom), 16px)" }}
      >
        <div className="flex justify-left mb-5">
          <div className="relative w-[56px] h-[56px] flex items-center justify-center rounded-full bg-[#F7941E]/10">
            <div className="w-[42px] h-[42px] rounded-full bg-[#F7941E]/10 flex items-center justify-center">
              <IconKey size={28} color="#F7941E" />
            </div>
          </div>
        </div>
        <BaseDialogHeader>
          <BaseDialogTitle className="text-xl text-[#3C3C3C] text-left md:text-center font-medium">
            Send OTP Code and Create New Password
          </BaseDialogTitle>
          <BaseDialogDescription className="text-sm text-[#767676] text-left md:text-center mt-3 md:mt-1">
            Please select “Email” to receive the OTP code via email or select
            “Phone Number” to receive the OTP code via a text message
          </BaseDialogDescription>
        </BaseDialogHeader>

        <BaseDialogFooter className="mt-5 md:mt-4 flex flex-row gap-4 md:gap-3 justify-center">
          <BaseButton
            onClick={() => onSelect("phone")}
            className="w-[156px] md:w-[141px] h-12 md:h-11 bg-white border border-[#DEDEDE] rounded-md text-base md:text-sm text-[#3C3C3C] font-medium hover:bg-white hover:opacity-80 cursor-pointer"
            disabled={isPending}
          >
            {isPending ? "Sending OTP Code..." : "Phone Number"}
          </BaseButton>
          <BaseButton
            onClick={() => onSelect("email")}
            className="w-[156px] md:w-[141px] h-12 md:h-11 bg-[#F7941E] rounded-md text-base md:text-sm text-white font-medium hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
            disabled={isPending}
          >
            {isPending ? "Sending OTP Code..." : "Email"}
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default OtpMethodDialog;
