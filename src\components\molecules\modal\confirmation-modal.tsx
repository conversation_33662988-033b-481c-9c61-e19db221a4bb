"use client";

import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogDescription,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { AlertCircle, CheckCircle2, Trash2, Info } from "lucide-react";
import React from "react";

export type ConfirmationType = "delete" | "info" | "success" | "warning";

export interface ConfirmationModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  type?: ConfirmationType;
  isLoading?: boolean;
}

const typeConfig = {
  delete: {
    icon: Trash2,
    iconColor: "text-red-400",
    iconBgColor: "bg-red-200",
    borderColor: "border-red-100",
    buttonVariant: "destructive" as const,
  },
  info: {
    icon: Info,
    iconColor: "text-orange-400",
    iconBgColor: "bg-orange-200",
    borderColor: "border-orange-100",
    buttonVariant: null,
  },
  success: {
    icon: CheckCircle2,
    iconColor: "text-green-400",
    iconBgColor: "bg-green-200",
    borderColor: "border-green-100",
    buttonVariant: "default" as const,
  },
  warning: {
    icon: AlertCircle,
    iconColor: "text-yellow-400",
    iconBgColor: "bg-yellow-200",
    borderColor: "border-yellow-100",
    buttonVariant: null,
  },
};

const ConfirmationModal = ({
  isOpen,
  onOpenChange,
  onConfirm,
  title,
  description,
  confirmText = "Konfirmasi",
  cancelText = "Batal",
  type = "info",
  isLoading = false,
}: ConfirmationModalProps) => {
  const config = typeConfig[type];
  const Icon = config.icon;

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <BaseDialog open={isOpen} onOpenChange={onOpenChange}>
      <BaseDialogContent className="h-fit min-w-4/12" showCloseButton={false}>
        <BaseDialogHeader>
          <div
            className={`${config.iconBgColor} w-fit p-2 rounded-full border-8 ${config.borderColor}`}
          >
            <Icon className={config.iconColor} size={28} />
          </div>
          <BaseDialogTitle className="flex flex-col gap-4 text-xl mt-3">
            {title}
          </BaseDialogTitle>
          <BaseDialogDescription className="text-gray-500 text-sm -mt-1">
            {description}
          </BaseDialogDescription>
        </BaseDialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <DialogClose asChild>
            <BaseButton className="w-34 h-11" variant="outline">
              {cancelText}
            </BaseButton>
          </DialogClose>
          <BaseButton
            className="w-34 h-11"
            variant={config.buttonVariant}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? "Memproses..." : confirmText}
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default ConfirmationModal;
