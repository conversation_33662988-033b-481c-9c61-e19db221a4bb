'use client';

import React from 'react';
import { IconChevron } from '@/assets/icons/IconChevron';

interface PaginationProps {
  page: number;
  totalEntries: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  isMobile?: boolean;
  hideSummary?: boolean;
  alignLeft?: boolean;
}

const PaginationComp: React.FC<PaginationProps> = ({
  page,
  totalEntries,
  pageSize,
  onPageChange,
  isMobile = false,
  hideSummary = false,
  alignLeft = false,
}) => {
  const currentPage = page + 1;
  const totalPages = Math.ceil(totalEntries / pageSize);

  const start = page * pageSize + 1;
  const end = Math.min((page + 1) * pageSize, totalEntries);

  const go = (target: number) => {
    const clamped = Math.min(totalPages, Math.max(1, target));
    onPageChange(clamped - 1);
  };

  const getFewPages = (totalPages: number): number[] => {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  };

  const getMobilePages = (
    currentPage: number,
    totalPages: number
  ): (number | string)[] => {
    const pages: (number | string)[] = [1];

    if (currentPage > 3) pages.push('dots-start');
    if (currentPage !== 1 && currentPage !== totalPages)
      pages.push(currentPage);
    if (currentPage < totalPages - 2) pages.push('dots-end');

    pages.push(totalPages);
    return pages;
  };

  const getDesktopPages = (
    currentPage: number,
    totalPages: number
  ): (number | string)[] => {
    let pages: number[] = [];

    if (currentPage <= 3) {
      pages = [1, 2, 3, 4, 5];
    } else if (currentPage >= totalPages - 2) {
      pages = [
        totalPages - 4,
        totalPages - 3,
        totalPages - 2,
        totalPages - 1,
        totalPages,
      ];
    } else {
      pages = [
        currentPage - 2,
        currentPage - 1,
        currentPage,
        currentPage + 1,
        currentPage + 2,
      ];
    }

    const result: (number | string)[] = [];

    if (pages[0] > 1) {
      result.push(1);
      if (pages[0] > 2) result.push('dots-start');
    }

    result.push(...pages);

    if (pages[pages.length - 1] < totalPages) {
      if (pages[pages.length - 1] < totalPages - 1) result.push('dots-end');
      result.push(totalPages);
    }

    return result;
  };

  const getPagesToRender = (): (number | string)[] => {
    if (totalPages <= 5) return getFewPages(totalPages);
    if (isMobile) return getMobilePages(currentPage, totalPages);
    return getDesktopPages(currentPage, totalPages);
  };

  const renderDots = (key: string) => (
    <span
      key={key}
      className="text-[#A4A4A4] px-1"
    >
      …
    </span>
  );

  const renderButton = (pageNumber: number) => (
    <button
      key={pageNumber}
      onClick={() => go(pageNumber)}
      className={`h-10 w-10 rounded text-xs cursor-pointer ${
        currentPage === pageNumber
          ? 'bg-[#3C3C3C14] text-[#3C3C3C] font-medium'
          : 'text-[#3C3C3C] hover:bg-[#F7F7F7]'
      }`}
    >
      {pageNumber}
    </button>
  );
  const renderPageButtons = () => {
    const pages = getPagesToRender();

    return pages.map((p) => {
      if (typeof p === 'number') return renderButton(p);
      return renderDots(p);
    });
  };

  return (
    <div className="w-full flex justify-between gap-3 items-center">
      {!hideSummary && (
        <p className="w-full hidden md:block text-xs text-[#3C3C3C]">
          Showing {start} to {end} of {totalEntries.toLocaleString('id-ID')}{' '}
          entries
        </p>
      )}

      <div
        className={`w-full flex gap-3 items-center ${
          alignLeft ? 'justify-start' : 'justify-end'
        }`}
      >
        {/* Prev */}
        <button
          onClick={() => go(currentPage - 1)}
          disabled={currentPage === 1}
          className={`h-10 w-10 flex justify-center items-center rounded-md ${
            currentPage === 1
              ? 'cursor-not-allowed'
              : 'cursor-pointer hover:bg-[#F7F7F7]'
          }`}
        >
          <IconChevron
            size={16}
            direction="left"
            color={currentPage === 1 ? '#A4A4A4' : '#3C3C3C'}
          />
        </button>
        {/* Page buttons */}
        {renderPageButtons()}
        {/* Next */}
        <button
          onClick={() => go(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`h-10 w-10 flex justify-center items-center rounded-md ${
            currentPage === totalPages
              ? 'cursor-not-allowed'
              : 'cursor-pointer hover:bg-[#F7F7F7]'
          }`}
        >
          <IconChevron
            size={16}
            direction="right"
            color={currentPage === totalPages ? '#A4A4A4' : '#3C3C3C'}
          />
        </button>
      </div>
    </div>
  );
};

export default PaginationComp;
