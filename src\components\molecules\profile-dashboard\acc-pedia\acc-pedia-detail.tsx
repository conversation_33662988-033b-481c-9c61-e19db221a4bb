'use client';

import React from 'react';
import { ChevronLeft, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import { slugify, useMediaQuery } from '@/hooks';

type LinkItem = { label: string; href?: string; onClick?: () => void };

export type AccPediaDetailProps = {
  title: string;
  verified?: boolean;
  updatedAt?: Date;
  description?: string;
  example?: string;
  formula?: string;
  otherInfos?: LinkItem[];
  references?: string[];
  similarKeywords?: string[];
  onBack?: () => void;
  className?: string;
};

const fmtFull = (d: Date) =>
  `${d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  })} ${d
    .toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
    .replace('.', ':')}`;

function Section({
  title,
  children,
}: React.PropsWithChildren<{ title: string }>) {
  return (
    <div className="flex flex-col gap-5">
      <p className="text-[#3C3C3C] font-bold">{title}</p>
      <div className="text-[#717171] text-sm">{children}</div>
    </div>
  );
}

export default function AccPediaDetail({
  title,
  verified = true,
  updatedAt,
  description,
  example,
  formula,
  otherInfos = [],
  references = [],
  similarKeywords = [],
  onBack,
  className,
}: Readonly<AccPediaDetailProps>) {
  const updated = updatedAt ?? new Date();
  const isMobilForIcon = useMediaQuery('(max-width: 767px)');

  return (
    <div className={cn('flex flex-col gap-6 md:gap-8', className)}>
      <div className="flex flex-col xl:flex-row xl:justify-between gap-3">
        <div className="flex flex-row gap-3 md:gap-2">
          <button
            className="cursor-pointer"
            onClick={onBack}
          >
            <ChevronLeft
              size={isMobilForIcon ? 20 : 24}
              color="#343330"
            />
          </button>

          <h3 className="text-[#3C3C3C] font-bold text-lg md:text-3xl">
            {title}
          </h3>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1 text-xs text-[#767676]">
            <Calendar size={16} />
            <span>Diperbarui: {fmtFull(updated)}</span>
          </div>

          {verified && (
            <span className="bg-[#E6F7EE] text-[#00AF54] rounded text-xs px-2 py-[6px]">
              Terverifikasi
            </span>
          )}
        </div>
      </div>

      <div className="border-t border-[#DEDEDE]" />

      <div className="flex flex-col gap-6 md:gap-8">
        <Section title="Deskripsi">
          <p>
            {description ??
              'MSCP adalah persentase jumlah Delivery Order (DO) Dealer yang menggunakan leasing ACC dibandingkan dengan Total DO Kredit di dealer tersebut. Dengan kata lain membandingkan penjualan kredit yang didapatkan dari dealer direct dengan perusahaan pembiayaan lainnya.'}
          </p>
        </Section>

        <div className="border-t border-[#DEDEDE]"></div>

        <Section title="Contoh Penerapan">
          <p>
            {example ??
              'Pencapaian MSCP dapat menjadi indikator tolak ukur preferensi dealer terhadap perusahaan pembiayaannya. Misal: jika MSCP ACC di dealer XXX tinggi dibanding kompetitor, maka strategi apa yang perlu dilakukan untuk mempertahankannya; sebaliknya jika rendah di dealer YYY, strategi/paket/promosi apa yang perlu ditinjau kembali.'}
          </p>
        </Section>

        <Section title="Formula">
          {formula ??
            'MSCP = Total jumlah unit yang dibiayai ACC (DO ACC) / Total penjualan secara kredit dari dealer (DO kredit)'}
        </Section>

        <Section title="Lihat Informasi Lain">
          <ul className="list-disc list-inside">
            {(otherInfos.length
              ? otherInfos
              : [{ label: 'MSCP' }, { label: 'MSCP Others' }]
            ).map((it) => (
              <li
                key={slugify(it.label)}
                className="marker:text-[#0070E0]"
              >
                {it.href ? (
                  <a
                    href={it.href}
                    className="text-[#0070E0] underline"
                    onClick={it.onClick}
                  >
                    {it.label}
                  </a>
                ) : (
                  <button
                    type="button"
                    onClick={it.onClick}
                    className="text-[#0070E0] underline"
                  >
                    {it.label}
                  </button>
                )}
              </li>
            ))}
          </ul>
        </Section>
        <Section title="Referensi">
          {references.length ? (
            <ul className="list-disc list-inside space-y-1">
              {references.map((r) => (
                <li key={slugify(String(r))}>{r}</li>
              ))}
            </ul>
          ) : (
            <span>-</span>
          )}
        </Section>

        <Section title="Similar Keywords">
          <div className="flex flex-wrap gap-3">
            {(() => {
              const list = similarKeywords.length
                ? similarKeywords
                : [
                    'Project Collaboration',
                    'Project Collaboration',
                    'Project Collaboration',
                  ];
              const seen = new Map<string, number>();

              return list.map((k) => {
                const count = (seen.get(k) ?? 0) + 1;
                seen.set(k, count);
                const key = `${slugify(k)}-${count}`;

                return (
                  <div
                    key={key}
                    className="inline-flex items-center gap-2 rounded-[12px] border border-[#DEDEDE] bg-white px-6 py-[14px]"
                  >
                    <span className="text-[#8A3962]">{k}</span>
                    <span
                      className="text-xs px-2 rounded-full text-white"
                      style={{
                        background:
                          'linear-gradient(104.04deg, #0077FF -1.4%, #001ADF 98.6%)',
                        fontFamily: 'var(--font-pixelify-sans)',
                      }}
                    >
                      NEW
                    </span>
                  </div>
                );
              });
            })()}
          </div>
        </Section>
      </div>
    </div>
  );
}
