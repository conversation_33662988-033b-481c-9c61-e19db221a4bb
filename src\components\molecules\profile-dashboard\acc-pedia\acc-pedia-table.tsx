'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  CellContext,
  HeaderContext,
  useReactTable,
} from '@tanstack/react-table';
import ToolbarFilter from '../riwayat-belajar/toolbar-filter';
import { CheckIcon, Eye, Triangle, Undo2Icon, XIcon } from 'lucide-react';
import { useMediaQuery } from '@/hooks';
import { cn } from '@/lib/utils';
import NoDataTable from '@/assets/images/no-found-data.png';
import Image from 'next/image';
import PaginationComp from '../../pagination';
import { OrangeCheckbox } from '@/components/atoms/checkbox/orange-checkbox';
import { BaseButton } from '@/components/atoms/button';
import ModalConfirmation from './modal-confirmation';
import ModalNotification from './modal-notification';
import RowActionMenu from './row-action-menu';

export type AccPediaRow = {
  id: string;
  title: string;
  keyword: string;
  createdBy: string;
  createdAt: Date;
};

type TableMeta = {
  allPageSelected: boolean;
  togglePageSelectAll: (checked: boolean) => void;
  pageSelection: Record<string, boolean>;
  toggleRow: (id: string, checked: boolean) => void;
  onView?: (row: AccPediaRow) => void;
  onMore?: (row: AccPediaRow) => void;
  onRowApprove?: (row: AccPediaRow) => void;
  onRowReject?: (row: AccPediaRow) => void;
};

const PAGE_SIZE = 10;

const makeId = () =>
  typeof crypto !== 'undefined' && 'randomUUID' in crypto
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);

export function generateDummyAccPedia(total = 37): AccPediaRow[] {
  const authors = [
    'Calvin Jeremiah',
    'Emily Chen',
    'Sofia Martinez',
    'David Kim',
    'Jessica Lee',
    'Michael Wang',
  ];
  const titles = [
    'Astra Pay',
    'Vortex Funds',
    'Quantum Transfer',
    'Eclipse Payments',
    'Nova Charge',
    'Zenith Wallet',
  ];
  const out: AccPediaRow[] = [];
  const now = new Date();
  for (let i = 0; i < total; i++) {
    const d = new Date(now);
    d.setDate(now.getDate() - (i % 9) - 1);
    d.setHours(9 + (i % 8), (i % 2) * 30, 0, 0);
    const t = titles[i % titles.length];
    out.push({
      id: makeId(),
      title: t,
      keyword: t,
      createdBy: authors[i % authors.length],
      createdAt: d,
    });
  }
  return out;
}

const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
    />
  </div>
);

const fmtCreatedAt = (d: Date) => {
  const day = d.toLocaleDateString('id-ID', { day: '2-digit' });
  const mon = d.toLocaleDateString('id-ID', { month: 'short' });
  const yr = d.getFullYear();
  const time = d
    .toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
    .replace('.', ':');
  return `${day} ${mon} ${yr}, ${time}`;
};

const getThWidthClass = (id: string) => {
  if (id === 'select') return 'w-[40px] min-w-[40px]';
  if (id === 'action') return 'w-[80px] min-w-[80px]';
  return 'min-w-[140px]';
};

type Ctx = Readonly<CellContext<AccPediaRow, unknown>>;
type Hdr = Readonly<HeaderContext<AccPediaRow, unknown>>;

export function TitleCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.getValue() as string}</span>;
}
export function KeywordCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.getValue() as string}</span>;
}
export function CreatedByCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.getValue() as string}</span>;
}
export function CreatedAtCell(ctx: Ctx) {
  return (
    <span className="text-[#3C3C3C]">
      {fmtCreatedAt(ctx.getValue() as Date)}
    </span>
  );
}

export function SelectHeader(ctx: Hdr) {
  const meta = ctx.table.options.meta as TableMeta | undefined;
  const rows = ctx.table.getRowModel().rows;
  const selectedCount = rows.filter(
    (r) => !!meta?.pageSelection?.[r.original.id]
  ).length;
  const totalOnPage = rows.length;

  let checked: boolean | 'indeterminate';
  if (selectedCount === 0) {
    checked = false;
  } else if (selectedCount === totalOnPage) {
    checked = true;
  } else {
    checked = 'indeterminate';
  }

  return (
    <OrangeCheckbox
      aria-label="Select current page"
      checked={checked}
      onCheckedChange={(v) => meta?.togglePageSelectAll(!!v)}
    />
  );
}

export function SelectCell(ctx: Ctx) {
  const meta = ctx.table.options.meta as TableMeta | undefined;
  const id = ctx.row.original.id;
  const checked = !!meta?.pageSelection?.[id];

  return (
    <OrangeCheckbox
      aria-label={`Select ${ctx.row.original.title}`}
      checked={checked}
      onCheckedChange={(v) => meta?.toggleRow(id, !!v)}
    />
  );
}

export function ActionHeader() {
  return (
    <div className="inline-flex items-center gap-1">
      <span>Action</span>
    </div>
  );
}

export function ActionCell(ctx: Ctx) {
  const meta = ctx.table.options.meta as TableMeta | undefined;
  const row = ctx.row.original;

  return (
    <div className="flex items-center">
      <button
        type="button"
        className="px-3 py-5 rounded-full hover:bg-[#F3F3F3] cursor-pointer"
        aria-label="View"
        onClick={() => meta?.onView?.(row)}
      >
        <Eye
          size={16}
          className="text-[#343330]"
        />
      </button>

      <RowActionMenu
        row={row}
        onApprove={(r) => meta?.onRowApprove?.(r)}
        onReject={(r) => meta?.onRowReject?.(r)}
      />
    </div>
  );
}

export default function AccPediaTable({
  rows: incomingRows,
  onView,
  onMore,
}: Readonly<{
  rows?: AccPediaRow[];
  onView?: (row: AccPediaRow) => void;
  onMore?: (row: AccPediaRow) => void;
}>) {
  const isMobile = useMediaQuery('(max-width: 1024px)');
  const isMobilForIcon = useMediaQuery('(max-width: 767px)');

  const allRows = useMemo<AccPediaRow[]>(
    () => incomingRows ?? [],
    [incomingRows]
  );

  const [page, setPage] = useState(0);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'title', desc: false },
  ]);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [confirmStatus, setConfirmStatus] = useState<
    'approve' | 'reject' | null
  >(null);
  const [resultOpen, setResultOpen] = React.useState(false);
  const [resultStatus, setResultStatus] = React.useState<
    'approve' | 'reject' | null
  >(null);
  const [pageSelection, setPageSelection] = useState<Record<string, boolean>>(
    {}
  );

  const TOTAL_ENTRIES = allRows.length;

  const pageRows = useMemo(() => {
    const start = page * PAGE_SIZE;
    return TOTAL_ENTRIES > 0 ? allRows.slice(start, start + PAGE_SIZE) : [];
  }, [allRows, page, TOTAL_ENTRIES]);

  const allPageSelected =
    pageRows.length > 0 && pageRows.every((r) => pageSelection[r.id]);

  const togglePageSelectAll = (checked: boolean) => {
    setPageSelection((prev) => {
      const next = { ...prev };
      pageRows.forEach((r) => {
        next[r.id] = checked;
      });
      return next;
    });
  };

  const toggleRow = (id: string, checked: boolean) => {
    setPageSelection((prev) => ({ ...prev, [id]: checked }));
  };

  const selectedIdsOnPage = useMemo(
    () => pageRows.filter((r) => !!pageSelection[r.id]).map((r) => r.id),
    [pageRows, pageSelection]
  );
  const selectedCountOnPage = selectedIdsOnPage.length;

  const columns = useMemo<ColumnDef<AccPediaRow>[]>(() => {
    return [
      {
        id: 'select',
        header: SelectHeader,
        cell: SelectCell,
        enableSorting: false,
        size: 36,
      },
      {
        id: 'title',
        header: 'Title',
        accessorFn: (r) => r.title,
        cell: TitleCell,
      },
      {
        id: 'keyword',
        header: 'Key Word',
        accessorFn: (r) => r.keyword,
        cell: KeywordCell,
      },
      {
        id: 'createdBy',
        header: 'Created By',
        accessorFn: (r) => r.createdBy,
        cell: CreatedByCell,
      },
      {
        id: 'createdAt',
        header: 'Created At',
        accessorFn: (r) => r.createdAt,
        cell: CreatedAtCell,
        sortingFn: 'datetime',
      },
      {
        id: 'action',
        header: ActionHeader,
        cell: ActionCell,
        enableSorting: false,
        size: 96,
      },
    ];
  }, []);

  const onRowApprove = (_row: AccPediaRow) => {
    setConfirmStatus('approve');
    setConfirmOpen(true);
  };

  const onRowReject = (_row: AccPediaRow) => {
    setConfirmStatus('reject');
    setConfirmOpen(true);
  };

  const onCancelClick = () => setPageSelection({});

  const onRejectClick = () => {
    if (selectedCountOnPage === 0) return;
    setConfirmStatus('reject');
    setConfirmOpen(true);
  };

  const onApproveClick = () => {
    if (selectedCountOnPage === 0) return;
    setConfirmStatus('approve');
    setConfirmOpen(true);
  };

  const handleConfirm = async () => {
    setPageSelection({});
    setResultStatus(confirmStatus);
    setResultOpen(true);
    setConfirmStatus(null);
  };

  const table = useReactTable({
    data: pageRows,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    meta: {
      allPageSelected,
      togglePageSelectAll,
      pageSelection,
      toggleRow,
      onView,
      onMore,
      onRowApprove,
      onRowReject,
    } as TableMeta,
  });

  const hasRows = TOTAL_ENTRIES > 0;

  return (
    <div className="flex flex-col gap-4">
      <p className="text-xl font-bold text-[#3C3C3C] py-3">ACC Pedia</p>

      {/* Top controls */}
      <div className="border-t border-[#EAEAEA] pt-4 text-sm text-[#3C3C3C]">
        <ToolbarFilter />
      </div>

      {/* TABLE */}
      <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-1">
        <table className="w-full border-collapse">
          <thead className="text-xs text-[#767676]">
            {table.getHeaderGroups().map((hg) => (
              <tr key={hg.id}>
                {hg.headers.map((header) => {
                  const canSort = header.column.getCanSort();
                  const sortDir = header.column.getIsSorted();
                  const widthClass = getThWidthClass(header.id);

                  return (
                    <th
                      key={header.id}
                      className={cn(
                        'py-4 px-3 font-medium text-left text-xs text-[#3C3C3C] select-none',
                        canSort && 'cursor-pointer',
                        widthClass
                      )}
                      onClick={
                        canSort
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                    >
                      <div className="flex items-center gap-1">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {canSort && (
                          <IconSort
                            direction={
                              sortDir === 'asc' || sortDir === 'desc'
                                ? sortDir
                                : undefined
                            }
                          />
                        )}
                      </div>
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>

          <tbody className="text-xs text-[#3C3C3C]">
            {hasRows ? (
              table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className="odd:bg-[#FAFAFA] even:bg-white"
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className={cn(
                        cell.column.id === 'action' ? 'p-0' : 'py-5 px-3'
                      )}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={table.getAllLeafColumns().length}
                  className="py-14"
                >
                  <div className="flex flex-col items-center justify-center gap-3">
                    <Image
                      src={NoDataTable}
                      alt="no data table"
                      width={560}
                      height={500}
                      className="max-w-[140px] max-h-[125px]"
                    />
                    <div className="flex flex-col gap-1">
                      <p className="text-[#3C3C3C] text-sm font-medium">
                        No data
                      </p>
                      <p className="text-[#767676] text-[10px] leading-[14px]">
                        Come again later
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {selectedCountOnPage > 0 && (
        <div className="flex flex-row gap-[10px] justify-between items-center h-fit">
          <BaseButton
            onClick={onCancelClick}
            className="flex items-center justify-center h-10 w-10 md:w-[95px] bg-white border-none text-[#EA2B1F] shadow-none md:rotate-0 hover:bg-white hover:opacity-80"
          >
            {isMobilForIcon ? (
              <Undo2Icon
                size={16}
                className="rotate-180 scale-x-[-1]"
              />
            ) : (
              'Cancel'
            )}
          </BaseButton>
          <div className="w-full md:w-fit flex gap-[10px]">
            <BaseButton
              onClick={onRejectClick}
              className="flex-1 md:flex-0 flex items-center justify-center gap-1 h-10 w-[95px] bg-white border border-[#EA2B1F] text-[#EA2B1F] shadow-none hover:bg-white hover:opacity-80"
            >
              <XIcon /> Reject
            </BaseButton>
            <BaseButton
              onClick={onApproveClick}
              className="flex-1 md:flex-0 flex items-center justify-center gap-1 h-10 w-[95px] border-none text-white shadow-none hover:opacity-80"
            >
              <CheckIcon />
              Approve
            </BaseButton>
          </div>
        </div>
      )}

      {hasRows && (
        <PaginationComp
          page={page}
          totalEntries={TOTAL_ENTRIES}
          pageSize={PAGE_SIZE}
          onPageChange={(p) => {
            setPage(p);
            setPageSelection({});
          }}
          isMobile={isMobile}
          hideSummary
        />
      )}

      {confirmStatus && (
        <ModalConfirmation
          open={confirmOpen}
          onOpenChange={(o) => {
            setConfirmOpen(o);
            if (!o) setConfirmStatus(null);
          }}
          tone={confirmStatus}
          title={
            confirmStatus === 'reject' ? 'Reject Keyword?' : 'Approve Keyword?'
          }
          description={
            confirmStatus === 'reject'
              ? 'Keyword yang dipilih akan ditolak dan tidak dipublikasikan. Tindakan ini tidak dapat dibatalkan.'
              : 'Keyword yang dipilih akan disetujui dan dipublikasikan. Setelah disetujui, keyword ini akan terlihat oleh semua pengguna.'
          }
          onConfirm={handleConfirm}
          onCancel={() => {}}
        />
      )}

      {resultStatus && (
        <ModalNotification
          open={resultOpen}
          onOpenChange={(o) => {
            setResultOpen(o);
            if (!o) setResultStatus(null);
          }}
          variant={resultStatus === 'approve' ? 'success' : 'error'}
          title={
            resultStatus === 'approve'
              ? 'Keyword Approved'
              : 'Keyword berhasil ditolak'
          }
          description={
            resultStatus === 'approve'
              ? 'Keyword sudah dipublikasikan dan kini tersedia untuk semua pengguna.'
              : 'Keyword sudah diarsipkan dan tidak akan ditampilkan kepada pengguna.'
          }
          onConfirm={() => setResultOpen(false)}
        />
      )}
    </div>
  );
}
