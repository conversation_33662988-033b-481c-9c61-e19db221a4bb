'use client';

import React, { useMemo, useState } from 'react';
import AccPediaTable, { AccPediaRow } from './acc-pedia-table';
import AccPediaDetail from './acc-pedia-detail';

export default function AccPediaWrapper({
  rows,
}: Readonly<{ rows: AccPediaRow[] }>) {
  const data = useMemo(() => rows ?? [], [rows]);
  const [selected, setSelected] = useState<AccPediaRow | null>(null);

  if (selected) {
    return (
      <AccPediaDetail
        title={selected.title}
        updatedAt={selected.createdAt}
        description={`Entri untuk keyword "${selected.keyword}" yang dibuat oleh ${selected.createdBy}.`}
        example="Gunakan indikator ini sebagai preferensi dealer terhadap ACC dibanding kompetitor di area terkait."
        formula="MSCP = (DO ACC) / (Total DO Kredit)"
        otherInfos={[{ label: 'MSCP' }, { label: 'MSCP Others' }]}
        references={[]}
        similarKeywords={[
          'Project Collaboration',
          'Operational Excellence',
          'Sales Credit',
        ]}
        onBack={() => setSelected(null)}
      />
    );
  }

  return (
    <AccPediaTable
      rows={data}
      onView={(row) => setSelected(row)}
      onMore={() => {}}
    />
  );
}
