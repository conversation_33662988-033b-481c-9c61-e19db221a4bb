'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogFooter,
  BaseDialogTitle,
  BaseDialogDescription,
} from '@/components/atoms/dialog';
import { IconQuestionNoRound } from '@/assets/icons/IconQuestionNoRound';
import { BaseButton } from '@/components/atoms/button';

type Tone = 'approve' | 'reject';

export type ModalConfirmationProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tone: Tone;
  title: string;
  description: string;
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm: () => void;
  onCancel: () => void;
  disableOutsideClose?: boolean;
  footerClassName?: string;
  className?: string;
};

export default function ModalConfirmation({
  open,
  onOpenChange,
  tone,
  title,
  description,
  confirmLabel,
  cancelLabel = 'Cancel',
  onConfirm,
  onCancel,
  disableOutsideClose,
  footerClassName,
  className,
}: Readonly<ModalConfirmationProps>) {
  const isApprove = tone === 'approve';

  const confirmText = confirmLabel ?? (isApprove ? 'Approve' : 'Reject');
  const confirmBg = isApprove ? 'bg-[#F7941E]' : 'bg-[#EE4F45]';

  return (
    <BaseDialog
      open={open}
      onOpenChange={onOpenChange}
    >
      <BaseDialogContent
        className={cn(
          'md:min-w-[576px] max-w-[640px] rounded-2xl p-5 pb-0',
          'shadow-xl border border-[#EAEAEA] bg-white',
          className
        )}
        disableOutsideClose={disableOutsideClose}
        showCloseButton={false}
      >
        <BaseDialogHeader className="space-y-4">
          <div
            className={`flex h-[56px] w-[56px] items-center justify-center rounded-full`}
            style={{
              color: '#F7941E',
              backgroundColor: '#F7941E1A',
            }}
          >
            <div
              className="flex items-center justify-center w-[42px] h-[42px] rounded-full"
              style={{ backgroundColor: '#F7941E1A' }}
            >
              <IconQuestionNoRound color="#F7941E" />
            </div>
          </div>

          <div className="space-y-3">
            <BaseDialogTitle className="text-xl text-[#3C3C3C] font-medium">
              {title}
            </BaseDialogTitle>

            <BaseDialogDescription className="text-sm text-[#767676]">
              {description}
            </BaseDialogDescription>
          </div>
        </BaseDialogHeader>

        <BaseDialogFooter
          className={cn(
            'py-4 flex w-full items-center justify-end gap-3',
            footerClassName
          )}
        >
          <BaseButton
            type="button"
            onClick={() => {
              onCancel();
              onOpenChange(false);
            }}
            className="flex items-center justify-center h-11 w-[141px] bg-white border border-[#DEDEDE] text-[#3C3C3C] shadow-none hover:opacity-80 hover:bg-white"
          >
            {cancelLabel}
          </BaseButton>
          <BaseButton
            type="button"
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
            className={cn(
              'flex items-center justify-center h-11 w-[141px] border-none text-white shadow-none hover:opacity-80',
              confirmBg
            )}
          >
            {confirmText}
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  );
}
