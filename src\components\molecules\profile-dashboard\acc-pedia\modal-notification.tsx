'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogFooter,
  BaseDialogTitle,
  BaseDialogDescription,
} from '@/components/atoms/dialog';
import { BaseButton } from '@/components/atoms/button';
import { IconCheck } from '@/assets/icons/IconCheck';
import { IconXMark } from '@/assets/icons/IconXMark';

type Variant = 'success' | 'error';

const colors = {
  success: { accent: '#2BBD711A', icon: '#16a34a', border: '#2BBD711A' },
  error: { accent: '#FF72691A', icon: '#EE4F45', border: '#EE4F451A' },
};

export type ModalNotificationProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  variant: Variant;
  title: string;
  description: string;
  confirmLabel?: string;
  onConfirm: () => void;
  disableOutsideClose?: boolean;
  footerClassName?: string;
  className?: string;
};

export default function ModalNotification({
  open,
  onOpenChange,
  variant,
  title,
  description,
  confirmLabel = 'Close',
  onConfirm,
  disableOutsideClose,
  footerClassName,
  className,
}: Readonly<ModalNotificationProps>) {
  const c = colors[variant];

  return (
    <BaseDialog
      open={open}
      onOpenChange={onOpenChange}
    >
      <BaseDialogContent
        className={cn(
          'md:min-w-[576px] max-w-[640px] rounded-2xl p-5 pb-0',
          'shadow-xl border border-[#EAEAEA] bg-white',
          className
        )}
        disableOutsideClose={disableOutsideClose}
        showCloseButton={false}
      >
        <BaseDialogHeader className="space-y-5">
          <div
            className={`flex h-[56px] w-[56px] items-center justify-center rounded-full`}
            style={{
              color: c.icon,
              backgroundColor: c.accent,
            }}
          >
            <div
              className="flex items-center justify-center w-[42px] h-[42px] rounded-full"
              style={{ backgroundColor: c.border }}
            >
              {variant === 'success' ? (
                <IconCheck
                  size={23}
                  color={c.icon}
                />
              ) : (
                <IconXMark
                  size={23}
                  color={c.icon}
                />
              )}
            </div>
          </div>

          <div className="space-y-3">
            <BaseDialogTitle className="text-xl text-[#3C3C3C] font-medium">
              {title}
            </BaseDialogTitle>

            <BaseDialogDescription className="text-sm text-[#767676]">
              {description}
            </BaseDialogDescription>
          </div>
        </BaseDialogHeader>

        <BaseDialogFooter
          className={cn(
            'py-4 flex w-full items-center justify-end gap-3',
            footerClassName
          )}
        >
          <BaseButton
            type="button"
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
            className="flex items-center justify-center h-11 w-[141px] border-none text-white shadow-none hover:opacity-80"
          >
            {confirmLabel}
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  );
}
