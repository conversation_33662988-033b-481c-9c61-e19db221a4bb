'use client';

import * as React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AccPediaRow } from './acc-pedia-table';

export default function RowActionMenu({
  row,
  onApprove,
  onReject,
  className,
}: Readonly<{
  row: AccPediaRow;
  onApprove: (row: AccPediaRow) => void;
  onReject: (row: AccPediaRow) => void;
  className?: string;
}>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          type="button"
          aria-label="More"
          className={cn(
            'px-3 py-5 rounded-full hover:bg-[#F3F3F3] cursor-pointer',
            className
          )}
        >
          <MoreVertical
            size={16}
            className="text-[#343330]"
          />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        sideOffset={6}
        className={cn(
          'min-w-[170px] rounded-md border border-[#DEDEDE] bg-white shadow-lg py-2 px-0',
          'overflow-hidden'
        )}
      >
        <DropdownMenuItem
          onClick={() => onApprove(row)}
          className={cn(
            'px-4 py-3 cursor-pointer',
            'text-sm text-[#3EB72E] focus:text-[#3EB72E] focus:bg-transparent'
          )}
        >
          Approve
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onReject(row)}
          className={cn(
            'px-4 py-3 cursor-pointer',
            'text-sm text-[#F0374B] focus:text-[#F0374B] focus:bg-transparent'
          )}
        >
          Reject
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
