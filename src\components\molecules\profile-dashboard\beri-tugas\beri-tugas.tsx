'use client';

import React, { useMemo, useState } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import { cn } from '@/lib/utils';
import SelectModuleDialog, { ModuleItem } from './select-module-dialog';
import SumberLainDialog, { OtherSource } from './sumber-lain-dialog';
import { ChevronDown, CalendarDays, Plus } from 'lucide-react';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseTextarea } from '@/components/atoms/textarea';
import { OrangeCheckbox } from '@/components/atoms/checkbox/orange-checkbox';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

type BeriTugasProps = { className?: string };

type RowItem =
  | { type: 'module'; id: string; material: string; source: string }
  | { type: 'source'; id: string; material: string; source: string };

const ASSIGNMENT_OPTIONS = [
  { value: 'alc', label: 'ALC Development' },
  { value: 'idp', label: 'IDP Task' },
  { value: 'module', label: 'Module Task' },
  { value: 'project', label: 'Project / Case' },
] as const;

type AssignmentType = (typeof ASSIGNMENT_OPTIONS)[number]['value'];

export default function BeriTugas({ className }: Readonly<BeriTugasProps>) {
  const [openSelect, setOpenSelect] = useState(false);
  const [selectedModules, setSelectedModules] = useState<ModuleItem[]>([]);

  const [openSourceDlg, setOpenSourceDlg] = useState(false);
  const [otherSources, setOtherSources] = useState<OtherSource[]>([]);
  const canAddSource = otherSources.length < 3;

  const rows: RowItem[] = useMemo(() => {
    const modRows: RowItem[] = selectedModules.map((m) => {
      const [left, right] = m.title.split(' - ');
      return {
        type: 'module',
        id: m.id,
        material: left ?? m.title,
        source: right ?? '-',
      };
    });
    const srcRows: RowItem[] = otherSources.map((s) => ({
      type: 'source',
      id: s.id,
      material: s.name,
      source: s.url,
    }));
    return [...modRows, ...srcRows];
  }, [selectedModules, otherSources]);

  const handleDeleteRow = (row: RowItem) => {
    if (row.type === 'module')
      setSelectedModules((prev) => prev.filter((m) => m.id !== row.id));
    else setOtherSources((prev) => prev.filter((s) => s.id !== row.id));
  };

  const [assignmentType, setAssignmentType] = useState<AssignmentType | ''>('');

  const [dueDate, setDueDate] = useState<Date | null>(null);
  const [openDue, setOpenDue] = useState(false);
  const dueText = dueDate ? format(dueDate, 'dd/MM/yyyy') : '';

  return (
    <>
      <div className={cn('w-full bg-white flex flex-col gap-4', className)}>
        <p className="text-sm font-semibold text-[#3C3C3C]">User Information</p>

        <div className="flex items-center gap-5">
          <div className="space-y-1">
            <BaseLabel
              className="text-xs font-medium text-[#3C3C3C]"
              htmlFor="npk"
            >
              NPK
            </BaseLabel>
            <BaseInput
              id="npk"
              placeholder="12345"
              disabled
              className="text-sm h-11 bg-[#F5F5F5]! text-[#B1B1B1] cursor-not-allowed! border border-[#DEDEDE]"
            />
          </div>
          <div className="w-full space-y-1">
            <BaseLabel
              className="text-xs font-medium text-[#3C3C3C]"
              htmlFor="nama"
            >
              Nama Staff
            </BaseLabel>
            <BaseInput
              id="nama"
              placeholder="Tulis Nama"
              className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
            />
          </div>
        </div>

        <div className="rounded-lg bg-[#FBFBFB] p-3">
          <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div className="flex-1">
              <BaseLabel
                htmlFor="modsel"
                className="text-xs font-medium text-[#3C3C3C]"
              >
                Pilih Module & Section
              </BaseLabel>
              <button
                id="modsel"
                type="button"
                onClick={() => setOpenSelect(true)}
                className={cn(
                  'w-full h-11 rounded-[8px] border border-[#DEDEDE] bg-white',
                  'px-3 text-sm text-left flex items-center justify-between cursor-pointer'
                )}
              >
                <span
                  className={cn(
                    'truncate',
                    selectedModules.length === 0 && 'text-[#B1B1B1]'
                  )}
                >
                  Select Option
                </span>
                <ChevronDown
                  size={16}
                  color="#3C3C3C"
                />
              </button>
            </div>

            <div className="flex flex-col gap-1 justify-start">
              <span className="text-xs font-medium text-[#3C3C3C]">
                Sumber Lain <span className="text-[#B1B1B1]">(Max. 3)</span>
              </span>
              <BaseButton
                type="button"
                disabled={!canAddSource}
                onClick={() => setOpenSourceDlg(true)}
                className={cn(
                  'h-11 w-full md:w-[150px] rounded-md text-sm font-medium',
                  'bg-white text-[#F7941E] border border-[#F7941E] hover:bg-white hover:opacity-80',
                  !canAddSource && 'opacity-50 cursor-not-allowed'
                )}
              >
                <Plus
                  size={14}
                  className="mr-1"
                />
                Tambah
              </BaseButton>
            </div>
          </div>

          {rows.length > 0 && (
            <div className="mt-3 space-y-2 overflow-x-auto">
              <div className="py-4 flex items-center rounded-md border border-[#E5E7EB] bg-white px-2">
                <div className="w-full grid grid-cols-[40px_1fr_1fr] md:grid-cols-[40px_1fr_1fr] items-center text-xs font-semibold text-[#3C3C3C]">
                  <span className="px-3">No</span>
                  <span className="px-3">Module/Material</span>
                  <span className="px-3">Source</span>
                </div>
              </div>

              {rows.map((r, idx) => (
                <div
                  key={`${r.type}-${r.id}`}
                  className="py-4 flex items-center rounded-md border border-[#E5E7EB] bg-white px-2"
                  onClick={() => handleDeleteRow(r)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleDeleteRow(r);
                    }
                  }}
                >
                  <div className="w-full grid grid-cols-[40px_1fr_1fr] md:grid-cols-[40px_1fr_1fr] items-center text-xs text-[#3C3C3C]">
                    <span className="px-3">{idx + 1}.</span>
                    <span className="px-3">{r.material}</span>
                    <span className="px-3">{r.source}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="space-y-1">
          <BaseLabel
            className="text-xs font-medium text-[#3C3C3C]"
            htmlFor="assignment-type"
          >
            Assignment Type
          </BaseLabel>

          <BaseSelect
            value={assignmentType}
            onValueChange={(v: AssignmentType) => setAssignmentType(v)}
          >
            <BaseSelectTrigger
              id="assignment-type"
              chevronClassName="text-[#3C3C3C] opacity-100"
              className={cn(
                'w-full h-11 rounded-md border border-[#DEDEDE] bg-white',
                'px-4 text-sm text-[#767676] justify-between cursor-pointer shadow-none'
              )}
            >
              <BaseSelectValue placeholder="Select Type" />
            </BaseSelectTrigger>

            <BaseSelectContent className="py-2 px-0!">
              {ASSIGNMENT_OPTIONS.map((opt) => (
                <BaseSelectItem
                  key={opt.value}
                  value={opt.value}
                  className="text-[#3C3C3C] font-normal text-sm px-4 py-3 cursor-pointer"
                >
                  {opt.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        <div className="space-y-1">
          <BaseLabel
            className="text-xs font-medium text-[#3C3C3C]"
            htmlFor="instruksi"
          >
            Instruksi Belajar
          </BaseLabel>
          <BaseTextarea
            id="instruksi"
            placeholder="Tuliskan Instruksi"
            className="text-sm h-[130px] rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
          />
        </div>

        <div className="flex items-center py-2 gap-2 select-none">
          <OrangeCheckbox id="is-idp" />
          <BaseLabel
            htmlFor="is-idp"
            className="text-xs text-[#3C3C3C] cursor-pointer"
          >
            Tugas Individual Development Program
          </BaseLabel>
        </div>

        <div className="mt-1 space-y-1">
          <BaseLabel
            className="text-xs text-[#3C3C3C] cursor-pointer"
            htmlFor="due"
          >
            Batas Pengumpulan
          </BaseLabel>

          <Popover
            open={openDue}
            onOpenChange={setOpenDue}
          >
            <PopoverTrigger asChild>
              <button
                type="button"
                className="w-full relative"
              >
                <BaseInput
                  id="due"
                  placeholder="Select Date"
                  value={dueText}
                  readOnly
                  className={cn(
                    'text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!',
                    dueDate ? 'text-[#767676]' : 'text-[#B1B1B1]',
                    className
                  )}
                />
                <CalendarDays
                  size={16}
                  color="#767676"
                  className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none"
                />
              </button>
            </PopoverTrigger>

            <PopoverContent
              align="end"
              className="w-auto p-0 bg-white"
            >
              <Calendar
                mode="single"
                selected={dueDate || undefined}
                onSelect={(d) => {
                  if (d) {
                    setDueDate(d);
                    setOpenDue(false);
                  }
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <SelectModuleDialog
        open={openSelect}
        onClose={() => setOpenSelect(false)}
        initialSelected={selectedModules}
        onApply={(sel) => setSelectedModules(sel)}
      />
      <SumberLainDialog
        open={openSourceDlg}
        onClose={() => setOpenSourceDlg(false)}
        onApply={(src) => setOtherSources((prev) => [...prev, src])}
      />
    </>
  );
}
