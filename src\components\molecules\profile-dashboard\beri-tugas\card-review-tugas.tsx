'use client';

import React from 'react';
import { BaseButton } from '@/components/atoms/button';
import { cn } from '@/lib/utils';

export type CardReviewTugasProps = {
  title: string;
  givenInfo: string;
  chipLabel?: string;
  items: string[];
  question: string;
  dueDate: string;
  progress: number;
  postTestScore: number;
  buttonLabel?: string;
  onClickView?: () => void;
};

export default function CardReviewTugas({
  title,
  givenInfo,
  chipLabel,
  items,
  question,
  dueDate,
  progress,
  postTestScore,
  buttonLabel = 'Lihat',
  onClickView,
}: Readonly<CardReviewTugasProps>) {
  return (
    <div className="w-full rounded-md border border-[#DEDEDE] bg-white">
      <div className="flex flex-col gap-3 xl:flex-row xl:items-start xl:justify-between px-5 py-4 xl:py-5">
        <p className="text-base md:text-lg font-bold text-[#3C3C3C]">{title}</p>
        <p className="text-xs md:text-sm text-[#767676]">{givenInfo}</p>
        {chipLabel && (
          <span
            className={cn(
              'text-xs w-fit h-fit px-2 py-[3px] rounded xl:hidden',
              'bg-[#E6F7EE] text-[#50D744]'
            )}
          >
            {chipLabel}
          </span>
        )}
      </div>

      <div className="flex flex-col gap-2 px-5">
        <p className="text-xs font-semibold text-[#3C3C3C] xl:hidden">
          List Materi Pembelajaran Kamu:
        </p>

        <div className="flex justify-between">
          <ol className="mt-1 md:mt-3 list-decimal pl-4 text-xs text-[#3C3C3C] space-y-2">
            {items.map((it) => (
              <li key={it}>{it}</li>
            ))}
          </ol>
          {chipLabel && (
            <span
              className={cn(
                'text-xs w-fit h-fit px-2 py-[3px] rounded hidden xl:block',
                'bg-[#E6F7EE] text-[#50D744]'
              )}
            >
              {chipLabel}
            </span>
          )}
        </div>
      </div>

      <div className="text-xs text-[#F7941E] p-5 pb-4 xl:pt-[14px]">{`"${question}"`}</div>

      <div className="border-t border-[#DEDEDE]" />

      <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-3 p-4 xl:p-5">
        <div className="grid grid-cols-2 xl:grid-cols-3 gap-4 xl:gap-6 w-full xl:w-fit">
          <div className="col-span-2 xl:col-span-1 space-y-2">
            <span className="block text-xs text-[#B1B1B1]">
              Waktu Pengumpulan
            </span>
            <span className="text-sm font-medium text-[#2BBD71]">
              {dueDate}
            </span>
          </div>

          <div className="xl:order-none order-1 space-y-2">
            <span className="block text-xs text-[#B1B1B1]">
              Post-test Score
            </span>
            <span className="block text-sm font-medium text-[#3C3C3C]">
              {postTestScore}
            </span>
          </div>

          <div className="xl:order-none order-2 space-y-2">
            <span className="block text-xs text-[#B1B1B1]">Progress</span>
            <span className="block text-sm font-medium text-[#3C3C3C]">
              {progress}%
            </span>
          </div>
        </div>

        <BaseButton
          type="button"
          className={cn(
            'rounded-md font-medium bg-[#F7941E] text-white',
            'border-none shadow-none hover:opacity-90',
            'h-12 w-full xl:w-[124px] '
          )}
          onClick={onClickView}
        >
          {buttonLabel}
        </BaseButton>
      </div>
    </div>
  );
}
