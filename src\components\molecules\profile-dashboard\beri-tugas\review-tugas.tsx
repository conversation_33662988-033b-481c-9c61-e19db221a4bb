'use client';

import React, { useMemo, useState } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { cn } from '@/lib/utils';
import { formatDateID } from '@/hooks';
import CardReviewTugas, { CardReviewTugasProps } from './card-review-tugas';

type TabKey = 'terkirim' | 'butuh-review' | 'selesai';
const TABS: { key: TabKey; label: string }[] = [
  { key: 'terkirim', label: 'Terkirim' },
  { key: 'butuh-review', label: 'Butuh Review' },
  { key: 'selesai', label: 'Seles<PERSON>' },
];

type ReviewItem = {
  id: string;
  name: string;
  npk: string;
  givenBy: string;
  givenAt: string;
  dueDate: string;
  items: string[];
  question: string;
  progress: number;
  postTestScore: number;
  hasIdp?: boolean;
};

const BASE_ITEMS = [
  'Client Engagement Strategies',
  'Customer Experience Enhancement',
  'Client Relationship Management',
  'Customer Loyalty Programs',
  'Client Satisfaction Initiatives',
];

const makeId = (prefix: string, i: number) =>
  `${prefix}-${i.toString().padStart(3, '0')}`;

const IDP_INDEXES: Record<TabKey, number[]> = {
  terkirim: [1, 4, 7, 12, 18],
  'butuh-review': [2, 5, 9],
  selesai: [3, 6, 10, 15],
};

function buildDummyForTab(tab: TabKey, count: number): ReviewItem[] {
  const metaByTab: Record<
    TabKey,
    {
      givenBy: string;
      givenAt: string;
      dueDate: string;
      progress: number;
      score: number;
    }
  > = {
    terkirim: {
      givenBy: 'Nadya Fitri',
      givenAt: '2024-04-02',
      dueDate: '2024-05-10',
      progress: 70,
      score: 95,
    },
    'butuh-review': {
      givenBy: 'Sultan Edwa',
      givenAt: '2024-04-12',
      dueDate: '2024-05-12',
      progress: 80,
      score: 100,
    },
    selesai: {
      givenBy: 'Rama Wijaya',
      givenAt: '2024-03-22',
      dueDate: '2024-04-28',
      progress: 100,
      score: 100,
    },
  };

  const meta = metaByTab[tab];
  const withIdp = new Set(IDP_INDEXES[tab] || []);
  const res: ReviewItem[] = [];

  for (let i = 1; i <= count; i++) {
    res.push({
      id: makeId(tab, i),
      name: 'Sultan Edwa',
      npk: `99${(800 + i).toString().padStart(3, '0')}`,
      givenBy: meta.givenBy,
      givenAt: meta.givenAt,
      dueDate: meta.dueDate,
      items: BASE_ITEMS,
      question: 'Sebutkan nama hewan yang hidungnya dibelakang!',
      progress: meta.progress,
      postTestScore: meta.score,
      hasIdp: withIdp.has(i),
    });
  }
  return res;
}

const DATA_TERKIRIM = buildDummyForTab('terkirim', 21);
const DATA_BUTUH_REVIEW = buildDummyForTab('butuh-review', 13);
const DATA_SELESAI = buildDummyForTab('selesai', 17);

const PAGE_SIZE = 10;

export default function ReviewTugas() {
  const [tab, setTab] = useState<TabKey>('terkirim');
  const [visible, setVisible] = useState<Record<TabKey, number>>({
    terkirim: PAGE_SIZE,
    'butuh-review': PAGE_SIZE,
    selesai: PAGE_SIZE,
  });

  const dataByTab = useMemo<Record<TabKey, ReviewItem[]>>(
    () => ({
      terkirim: DATA_TERKIRIM,
      'butuh-review': DATA_BUTUH_REVIEW,
      selesai: DATA_SELESAI,
    }),
    []
  );

  const currentList = dataByTab[tab];
  const visibleItems = useMemo(
    () => currentList.slice(0, visible[tab]),
    [currentList, visible, tab]
  );

  const canLoadMore = visible[tab] < currentList.length;
  const loadMore = () =>
    setVisible((v) => ({
      ...v,
      [tab]: Math.min(v[tab] + PAGE_SIZE, currentList.length),
    }));

  const buttonLabel = tab === 'butuh-review' ? 'Review' : 'Lihat';

  return (
    <div className="w-full">
      <div className="overflow-hidden bg-white flex flex-col gap-4 max-h-[500px] md:max-h-[996px]">
        <div className="flex justify-between md:justify-start gap-8">
          {TABS.map((t) => {
            const active = t.key === tab;
            return (
              <BaseButton
                key={t.key}
                type="button"
                onClick={() => setTab(t.key)}
                className={cn(
                  'text-xs font-medium py-2 px-4 rounded-[8px] cursor-pointer border',
                  active
                    ? 'bg-[#F7941E] text-white border-none'
                    : 'bg-white text-[#3C3C3C] border-none shadow-none hover:bg-[#FFF3E6]'
                )}
              >
                {t.label}
              </BaseButton>
            );
          })}
        </div>

        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          <div className="space-y-4 pb-4">
            {visibleItems.map((it) => {
              const cardProps: CardReviewTugasProps = {
                title: `${it.name} - ${it.npk}`,
                givenInfo: `Diberikan oleh ${it.givenBy} pada ${formatDateID(
                  it.givenAt
                )}`,
                chipLabel: it.hasIdp
                  ? 'Individual Development Plan'
                  : undefined,
                items: it.items,
                question: it.question,
                dueDate: formatDateID(it.dueDate),
                progress: it.progress,
                postTestScore: it.postTestScore,
                buttonLabel,
                onClickView: () => alert(`${buttonLabel} tugas: ${it.id}`),
              };
              return (
                <CardReviewTugas
                  key={it.id}
                  {...cardProps}
                />
              );
            })}
          </div>

          {canLoadMore && (
            <div className="flex items-center justify-center pt-4 w-full border-t border-[#EAEAEA] bg-white">
              <BaseButton
                type="button"
                onClick={loadMore}
                className="w-fit h-fit text-xs p-0 text-[#F7941E] bg-white shadow-none border-none hover:bg-white hover:opacity-80"
              >
                Muat Lebih Banyak
              </BaseButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
