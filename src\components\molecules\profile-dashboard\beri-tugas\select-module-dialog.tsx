'use client';

import React, { useMemo, useState } from 'react';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseButton } from '@/components/atoms/button';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import { cn } from '@/lib/utils';
import { InfoIcon, XIcon } from 'lucide-react';

export type ModuleItem = {
  id: string;
  title: string;
  kind: 'Module' | 'Section';
  technical?: string[];
  soft?: string[];
};

const MAX_SELECT = 5;

export type SelectModuleDialogProps = {
  open: boolean;
  onClose: () => void;
  initialSelected?: ModuleItem[];
  onApply: (selected: ModuleItem[]) => void;
  className?: string;
};

// ── dummy data
const ALL_ITEMS: ModuleItem[] = Array.from({ length: 20 }).map((_, i) => ({
  id: `mod-${i + 1}`,
  title:
    i % 2 === 0
      ? 'Leaders Eat Last - Simon <PERSON>'
      : 'The 7 Habits of Highly Effective People',
  kind: 'Module',
  technical: [],
  soft: [],
}));

export default function SelectModuleDialog({
  open,
  onClose,
  onApply,
  initialSelected = [],
  className,
}: Readonly<SelectModuleDialogProps>) {
  const [query, setQuery] = useState('');
  const [qTech, setQTech] = useState('');
  const [qSoft, setQSoft] = useState('');
  const [selected, setSelected] = useState<ModuleItem[]>(
    initialSelected.slice(0, MAX_SELECT)
  );

  const filtered = useMemo(() => {
    const q = query.toLowerCase();
    const qt = qTech.toLowerCase();
    const qs = qSoft.toLowerCase();

    return ALL_ITEMS.filter((it) => {
      const byTitle = it.title.toLowerCase().includes(q);
      const byTech =
        qt.length === 0 ||
        (it.technical ?? []).some((t) => t.toLowerCase().includes(qt));
      const bySoft =
        qs.length === 0 ||
        (it.soft ?? []).some((s) => s.toLowerCase().includes(qs));
      return byTitle && byTech && bySoft;
    }).slice(0, 50);
  }, [query, qTech, qSoft]);

  const idsSelected = useMemo(
    () => new Set(selected.map((s) => s.id)),
    [selected]
  );

  const toggleSelect = (item: ModuleItem) => {
    const isOn = idsSelected.has(item.id);
    if (isOn) setSelected((prev) => prev.filter((x) => x.id !== item.id));
    else
      setSelected((prev) =>
        prev.length >= MAX_SELECT ? prev : [...prev, item]
      );
  };

  const canApply = selected.length > 0;

  return (
    <BaseDialog
      open={open}
      onOpenChange={(v) => (!v ? onClose() : null)}
    >
      <BaseDialogContent
        id="select-module-section-dialog"
        className={cn(
          'w-full max-w-[90%] md:max-w-[550px] p-0 rounded-md bg-white gap-0',
          className
        )}
        overlayClassName="#3C3C3C/70"
        showCloseButton={false}
      >
        <div className="px-5 pt-5 pb-3 flex items-center justify-between border-b border-[#DEDEDE]">
          <BaseDialogTitle className="text-base font-medium text-[#3C3C3C]">
            Pilih Module & Section
          </BaseDialogTitle>
          <button
            type="button"
            onClick={onClose}
            aria-label="Close"
            className="text-[#6B6B6B] w-6 h-6 hover:opacity-80 cursor-pointer"
          >
            <XIcon
              size={16}
              color="#767676"
            />
          </button>
        </div>

        <div className="p-5 max-h-[70vh] overflow-y-auto">
          <div className="mb-5">
            <BaseInput
              id="search-mod"
              placeholder="Masukkan nama module atau section"
              value={query}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setQuery(e.target.value)
              }
              className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-2 gap-[10px] mb-4">
            <div>
              <BaseLabel
                className="text-xs font-medium text-[#3C3C3C]"
                htmlFor="flt-tech"
              >
                Technical Competencies
              </BaseLabel>
              <BaseInput
                id="flt-tech"
                placeholder="Cari technical competencies"
                value={qTech}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setQTech(e.target.value)
                }
                className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
              />
            </div>
            <div>
              <BaseLabel
                className="text-xs font-medium text-[#3C3C3C]"
                htmlFor="flt-soft"
              >
                Soft Competencies
              </BaseLabel>
              <BaseInput
                id="flt-soft"
                placeholder="Cari soft competencies"
                value={qSoft}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setQSoft(e.target.value)
                }
                className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
              />
            </div>
          </div>

          <div className="flex flex-row gap-2 items-center text-xs text-[#717171] rounded-md border border-[#EDA958] bg-[#FFF6EC] p-4 mb-4 overflow-x-hidden">
            <InfoIcon
              size={16}
              fill="#EDA958"
              color="white"
              className="shrink-0"
            />
            <span className="truncate">
              List di bawah hanya menampilkan max. 50 data sesuai pencarian dan
              filter
            </span>
          </div>

          <div className="flex items-center justify-between mb-4">
            <p className="text-sm font-semibold text-[#3C3C3C]">
              List Module & Section
            </p>
            <p className="text-sm text-[#B1B1B1]">
              {selected.length}/{MAX_SELECT} dipilih
            </p>
          </div>

          <div className="space-y-3">
            {filtered.map((it) => {
              const active = idsSelected.has(it.id);
              const disabled = !active && selected.length >= MAX_SELECT;
              return (
                <button
                  key={it.id}
                  type="button"
                  onClick={() => toggleSelect(it)}
                  disabled={disabled}
                  className={cn(
                    'w-full text-left rounded-xl border p-4 bg-white cursor-pointer',
                    active ? 'border-[#F7941E]' : 'border-[#E5E7EB]',
                    disabled && 'opacity-60 cursor-not-allowed'
                  )}
                >
                  <p className="text-base md:text-sm font-semibold text-[#F7941E]">
                    {it.title}
                  </p>
                  <p className="text-sm text-[#B1B1B1] mt-2">{it.kind}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-[30px] text-sm text-[#717171]">
                    <div className="space-y-2">
                      <p>Technical Competencies</p>
                      <p>
                        {it.technical?.length ? it.technical.join(', ') : '-'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p>Soft Competencies</p>
                      <p>{it.soft?.length ? it.soft.join(', ') : '-'}</p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        <div className="px-5 py-4 border-t border-[#EAEAEA] flex justify-end">
          <BaseButton
            type="button"
            disabled={!canApply}
            onClick={() => {
              if (!canApply) return;
              onApply(selected);
              onClose();
            }}
            className={cn(
              'w-full md:w-fit h-11 px-4 rounded-md text-sm font-medium shadow-none border-none',
              canApply
                ? 'bg-[#F7941E] text-white hover:opacity-90'
                : 'bg-[#FFE3C4] text-white cursor-not-allowed'
            )}
          >
            Apply
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
}
