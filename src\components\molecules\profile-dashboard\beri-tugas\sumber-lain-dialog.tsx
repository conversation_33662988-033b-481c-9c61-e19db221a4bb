'use client';

import React, { useMemo, useState } from 'react';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseButton } from '@/components/atoms/button';
import { cn } from '@/lib/utils';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import { XIcon } from 'lucide-react';

export type OtherSource = {
  id: string;
  name: string;
  url: string;
};

export type SumberLainDialogProps = {
  open: boolean;
  onClose: () => void;
  onApply: (source: OtherSource) => void;
  className?: string;
};

export default function SumberLainDialog({
  open,
  onClose,
  onApply,
  className,
}: Readonly<SumberLainDialogProps>) {
  const [name, setName] = useState('');
  const [url, setUrl] = useState('');

  const isValidUrl = (u: string) => {
    try {
      const x = new URL(u.startsWith('http') ? u : `https://${u}`);
      return !!x.hostname;
    } catch {
      return false;
    }
  };

  const canApply = useMemo(() => {
    return name.trim().length > 0 && isValidUrl(url.trim());
  }, [name, url]);

  const handleApply = () => {
    if (!canApply) return;
    const normalized = url.startsWith('https://') ? url : `https://${url}`;
    onApply({
      id: crypto.randomUUID(),
      name: name.trim(),
      url: normalized.trim(),
    });
    setName('');
    setUrl('');
    onClose();
  };

  return (
    <BaseDialog
      open={open}
      onOpenChange={(v) => (!v ? onClose() : null)}
    >
      <BaseDialogContent
        className={cn(
          'w-full max-w-[90%] md:max-w-[550px] p-0 rounded-md bg-white gap-5',
          className
        )}
        overlayClassName="#3C3C3C/70"
        showCloseButton={false}
      >
        <div className="px-5 py-4 flex items-center justify-between border-b border-[#DEDEDE]">
          <BaseDialogTitle className="text-base font-medium text-[#3C3C3C]">
            Tambah Sumber Lain
          </BaseDialogTitle>
          <button
            type="button"
            onClick={onClose}
            aria-label="Close"
            className="text-[#6B6B6B] w-6 h-6 hover:opacity-80"
          >
            <XIcon
              size={16}
              color="#767676"
            />
          </button>
        </div>

        <div className="px-5">
          <div className="space-y-5">
            <div className="space-y-1">
              <BaseLabel
                className="text-xs font-medium text-[#3C3C3C]"
                htmlFor="nama-tugas"
              >
                Nama Tugas
              </BaseLabel>
              <BaseInput
                id="nama-tugas"
                placeholder="Masukkan Nama Tugas"
                value={name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setName(e.target.value)
                }
                className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
              />
            </div>

            <div className="space-y-1">
              <BaseLabel
                className="text-xs font-medium text-[#3C3C3C]"
                htmlFor="link-tugas"
              >
                Link Tugas
              </BaseLabel>
              <BaseInput
                id="link-tugas"
                placeholder="Masukkan URL Tugas (i.e: www.example.com or https://example.com)"
                value={url}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setUrl(e.target.value)
                }
                className="text-sm h-11 rounded-md shadow-none focus:outline-none! focus:ring-0! focus:border-[#DEDEDE]!"
              />
            </div>
          </div>
        </div>

        <div className="px-5 py-4 border-t border-[#EAEAEA] flex justify-end">
          <BaseButton
            type="button"
            disabled={!canApply}
            onClick={handleApply}
            className={cn(
              'w-full md:w-fit h-11 px-4 rounded-md text-sm font-medium shadow-none border-none',
              canApply
                ? 'bg-[#F7941E] text-white hover:opacity-90'
                : 'bg-[#FFE3C4] text-white cursor-not-allowed'
            )}
          >
            Add Source
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
}
