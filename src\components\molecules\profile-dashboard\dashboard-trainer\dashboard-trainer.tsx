'use client';

import React, { useState } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { IconExport } from '@/assets/icons/IconExport';
import ProfileDashboardTrainer from './profile-dashboard-trainer';
import TrainingDetailPanel, { TrainingItem } from './detail-training/training-detail-panel';
import UpcomingTrainingTable, { UpcomingRow } from './upcoming-training-table';
import TrainingHistoryTable, { HistoryRow } from './training-history-table';

const DashboardTrainer: React.FC = () => {
  const [selectedTraining, setSelectedTraining] = useState<TrainingItem | null>(
    null
  );
  const [hasSummary, setHasSummary] = useState(false);

  const handleViewUpcoming = (row: UpcomingRow) => {
    setSelectedTraining({
      id: row.id,
      date: row.date,
      durationMin: row.durationMin,
      participants: row.participants,
      module: row.module,
    });
    setHasSummary(false);
  };
  const handleViewHistory = (row: HistoryRow) => {
    setSelectedTraining({
      id: row.id,
      date: row.date,
      durationMin: row.durationMin,
      participants: row.participants,
      module: row.module,
    });
    setHasSummary(true);
  };

  const handleBack = () => {
    setSelectedTraining(null);
  };

  return (
    <div className="flex flex-col gap-4">
      {selectedTraining ? (
        <TrainingDetailPanel
          training={selectedTraining}
          onBack={handleBack}
          summary={hasSummary}
        />
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="text-xl font-bold text-[#3C3C3C] py-3">
              Dashboard Trainer
            </p>
            <BaseButton className="w-fit flex gap-1 items-center h-11 px-4 border border-[#DEDEDE] bg-white hover:bg-white hover:opacity-80">
              <IconExport color="#3C3C3C" />{' '}
              <span className="hidden md:block text-sm text-[#3C3C3C] font-medium">
                Export File
              </span>
            </BaseButton>
          </div>

          <div className="flex flex-col gap-6">
            <ProfileDashboardTrainer />

            <UpcomingTrainingTable onView={handleViewUpcoming} />
            <TrainingHistoryTable onView={handleViewHistory} />
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardTrainer;
