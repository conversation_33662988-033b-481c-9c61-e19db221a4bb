'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  CellContext,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { Triangle } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import NoDataTable from '@/assets/images/no-found-data.png';

export type FeedbackRow = {
  no: number;
  response: string;
  date: Date;
};

type Props = {
  answered: number;
  skipped: number;
  className?: string;
  rows?: FeedbackRow[];
  title?: string;
};

const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
    />
  </div>
);

const fmtDate = (d: Date) =>
  new Date(d).toLocaleString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

type Ctx = Readonly<CellContext<FeedbackRow, unknown>>;

function NoCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.row.original.no ?? ''}</span>;
}
function ResponseCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.row.original.response}</span>;
}
function DateCell(ctx: Ctx) {
  return (
    <span className="text-[#3C3C3C] font-medium">
      {fmtDate(ctx.row.original.date)}
    </span>
  );
}

export default function OpinionAndFeedback({
  answered = 0,
  skipped = 0,
  className,
  rows,
  title = 'Opinion and Feedback for Trainer',
}: Readonly<Props>) {
  const data = useMemo<FeedbackRow[]>(() => rows ?? [], [rows]);

  const [sorting, setSorting] = useState<SortingState>([]);

  const columns = useMemo<ColumnDef<FeedbackRow>[]>(() => {
    return [
      {
        id: 'no',
        header: 'No#',
        accessorFn: (r) => r.no,
        sortingFn: 'basic',
        cell: NoCell,
        size: 80,
      },
      {
        id: 'response',
        header: 'Responses',
        accessorFn: (r) => r.response,
        sortingFn: 'alphanumeric',
        cell: ResponseCell,
        size: 400,
      },
      {
        id: 'date',
        header: 'Date',
        accessorFn: (r) => r.date.getTime(),
        sortingFn: 'basic',
        cell: DateCell,
        size: 220,
      },
    ];
  }, []);

  const table = useReactTable({
    data,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const hasRows = data.length > 0;

  return (
    <div className={cn('w-full h-full flex flex-col gap-6', className)}>
      <div className="flex flex-col items-center gap-3">
        <p className="text-sm md:text-base font-medium">{title}</p>
        <div className="flex flex-row gap-6 items-center justify-center text-sm text-[#717171] text-center">
          <span>Answered: {answered}</span>
          <span>Skipped: {skipped}</span>
        </div>
      </div>

      <div className="rounded-xl bg-white border border-[#DEDEDE] p-1">
        <div className="w-full overflow-x-auto">
          <div className="max-h-[450px] overflow-y-auto pr-1">
            <table className="w-full border-collapse">
              <thead>
                {table.getHeaderGroups().map((hg) => (
                  <tr key={hg.id}>
                    {hg.headers.map((header) => {
                      const canSort = header.column.getCanSort();
                      const sortDir = header.column.getIsSorted();
                      const width =
                        header.column.id === 'response'
                          ? 'min-w-[200px] md:min-w-[320px]'
                          : 'min-w-[120px]';

                      return (
                        <th
                          key={header.id}
                          onClick={
                            canSort
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                          className={cn(
                            'py-4 px-3 font-medium text-left text-xs text-[#3C3C3C] select-none sticky top-0 bg-white',
                            canSort && 'cursor-pointer',
                            width
                          )}
                          style={{ zIndex: 1 }}
                        >
                          <div className="flex items-center gap-1">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            <IconSort
                              direction={
                                sortDir === 'asc' || sortDir === 'desc'
                                  ? sortDir
                                  : undefined
                              }
                            />
                          </div>
                        </th>
                      );
                    })}
                  </tr>
                ))}
              </thead>

              <tbody className="text-xs text-[#3C3C3C]">
                {hasRows ? (
                  table.getRowModel().rows.map((row) => (
                    <tr
                      key={row.id}
                      className="odd:bg-[#FAFAFA] even:bg-white"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <td
                          key={cell.id}
                          className="py-5 px-3 align-top"
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={table.getAllLeafColumns().length}
                      className="py-14"
                    >
                      <div className="flex flex-col items-center justify-center gap-3">
                        <Image
                          src={NoDataTable}
                          alt="no data table"
                          width={560}
                          height={500}
                          className="max-w-[140px] max-h-[125px]"
                        />
                        <div className="flex flex-col gap-1">
                          <p className="text-[#3C3C3C] text-sm font-medium">
                            No data
                          </p>
                          <p className="text-[#767676] text-[10px] leading-[14px]">
                            Come again later
                          </p>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
