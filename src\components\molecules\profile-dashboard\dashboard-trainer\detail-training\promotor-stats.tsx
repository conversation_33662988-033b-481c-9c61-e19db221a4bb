'use client';

import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Triangle } from 'lucide-react';

const IconSortNeutral = () => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill="#EBEBEB"
      color="#EBEBEB"
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill="#EBEBEB"
      color="#EBEBEB"
    />
  </div>
);

type LabelProps = { children: React.ReactNode };
const HeaderLabel: React.FC<LabelProps> = ({ children }) => (
  <div className="flex items-center gap-1 text-xs font-semibold text-[#3C3C3C]">
    <span>{children}</span>
  </div>
);

type StatProps = { children: React.ReactNode };
const Stat: React.FC<StatProps> = ({ children }) => (
  <div className="text-[13px] leading-5 text-[#1E1E1E]">{children}</div>
);

const Count: React.FC<{ value: number }> = ({ value }) => (
  <div className="text-xs text-[#6B6B6B]">{value}</div>
);

type Props = {
  className?: string;
  detractors?: number;
  passives?: number;
  promoters?: number;
  total?: number;
  npsOverride?: number;
};

export default function PromotorStats({
  className,
  detractors = 0,
  passives = 0,
  promoters = 6,
  total,
  npsOverride,
}: Readonly<Props>) {
  const computed = useMemo(() => {
    const t = total ?? detractors + passives + promoters;
    const pct = (n: number) => (t > 0 ? Math.round((n / t) * 100) : 0);

    let nps = 0;
    if (typeof npsOverride === 'number') {
      nps = Math.round(npsOverride);
    } else if (t > 0) {
      nps = Math.round(((promoters - detractors) / t) * 100);
    }

    return {
      t,
      detPct: pct(detractors),
      pasPct: pct(passives),
      proPct: pct(promoters),
      nps,
    };
  }, [detractors, passives, promoters, total, npsOverride]);

  return (
    <div className={cn('flex flex-col gap-3 lg:gap-5', className)}>
      <div className="transition-all duration-300 overflow-hidden max-h-[1600px] opacity-100">
        <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-1 bg-white">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="py-4 px-3 text-left select-none min-w-[160px]">
                  <div className="flex items-center gap-1">
                    <HeaderLabel>Detractors (0-6)</HeaderLabel>
                    <IconSortNeutral />
                  </div>
                </th>
                <th className="py-4 px-3 text-left select-none min-w-[160px]">
                  <div className="flex items-center gap-1">
                    <HeaderLabel>Passives (7-8)</HeaderLabel>
                    <IconSortNeutral />
                  </div>
                </th>
                <th className="py-4 px-3 text-left select-none min-w-[160px]">
                  <div className="flex items-center gap-1">
                    <HeaderLabel>Promoters (9-10)</HeaderLabel>
                    <IconSortNeutral />
                  </div>
                </th>
                <th className="py-4 px-3 text-left select-none min-w-[160px]">
                  <div className="flex items-center gap-1">
                    <HeaderLabel>Net Promoter Score</HeaderLabel>
                    <IconSortNeutral />
                  </div>
                </th>
              </tr>
            </thead>

            <tbody className="text-xs text-[#3C3C3C]">
              <tr className="bg-white">
                <td className="py-5 px-3 align-top">
                  <div className="flex flex-col gap-1">
                    <Stat>{computed.detPct}%</Stat>
                    <Count value={detractors} />
                  </div>
                </td>

                <td className="py-5 px-3 align-top">
                  <div className="flex flex-col gap-1">
                    <Stat>{computed.pasPct}%</Stat>
                    <Count value={passives} />
                  </div>
                </td>

                <td className="py-5 px-3 align-top">
                  <div className="flex flex-col gap-1">
                    <Stat>{computed.proPct}%</Stat>
                    <Count value={promoters} />
                  </div>
                </td>

                <td className="py-5 px-3 align-top">
                  <div className="flex flex-col gap-1">
                    <Stat>{computed.nps}</Stat>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
