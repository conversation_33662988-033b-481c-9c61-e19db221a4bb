'use client';

import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  CellContext,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { Triangle } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import NoDataTable from '@/assets/images/no-found-data.png';

export type QuestionRow = {
  code: string;
  question: string;
  difficulty: number;
  averageScore: number;
};

export type QuestionRankingTableProps = {
  title?: string;
  rows?: QuestionRow[];
};

const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
    />
  </div>
);

type Ctx = Readonly<CellContext<QuestionRow, unknown>>;

function QuestionCell(ctx: Ctx) {
  const row = ctx.row.original;
  return (
    <div className="flex items-start gap-3">
      <span className="shrink-0 w-8 text-[#3C3C3C] font-medium">
        {row.code}
      </span>
      <p className="text-[#3C3C3C] leading-snug">{row.question}</p>
    </div>
  );
}
function DifficultyCell(ctx: Ctx) {
  return <span className="text-[#3C3C3C]">{ctx.getValue() as number}</span>;
}
function AverageCell(ctx: Ctx) {
  const v = ctx.getValue() as number;
  return <span className="text-[#3C3C3C] font-medium">{v}%</span>;
}

export default function QuestionRankingTable({
  title = 'Question Ranking',
  rows,
}: Readonly<QuestionRankingTableProps>) {
  const allRows = useMemo<QuestionRow[]>(() => rows ?? [], [rows]);
  const TOTAL = allRows.length;

  const [sorting, setSorting] = useState<SortingState>([]);
  const columns = useMemo<ColumnDef<QuestionRow>[]>(() => {
    const leftHeader = `Question (${TOTAL})`;
    return [
      {
        id: 'question',
        header: leftHeader,
        accessorFn: (r) => r.question,
        sortingFn: 'alphanumeric',
        cell: QuestionCell,
      },
      {
        id: 'difficulty',
        header: 'Difficulty',
        accessorFn: (r) => r.difficulty,
        sortingFn: 'basic',
        cell: DifficultyCell,
      },
      {
        id: 'averageScore',
        header: 'Average Score',
        accessorFn: (r) => r.averageScore,
        sortingFn: 'basic',
        cell: AverageCell,
      },
    ];
  }, [TOTAL]);

  const table = useReactTable({
    data: allRows,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const hasRows = TOTAL > 0;

  return (
    <div className="flex flex-col gap-3 lg:gap-5">
      <div className="transition-all duration-300 overflow-hidden max-h-[1600px] opacity-100">
        <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-1">
          <table className="w-full border-collapse">
            <thead className="text-xs text-[#767676]">
              {table.getHeaderGroups().map((hg) => (
                <tr key={hg.id}>
                  {hg.headers.map((header) => {
                    const canSort = header.column.getCanSort();
                    const sortDir = header.column.getIsSorted();
                    const id = header.id;
                    const widthClass =
                      id === 'question' ? 'min-w-[280px]' : 'min-w-[140px]';

                    return (
                      <th
                        key={header.id}
                        onClick={
                          canSort
                            ? header.column.getToggleSortingHandler()
                            : undefined
                        }
                        className={cn(
                          'py-4 px-3 font-medium text-left text-xs text-[#3C3C3C] select-none',
                          canSort && 'cursor-pointer',
                          widthClass
                        )}
                      >
                        <div className="flex items-center gap-1">
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          <IconSort
                            direction={
                              sortDir === 'asc' || sortDir === 'desc'
                                ? sortDir
                                : undefined
                            }
                          />
                        </div>
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>

            <tbody className="text-xs text-[#3C3C3C]">
              {hasRows ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className="odd:bg-[#FAFAFA] even:bg-white"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="py-5 px-3 align-top"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={table.getAllLeafColumns().length}
                    className="py-14"
                  >
                    <div className="flex flex-col items-center justify-center gap-3">
                      <Image
                        src={NoDataTable}
                        alt="no data table"
                        width={560}
                        height={500}
                        className="max-w-[140px] max-h-[125px]"
                      />
                      <div className="flex flex-col gap-1">
                        <p className="text-[#3C3C3C] text-sm font-medium">
                          No data
                        </p>
                        <p className="text-[#767676] text-[10px] leading-[14px]">
                          Come again later
                        </p>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
