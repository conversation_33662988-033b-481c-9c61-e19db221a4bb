'use client';

import React, { useMemo } from 'react';
import clsx from 'clsx';
import { Triangle } from 'lucide-react';

export type QuizScoreStatsCardProps = {
  lowest: number | string;
  median: number | string;
  highest: number | string;
  mean?: number | string;
  stdDev?: number | string;
  suffix?: string;
  decimals?: number;
  hideSummary?: boolean;
  className?: string;
  labels?: {
    lowest?: string;
    median?: string;
    highest?: string;
    mean?: string;
    stdDev?: string;
  };
};

const fmt = (v: number | string, decimals = 0, suffix = '%') =>
  typeof v === 'string' ? v : `${v.toFixed(decimals)}${suffix}`;

// ikon sort netral (atas & bawah abu2 seperti di contoh tanstackmu)
const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
    />
  </div>
);

export default function QuizScoreStatsCard({
  lowest,
  median,
  highest,
  mean,
  stdDev,
  suffix = '%',
  decimals = 0,
  hideSummary = false,
  className,
  labels,
}: Readonly<QuizScoreStatsCardProps>) {
  const H = useMemo(
    () => ({
      lowest: labels?.lowest ?? 'Lowest Score',
      median: labels?.median ?? 'Median',
      highest: labels?.highest ?? 'Highest Score',
      mean: labels?.mean ?? 'Mean',
      stdDev: labels?.stdDev ?? 'Standard Deviation',
    }),
    [labels]
  );

  const V = useMemo(
    () => ({
      lowest: fmt(lowest as any, decimals, suffix),
      median: fmt(median as any, decimals, suffix),
      highest: fmt(highest as any, decimals, suffix),
      mean:
        typeof mean !== 'undefined' ? fmt(mean as any, decimals, suffix) : null,
      stdDev:
        typeof stdDev !== 'undefined'
          ? fmt(stdDev as any, decimals, suffix)
          : null,
    }),
    [lowest, median, highest, mean, stdDev, decimals, suffix]
  );

  return (
    <div className={clsx('w-full', className)}>
      <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-2">
        <table className="w-full border-collapse table-fixed">
          <thead className="text-xs text-[#767676] font-semibold">
            <tr>
              <th className="w-[125px] md:w-[150px] xl:w-[210px] py-4 px-3 text-left">
                <span className="flex items-center gap-1">
                  {H.lowest}
                  <IconSort />
                </span>
              </th>
              <th className="w-[125px] md:w-[150px] xl:w-[210px] py-4 px-3 text-left">
                <span className="flex items-center gap-1">
                  {H.median}
                  <IconSort />
                </span>
              </th>
              <th className="w-[125px] md:w-[150px] xl:w-[210px] py-4 px-3 text-left">
                <span className="flex items-center gap-1">
                  {H.highest}
                  <IconSort />
                </span>
              </th>
            </tr>
          </thead>

          <tbody className="text-xs text-[#3C3C3C]">
            <tr className="bg-white font-medium">
              <td className="w-[125px] md:w-[150px] xl:w-[210px] px-3 py-5 text-left">
                {V.lowest}
              </td>
              <td className="w-[125px] md:w-[150px] xl:w-[210px] px-3 py-5 text-left">
                {V.median}
              </td>
              <td className="w-[125px] md:w-[150px] xl:w-[210px] px-3 py-5 text-left">
                {V.highest}
              </td>
            </tr>

            {!hideSummary && (V.mean || V.stdDev) && (
              <tr>
                <td colSpan={3}>
                  <div className="rounded-md bg-[#FAFAFA] p-3 text-sm text-[#3C3C3C]">
                    {V.mean && (
                      <p className="leading-6">
                        <span className="font-medium">{H.mean}:</span> {V.mean}
                      </p>
                    )}
                    {V.stdDev && (
                      <p className="leading-6">
                        <span className="font-medium">{H.stdDev}:</span>{' '}
                        {V.stdDev}
                      </p>
                    )}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
