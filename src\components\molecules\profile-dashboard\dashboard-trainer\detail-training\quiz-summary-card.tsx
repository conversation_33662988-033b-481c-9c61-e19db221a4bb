'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import clsx from 'clsx';
import { ChartLine } from 'lucide-react';

export type QuizBucket = {
  range: string;
  count: number;
};

export type QuizSummaryProps = {
  title?: string;
  averageScore?: number | null;
  maxScore?: number;
  buckets: QuizBucket[];
  height?: number;
  minChartWidth?: number;
  barColor?: string;
  gridColor?: string;
  className?: string;
  tooltipLabel?: string;
};

function EmptyState() {
  return (
    <div className="flex items-center justify-center h-[220px] text-sm text-[#9C9C9C]">
      No data to display
    </div>
  );
}

function getRoundedMax(maxVal: number) {
  if (maxVal <= 10) return 10;
  const step = 10;
  return Math.ceil(maxVal / step) * step;
}

export default function QuizSummary({
  title = 'Quiz Summary',
  averageScore = null,
  maxScore = 100,
  buckets,
  height = 280,
  minChartWidth = 720,
  barColor = '#F7941E',
  gridColor = '#EAEAEA',
  className,
  tooltipLabel = 'peserta',
}: Readonly<QuizSummaryProps>) {
  const maxCount = React.useMemo(
    () => Math.max(0, ...buckets.map((b) => b.count)),
    [buckets]
  );
  const yMax = React.useMemo(() => getRoundedMax(maxCount), [maxCount]);

  const avgText =
    typeof averageScore === 'number'
      ? `${Number(averageScore.toFixed(1))}/${maxScore}`
      : null;

  return (
    <div className={clsx('w-full', className)}>
      <div className="flex flex-col items-center font-medium gap-3 mb-3">
        <p className="md:text-lg font-bold text-[#3C3C3C]">{title}</p>
        {avgText && (
          <div className="flex flex-col gap-1 items-center">
            <p className="text-xs text-[#717171]">Average Score</p>
            <p className="flex flex-row gap-1 items-center font-medium text-[#717171]">
              <ChartLine
                color="#717171"
                size={16}
              />
              {avgText}
              <span className="text-xs">pts</span>
            </p>
          </div>
        )}
      </div>

      <div className="w-full">
        {buckets.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="w-full overflow-x-auto">
            <div
              className="shrink-0"
              style={{ minWidth: minChartWidth, height }}
            >
              <ResponsiveContainer
                width="100%"
                height="100%"
              >
                <BarChart
                  data={buckets}
                  margin={{ top: 12, right: 0, left: 0, bottom: 0 }}
                  barSize={30}
                  barGap={0}
                  barCategoryGap="20%"
                >
                  <CartesianGrid
                    stroke={gridColor}
                    vertical={true}
                    horizontal={true}
                  />

                  <XAxis
                    dataKey="range"
                    tickLine={false}
                    axisLine={{ stroke: gridColor }}
                    interval={0}
                    height={28}
                    tick={{
                      fontSize: 12,
                      fill: '#2E2A2A',
                      fontWeight: 600 as any,
                    }}
                    padding={{ left: 0, right: 0 }}
                  />

                  <YAxis
                    width={36}
                    domain={[0, yMax]}
                    allowDecimals={false}
                    tickCount={Math.min(11, yMax + 1)}
                    axisLine={{ stroke: gridColor }}
                    tickLine={false}
                    tick={{
                      fontSize: 12,
                      fill: '#77838F',
                      fontWeight: 500 as any,
                    }}
                    tickMargin={12}
                  />

                  <Tooltip
                    cursor={{ fill: 'rgba(0,0,0,0.04)' }}
                    contentStyle={{ borderRadius: 8, borderColor: gridColor }}
                    formatter={(value) => [
                      `${value} ${tooltipLabel}`,
                      'Jumlah',
                    ]}
                    labelFormatter={(label) => `Skor ${label}`}
                  />

                  <Bar
                    dataKey="count"
                    radius={[4, 4, 0, 0]}
                    fill={barColor}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
