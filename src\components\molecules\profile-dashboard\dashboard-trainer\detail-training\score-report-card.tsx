'use client';
import React from 'react';

export type ScoreBin = { label: string; count: number };

export default function ScoreReportCard({
  title = 'Quiz Summary',
  averageScore = 99,
  maxScore = 100,
  bins,
  lowestPct = 96,
  medianPct = 100,
  highestPct = 100,
  meanPct = 99,
  stdDevPct = 2,
}: Readonly<{
  title?: string;
  averageScore?: number;
  maxScore?: number;
  bins: ScoreBin[];
  lowestPct?: number;
  medianPct?: number;
  highestPct?: number;
  meanPct?: number;
  stdDevPct?: number;
}>) {
  const maxCount = Math.max(10, ...bins.map((b) => b.count));
  const h = 220; // chart height
  const w = 12 * bins.length; // per-bin width ~12px
  const padLeft = 30;
  const padTop = 10;
  const padBottom = 36;
  const chartW = w;
  const chartH = h;

  const yTicks = Array.from({ length: 11 }, (_, i) => i); // 0..10
  const gridLines = yTicks.map((t) => {
    const y = padTop + chartH - (t / maxCount) * chartH;
    return (
      <line
        key={t}
        x1={padLeft}
        x2={padLeft + chartW}
        y1={y}
        y2={y}
        stroke="#EEE"
      />
    );
  });

  return (
    <div className="rounded-xl border border-[#EAEAEA]">
      <div className="p-4 sm:p-5">
        <div className="text-center mb-2">
          <p className="text-sm text-[#3C3C3C] font-semibold">{title}</p>
          <p className="text-[11px] text-[#A7A7A7] mt-1">Average Score</p>
          <div className="flex items-center justify-center gap-1 text-[12px] text-[#717171]">
            <span className="inline-block rotate-315">↘</span>
            <span className="font-medium">
              {averageScore}/{maxScore} pts
            </span>
          </div>
        </div>

        {/* Chart */}
        <div className="w-full overflow-x-auto">
          <svg
            width={padLeft + chartW + 8}
            height={padTop + chartH + padBottom}
          >
            {/* Grid */}
            {gridLines}
            {/* Y-axis labels (0..10) */}
            {yTicks.map((t) => {
              const y = padTop + chartH - (t / maxCount) * chartH;
              return (
                <text
                  key={t}
                  x={padLeft - 10}
                  y={y + 3}
                  textAnchor="end"
                  fontSize="10"
                  fill="#9B9B9B"
                >
                  {t}
                </text>
              );
            })}

            {/* Bars */}
            {bins.map((b, i) => {
              const bw = 10;
              const gap = 2;
              const x = padLeft + i * (bw + gap);
              const hPx = (b.count / maxCount) * chartH;
              const y = padTop + chartH - hPx;
              return (
                <rect
                  key={b.label}
                  x={x}
                  y={y}
                  width={bw}
                  height={hPx}
                  rx={2}
                  fill="#FFA119"
                />
              );
            })}

            {/* X labels */}
            {bins.map((b, i) => {
              const bw = 10;
              const gap = 2;
              const x = padLeft + i * (bw + gap) + bw / 2;
              const y = padTop + chartH + 16;
              return (
                <text
                  key={b.label}
                  x={x}
                  y={y}
                  fontSize="10"
                  textAnchor="middle"
                  fill="#7E7E7E"
                >
                  {b.label}
                </text>
              );
            })}
          </svg>
        </div>
      </div>

      {/* Footer stats */}
      <div className="rounded-b-xl bg-white px-4 sm:px-5 pb-4">
        <div className="rounded-xl border border-[#EFEFEF] p-4 sm:p-5">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <Stat
              label="Lowest Score"
              value={`${lowestPct}%`}
            />
            <Stat
              label="Median"
              value={`${medianPct}%`}
            />
            <Stat
              label="Highest Score"
              value={`${highestPct}%`}
            />
          </div>

          <div className="mt-4 text-[11px] text-[#777]">
            <p>Mean: {meanPct}%</p>
            <p>Standard Deviation: {stdDevPct}%</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Stat({ label, value }: Readonly<{ label: string; value: string }>) {
  return (
    <div className="flex flex-col">
      <span className="text-[#9B9B9B] text-[12px]">{label}</span>
      <span className="text-[#3C3C3C] font-medium mt-1">{value}</span>
    </div>
  );
}
