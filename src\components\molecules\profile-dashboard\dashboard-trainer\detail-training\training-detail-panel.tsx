'use client';
import React from 'react';
import { BaseButton } from '@/components/atoms/button';
import { IconExport } from '@/assets/icons/IconExport';
import { IconArrow } from '@/assets/icons/IconArrow';
import { IconBookOpenUser } from '@/assets/icons/IconBookOpenUser';
import { IconCalendarDots } from '@/assets/icons/IconCalendarDots';
import { IconClock } from '@/assets/icons/IconClock';
import { IconUserRound } from '@/assets/icons/IconUserRound';
import QuizSummary, { QuizBucket } from './quiz-summary-card';
import QuestionRankingTable, { QuestionRow } from './question-ranking-table';
import OpinionAndFeedback, { FeedbackRow } from './opinion-and-feedbacl';
import PaginationComp from '@/components/molecules/pagination';
import QuizScoreStatsCard from './quiz-score-stats-card';
import PromotorScore from '../promotor-score';
import PromotorStats from './promotor-stats';

export type TrainingItem = {
  id: string;
  date: Date;
  durationMin: number;
  participants: number;
  module: string;
};

type Participant = {
  id: string;
  name: string;
  role: string;
  department: string;
  npk: string;
};

const quizBuckets: QuizBucket[] = [
  { range: '0-10%', count: 0 },
  { range: '11-20%', count: 0 },
  { range: '21-30%', count: 0 },
  { range: '31-40%', count: 0 },
  { range: '41-50%', count: 0 },
  { range: '51-60%', count: 0 },
  { range: '61-70%', count: 0 },
  { range: '71-80%', count: 0 },
  { range: '81-90%', count: 7 },
  { range: '91-100%', count: 6 },
];

const questionRows: QuestionRow[] = [
  {
    code: 'Q8',
    question:
      'Kemampuan menggunakan contoh yang relevan dengan materi yang disampaikan',
    difficulty: 1,
    averageScore: 97,
  },
  {
    code: 'Q12',
    question: 'Kemampuan menghidupkan suasana pelatihan',
    difficulty: 1,
    averageScore: 97,
  },
  {
    code: 'Q6',
    question: 'Penguasaan materi (teori/prosedur dan praktek di lapangan)',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q7',
    question:
      'Penggunaan kata-kata mudah dimengerti dan mewakili apa yang ingin disampaikan',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q9',
    question: 'Kemampuan menjawab pertanyaan dan mengatasi sanggahan',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q10',
    question: 'Kemampuan menjawab pertanyaan dan mengatasi sanggahan',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q11',
    question: 'Kemampuan menjawab pertanyaan dan mengatasi sanggahan',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q13',
    question:
      'Kemampuan manajemen waktu sehingga seluruh materi dapat tersampaikan dengan optimal',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q14',
    question:
      'Efektivitas penggunaan alat bantu secara online maupun offline (pointer, zoom, markers, flipchart, dll).',
    difficulty: 3,
    averageScore: 100,
  },
  {
    code: 'Q15',
    question: 'Tampilan slide presentasi materi',
    difficulty: 3,
    averageScore: 100,
  },
];

const base = new Date('2025-06-05T14:59:00');

const responses = [
  'Sangat baik',
  'Baik',
  'Cukup',
  'Perlu peningkatan',
  'Materi sangat jelas',
  'Penyampaian menarik',
];

export const feedbackRowsDummy: FeedbackRow[] = Array.from(
  { length: 28 },
  (_, i) => ({
    no: i + 1,
    response: responses[i % responses.length],
    date: new Date(base.getTime() + i * 60_000),
  })
);

const makeId = () =>
  typeof crypto !== 'undefined' && 'randomUUID' in crypto
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);

function generateDummyParticipants(total = 23): Participant[] {
  const names = [
    'Kevin Joshua Adiyanto H',
    'Lina Marie Torres',
    'Ravi Singh',
    'Ayu Puspita',
    'Budi Santoso',
    'Nadia Zahra',
    'Irfan Maulana',
  ];
  const roles = ['UI/UX Designer', 'IT Analyst', 'Frontend Dev', 'Backend Dev'];
  const depts = ['Digital Development', 'IT', 'Product', 'QA'];

  return Array.from({ length: total }, (_, i) => ({
    id: makeId(),
    name: names[i % names.length],
    role: roles[i % roles.length],
    department: depts[i % depts.length],
    npk: String(10000 + i).padStart(5, '0'),
  }));
}

const fmtDate = (d: Date) =>
  d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
const fmtTime = (d: Date) =>
  d.toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });

export default function TrainingDetailPanel({
  training,
  onBack,
  summary = false,
}: Readonly<{
  training: TrainingItem;
  onBack: () => void;
  summary: boolean;
}>) {
  const PAGE_SIZE = 5;
  const [page, setPage] = React.useState(0);

  const all = React.useMemo(() => generateDummyParticipants(23), []);
  const slice = React.useMemo(() => {
    const start = page * PAGE_SIZE;
    return all.slice(start, start + PAGE_SIZE);
  }, [all, page]);

  return (
    <div className="w-full">
      <div className="flex flex-col gap-6 md:gap-[2px]">
        <div className="flex items-start justify-between">
          <p className="text-xl font-bold text-[#3C3C3C]">Training Detail</p>
          <div className="flex gap-2">
            <BaseButton
              className="h-11 px-4 border border-[#DEDEDE] bg-white hover:bg-white hover:opacity-80 text-[#3C3C3C]"
              onClick={onBack}
            >
              <IconArrow color="#3C3C3C" />{' '}
              <span className="hidden xl:block">Kembali</span>
            </BaseButton>
            <BaseButton className="h-11 px-4 border border-[#DEDEDE] bg-white hover:bg-white hover:opacity-80">
              <IconBookOpenUser />
            </BaseButton>
            <BaseButton className="h-11 px-4 border border-[#DEDEDE] bg-white hover:bg-white hover:opacity-80">
              <IconExport color="#3C3C3C" />
              <span className="hidden xl:inline text-sm text-[#3C3C3C] font-medium">
                Export File
              </span>
            </BaseButton>
          </div>
        </div>
        <div className="flex flex-row gap-8 md:gap-10 pt-1">
          <div>
            <p className="text-xs text-[#B1B1B1] mb-1">Training Date</p>
            <div className="flex flex-row gap-[10px] items-start md:items-center">
              <span className="shrink-0 inline-flex">
                <IconCalendarDots size={16} />
              </span>
              <p className="text-sm text-[#717171]">
                {fmtDate(training.date)}, {fmtTime(training.date)}
              </p>
            </div>
          </div>

          <div>
            <p className="text-xs text-[#B1B1B1] mb-1">Duration</p>
            <div className="flex flex-row gap-[10px] items-start md:items-center">
              <span className="shrink-0 inline-flex">
                <IconClock size={16} />
              </span>
              <p className="text-sm text-[#717171]">
                {training.durationMin} mins
              </p>
            </div>
          </div>

          <div>
            <p className="text-xs text-[#B1B1B1] mb-1">Participant</p>
            <div className="flex flex-row gap-[10px] items-start md:items-center">
              <span className="shrink-0 inline-flex">
                <IconUserRound size={16} />
              </span>
              <p className="text-sm text-[#717171]">{training.participants}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="border-b border-[#EAEAEA] -mx-5 my-6"></div>

      <div className="w-full flex flex-col gap-6">
        <div className="w-full flex flex-col gap-4">
          <p className="text-lg font-bold text-[#3C3C3C]">Participants List</p>

          <div className="overflow-x-auto rounded-lg border border-[#EAEAEA] p-4">
            <table className="w-full border-collapse">
              <thead className="text-xs text-[#767676]">
                <tr>
                  <th className="text-left py-3 px-3 min-w-[220px]">
                    Participants
                  </th>
                  <th className="text-left py-3 px-3 min-w-[160px]">Role</th>
                  <th className="text-left py-3 px-3 min-w-[180px]">
                    Department
                  </th>
                  <th className="text-left py-3 px-3 min-w-[100px]">NPK</th>
                </tr>
              </thead>
              <tbody className="text-xs text-[#3C3C3C]">
                {slice.map((p, i) => (
                  <tr
                    key={p.id}
                    className={i % 2 === 1 ? 'bg-[#FAFAFA]' : 'bg-white'}
                  >
                    <td className="py-4 px-3">{p.name}</td>
                    <td className="py-4 px-3">{p.role}</td>
                    <td className="py-4 px-3">{p.department}</td>
                    <td className="py-4 px-3">{p.npk}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <PaginationComp
            page={page}
            totalEntries={all.length}
            pageSize={PAGE_SIZE}
            onPageChange={setPage}
            isMobile={false}
            hideSummary
            alignLeft
          />
        </div>
        {summary && (
          <div className="w-full flex flex-col gap-4">
            <p className="md:text-lg font-bold text-[#3C3C3C]">Score Report</p>

            <div className="w-full flex flex-col gap-10 overflow-x-auto rounded-lg border border-[#EAEAEA] p-4">
              <QuizSummary
                averageScore={99}
                maxScore={100}
                buckets={quizBuckets}
                height={351}
                barColor="#FFA31A"
                tooltipLabel="peserta"
              />

              <QuizScoreStatsCard
                lowest={96}
                median={100}
                highest={100}
                mean={99}
                stdDev={2}
              />

              <div className="w-full flex flex-col gap-4">
                <p className="text-sm md:text-base font-bold text-[#3C3C3C]">
                  Question Ranking
                </p>

                <QuestionRankingTable rows={questionRows} />
              </div>

              <PromotorScore
                title="How likely is it that you would recommend Trainer to a friend or colleague?"
                className="flex-1 h-full justify-center border-none p-0"
                classNameTitle="w-full text-sm md:text-center"
                classNameGauge="w-[247px]"
                answered={6}
                skipped={0}
              />

              <PromotorStats />

              <OpinionAndFeedback
                answered={22}
                skipped={6}
                rows={feedbackRowsDummy}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
