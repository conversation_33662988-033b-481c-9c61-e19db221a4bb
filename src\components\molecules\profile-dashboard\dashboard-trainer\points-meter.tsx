'use client';

import { ChartLine } from 'lucide-react';
import { useCallback, useId, useRef } from 'react';
import { cn } from '@/lib/utils';

type PointsMeterProps = {
  value: number;
  total?: number;
  segments?: number;
  className?: string;
  meterClassName?: string;
  filledClass?: string;
  emptyClass?: string;
};

export default function PointsMeter({
  value,
  total = 100,
  segments = 18,
  className,
  meterClassName,
  filledClass = 'bg-[#FFBF00]',
  emptyClass = 'bg-[#D9D9D9]',
}: Readonly<PointsMeterProps>) {
  const ratio = Math.max(0, Math.min(1, value / total));

  let filled: number;
  if (value <= 0) filled = 0;
  else if (value >= total) filled = segments;
  else
    filled = Math.max(1, Math.min(segments - 1, Math.floor(ratio * segments)));

  const baseId = useId();
  const idsRef = useRef<string[]>([]);
  const counterRef = useRef(0);
  const makeId = useCallback(() => {
    counterRef.current += 1;
    return `${baseId}-${counterRef.current}`;
  }, [baseId]);

  if (idsRef.current.length !== segments) {
    if (idsRef.current.length < segments) {
      const toAdd = segments - idsRef.current.length;
      idsRef.current = idsRef.current.concat(
        Array.from({ length: toAdd }, () => makeId())
      );
    } else {
      idsRef.current = idsRef.current.slice(0, segments);
    }
  }

  return (
    <div
      className={cn(
        'w-full h-full bg-[#222222] rounded-md flex items-center justify-center',
        className
      )}
    >
      <div className="flex flex-col gap-3 py-[38px] px-6">
        <p className="text-xs lg:text-base text-[#B1B1B1]">Average Score</p>
        <p className="text-2xl lg:text-[32px] text-white">{value}%</p>
      </div>

      {/* divider ikut stretch */}
      <div className="border-l border-[#585858] h-[80%]" />

      <div className="flex flex-col gap-3 py-[34px] px-3">
        <div className="flex flex-col gap-1">
          <p className="text-xs lg:text-sm text-[#DDDDDD] text-nowrap">
            Total points collected from learner
          </p>
          <div className="flex flex-row items-center gap-[2px]">
            <ChartLine
              size={16}
              color="#FFFFFF"
            />
            <p className="text-sm lg:text-base text-white">{value}/100</p>
            <p className="text-[#717171] text-xs">pts</p>
          </div>
        </div>

        <div
          role="meter"
          aria-valuemin={0}
          aria-valuemax={total}
          aria-valuenow={value}
          className={cn('flex gap-[2px]', meterClassName)}
        >
          {idsRef.current.map((id, i) => (
            <div
              key={id}
              className={cn(
                'h-6 w-2 rounded-2xl transition-all',
                i < filled ? filledClass : emptyClass
              )}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
