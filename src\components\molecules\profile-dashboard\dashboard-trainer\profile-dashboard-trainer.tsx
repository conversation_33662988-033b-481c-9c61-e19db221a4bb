'use client';

import React from 'react';
import { IconClockCountdown } from '@/assets/icons/IconClockCountdown';
import PointsMeter from './points-meter';
import PromotorScore from './promotor-score';

export default function ProfileDashboardTrainer() {
  return (
    <div className="w-full lg:rounded-lg lg:border lg:border-[#EAEAEA] lg:p-4 text-sm text-[#3C3C3C]">
      <div className="w-full flex flex-col xl:flex-row gap-3 xl:items-stretch">
        <div className="w-full flex-1 flex flex-col gap-3 justify-between">
          <div className="flex items-center gap-[6px] h-[40px] justify-center border border-[#DEDEDE] rounded-sm">
            <IconClockCountdown />
            <p className="text-[#3C3C3C] text-sm font-bold">
              16 hours{' '}
              <span className="text-[#B1B1B1] font-normal">training</span>
            </p>
          </div>

          <PointsMeter
            value={99}
            total={100}
            segments={18}
            className="flex-1"
          />
        </div>

        <PromotorScore
          point={100}
          title="Overall Net Promotor Score"
          className="flex-1 h-full"
        />
      </div>
    </div>
  );
}
