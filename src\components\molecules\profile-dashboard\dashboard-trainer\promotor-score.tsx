'use client';

import { cn } from '@/lib/utils';
import React from 'react';
import GaugeComponent from 'react-gauge-component';

type Props = {
  title?: string;
  point?: number;
  answered?: number | null;
  skipped?: number | null;
  className?: string;
  classNameTitle?: string;
  classNameGauge?: string;
};

export default function PromotorScore({
  title = 'How likely is it that you would recommend <PERSON><PERSON> to a friend or colleague?',
  point = 100,
  answered,
  skipped,
  className,
  classNameTitle,
  classNameGauge,
}: Readonly<Props>) {
  const value = Math.max(-100, Math.min(100, point));

  return (
    <div
      className={cn(
        'w-full h-full flex flex-col gap-3 p-4 rounded-xl bg-white border border-[#DEDEDE]',
        className
      )}
    >
      <p
        className={cn(
          'text-[15px] md:text-base font-semibold text-[#1F1F1F] text-center md:text-left',
          classNameTitle
        )}
      >
        {title}
      </p>

      {(answered || skipped) && (
        <div className="flex flex-row gap-6 items-center justify-center text-sm text-[#717171] text-center">
          <span>Answered: {answered ?? 0}</span>
          <span>Skipped: {skipped ?? 0}</span>
        </div>
      )}

      <div className="relative w-full flex flex-col items-center">
        <div className={cn(classNameGauge)}>
          <GaugeComponent
            id="nps-gauge"
            type="semicircle"
            minValue={-100}
            maxValue={100}
            value={value}
            arc={{
              width: 0.4,
              padding: 0.03,
              cornerRadius: 0,
              subArcs: [
                { limit: -50, color: '#F59E0B' },
                { limit: 0, color: '#F59E0B' },
                { limit: 50, color: '#F59E0B' },
                { limit: 100, color: '#F59E0B' },
              ],
            }}
            pointer={{ length: 0, width: 0 }}
            labels={{
              valueLabel: { hide: true },
              tickLabels: {
                type: 'outer',
                ticks: [-100, -50, 0, 50, 100].map((v) => ({ value: v })),
                defaultTickValueConfig: {
                  formatTextValue: (v) => String(v),
                  style: { fontSize: 12, fill: '#BDBDBD' },
                },
                defaultTickLineConfig: { hide: true },
              },
            }}
            style={{ width: '100%', height: 'auto' }}
          />
        </div>

        <div className="absolute inset-0 flex items-end justify-center pb-2 pointer-events-none">
          <span className="text-[18px] md:text-[20px] font-semibold text-black">
            {value} NPS
          </span>
        </div>
      </div>
    </div>
  );
}
