'use client';
import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
  flexRender,
  SortingState,
  getSortedRowModel,
  CellContext,
} from '@tanstack/react-table';
import { useMediaQuery } from '@/hooks';
import { Eye, Triangle } from 'lucide-react';
import { IconChevron } from '@/assets/icons/IconChevron';
import NoDataTable from '@/assets/images/no-found-data.png';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import PaginationComp from '../../pagination';

export type UpcomingRow = {
  id: string;
  date: Date;
  durationMin: number;
  module: string;
  participants: number;
};

export type UpcomingOnView = (row: UpcomingRow) => void;

const PAGE_SIZE = 10;

const formatDate = (d: Date) =>
  d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
const formatTime = (d: Date) =>
  d.toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });

const makeId = () =>
  typeof crypto !== 'undefined' && 'randomUUID' in crypto
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);

function generateDummyTrainings(): UpcomingRow[] {
  const results: UpcomingRow[] = [];
  let cursor = new Date();
  for (let i = 0; i < 20; i++) {
    const step = 2 + (i % 4);
    cursor = new Date(
      cursor.getFullYear(),
      cursor.getMonth(),
      cursor.getDate() + step,
      9 + (i % 6),
      (i % 2) * 30
    );
    const duration = [30, 45, 60, 90][i % 4];
    results.push({
      id: makeId(),
      date: cursor,
      durationMin: duration,
      module: `Module #${(i % 7) + 1}`,
      participants: 10 + ((i * 5) % 25),
    });
  }
  results.sort((a, b) => a.date.getTime() - b.date.getTime());
  return results;
}

const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => (
  <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
    <Triangle
      size={12}
      fill={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}
    />
    <Triangle
      size={12}
      className="rotate-180"
      fill={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
      color={direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}
    />
  </div>
);

function TableHeader({
  title,
  open,
  onToggle,
}: Readonly<{ title: string; open: boolean; onToggle: () => void }>) {
  return (
    <div className="flex justify-between items-center py-3 border-b border-[#DEDEDE]">
      <p className="text-base text-[#3C3C3C] font-bold">{title}</p>
      <button
        type="button"
        aria-expanded={open}
        onClick={onToggle}
        className="inline-flex items-center cursor-pointer"
      >
        <IconChevron
          direction={open ? 'top' : 'bottom'}
          color="#343330"
        />
      </button>
    </div>
  );
}

type Ctx = Readonly<CellContext<UpcomingRow, unknown>>;

function StartsAtCell(ctx: Ctx) {
  const d = ctx.getValue() as Date;
  return (
    <div className="flex flex-col gap-1">
      <span className="text-[#3C3C3C]">{formatDate(d)}</span>
      <span className="text-[#717171]">{formatTime(d)}</span>
    </div>
  );
}

function DurationCell(ctx: Ctx) {
  return <span>{ctx.getValue() as number} mins</span>;
}

function ModuleCell(_ctx: Ctx) {
  return (
    <p className="text-[#2CA5FF] hover:underline cursor-pointer">View Module</p>
  );
}

function ParticipantsCell(ctx: Ctx) {
  return <span>{ctx.getValue() as number}</span>;
}

const ActionCellFactory =
  (onView?: UpcomingOnView) =>
  (ctx: Readonly<CellContext<UpcomingRow, unknown>>) =>
    (
      <button
        type="button"
        className="p-2 rounded-full hover:bg-[#F3F3F3] cursor-pointer"
        aria-label="View"
        onClick={() => onView?.(ctx.row.original)}
      >
        <Eye
          size={24}
          className="text-[#343330]"
        />
      </button>
    );

export default function UpcomingTrainingTable({
  onView,
}: Readonly<{ onView?: UpcomingOnView }>) {
  const isMobile = useMediaQuery('(max-width: 1024px)');

  const [page, setPage] = useState(0);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'startsAt', desc: false },
  ]);
  const [collapsed, setCollapsed] = useState(false);

  const allRows = useMemo<UpcomingRow[]>(() => generateDummyTrainings(), []);

  const TOTAL_ENTRIES = allRows.length;

  const pageRows = useMemo(() => {
    const start = page * PAGE_SIZE;
    return TOTAL_ENTRIES > 0 ? allRows.slice(start, start + PAGE_SIZE) : [];
  }, [allRows, page, TOTAL_ENTRIES]);

  const columns = useMemo<ColumnDef<UpcomingRow>[]>(
    () => [
      {
        id: 'startsAt',
        header: 'Training Date',
        accessorFn: (r) => r.date,
        sortingFn: 'datetime',
        cell: StartsAtCell,
      },
      {
        id: 'duration',
        header: 'Durasi',
        accessorFn: (r) => r.durationMin,
        cell: DurationCell,
      },
      {
        id: 'module',
        header: 'Module',
        accessorFn: (r) => r.module,
        enableSorting: false,
        cell: ModuleCell,
      },
      {
        id: 'participants',
        header: 'Jumlah Peserta',
        accessorFn: (r) => r.participants,
        cell: ParticipantsCell,
      },
      {
        id: 'action',
        header: 'Action',
        enableSorting: false,
        cell: ActionCellFactory(onView),
      },
    ],
    [onView]
  );

  const table = useReactTable({
    data: pageRows,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const hasRows = TOTAL_ENTRIES > 0;

  return (
    <div className="flex flex-col gap-3 lg:gap-5">
      <TableHeader
        title="Upcoming Training"
        open={!collapsed}
        onToggle={() => setCollapsed((v) => !v)}
      />

      <div
        className={cn(
          'transition-all duration-300 overflow-hidden',
          collapsed ? 'max-h-0 opacity-0' : 'max-h-[1200px] opacity-100'
        )}
      >
        <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-4">
          <table className="w-full border-collapse">
            <thead className="text-xs text-[#767676]">
              {table.getHeaderGroups().map((hg) => (
                <tr key={hg.id}>
                  {hg.headers.map((header) => {
                    const sortDir = header.column.getIsSorted();
                    const canSort = header.column.getCanSort();
                    const id = header.id;

                    const widthClass =
                      id === 'action' ? 'min-w-[80px]' : 'min-w-[140px]';

                    return (
                      <th
                        key={header.id}
                        onClick={
                          canSort
                            ? header.column.getToggleSortingHandler()
                            : undefined
                        }
                        className={cn(
                          'py-4 px-3 font-medium text-left text-xs text-[#3C3C3C] select-none',
                          canSort && 'cursor-pointer',
                          widthClass
                        )}
                      >
                        <div className="flex items-center gap-1">
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {canSort && (
                            <IconSort
                              direction={
                                sortDir === 'asc' || sortDir === 'desc'
                                  ? sortDir
                                  : undefined
                              }
                            />
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>

            <tbody className="text-xs text-[#3C3C3C]">
              {hasRows ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className="odd:bg-[#FAFAFA] even:bg-white"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className="py-5 px-3"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={table.getAllLeafColumns().length}
                    className="py-14"
                  >
                    <div className="flex flex-col items-center justify-center gap-3">
                      <Image
                        src={NoDataTable}
                        alt="no data table"
                        width={560}
                        height={500}
                        className="max-w-[140px] max-h-[125px]"
                      />
                      <div className="flex flex-col gap-1">
                        <p className="text-[#3C3C3C] text-sm font-medium">
                          No training scheduled
                        </p>
                        <p className="text-[#767676] text-[10px] leading-[14px]">
                          Come again later
                        </p>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {hasRows && !collapsed && (
        <div className="border-t border-[#DEDEDE] pt-3 lg:pt-5">
          <PaginationComp
            page={page}
            totalEntries={TOTAL_ENTRIES}
            pageSize={PAGE_SIZE}
            onPageChange={setPage}
            isMobile={isMobile}
          />
        </div>
      )}
    </div>
  );
}
