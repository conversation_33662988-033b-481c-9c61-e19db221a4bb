'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { CalendarIcon, Clock, MapPinIcon } from 'lucide-react';
import TodoList from '@/assets/images/todo-list.png';
import Image from 'next/image';

export type InClassTrainingCardProps = {
  className?: string;
  eventName: string;
  title: string;
  instructor: string;
  dateRange: string;
  timeRange: string;
  company: string;
};

export default function CardInClassTraining({
  className,
  eventName,
  title,
  instructor,
  dateRange,
  timeRange,
  company,
}: Readonly<InClassTrainingCardProps>) {
  return (
    <div
      className={cn(
        'rounded-xl bg-white border border-[#DEDEDE] overflow-hidden',
        className
      )}
    >
      <div className="relative p-4 min-h-[136px] flex flex-col gap-2 bg-[linear-gradient(101.68deg,#FFA841_5.83%,#F98700_95.34%)]">
        <span className="w-fit text-[#3C3C3C] text-xs px-2 py-[3px] rounded border border-[#DEDEDE] bg-white">
          {eventName}
        </span>

        <div className="text-white space-y-2 max-w-[194px]">
          <h3 className="text-lg font-bold line-clamp-2">{title}</h3>
          <p className="text-sm">with {instructor}</p>
        </div>

        <Image
          src={TodoList}
          alt="Todo List Accent One"
          width={48}
          height={64}
          className="pointer-events-none absolute -top-[5px] left-[233px] opacity-60"
        />

        <Image
          src={TodoList}
          alt="Todo List Accent Two"
          width={48}
          height={64}
          className="pointer-events-none absolute top-[51px] left-[257px] opacity-60"
        />
      </div>

      <div className="p-4 text-sm text-[#767676] space-y-3">
        <div className="flex items-center gap-2">
          <CalendarIcon
            size={16}
            className="shrink-0 text-[#767676]"
          />
          <span>{dateRange}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock
            size={16}
            className="shrink-0 text-[#767676]"
          />
          <span>{timeRange}</span>
        </div>
        <div className="flex items-center gap-2">
          <MapPinIcon
            size={16}
            className="shrink-0 text-[#767676]"
          />

          <span>{company}</span>
        </div>
      </div>
    </div>
  );
}
