'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export type CardModuleProps = {
  className?: string;
  title: string;
  technical?: string[];
  soft?: string[];
};

export default function CardModule({
  className,
  title,
  technical,
  soft,
}: Readonly<CardModuleProps>) {
  return (
    <div
      className={cn(
        'rounded-lg border border-[#DEDEDE] bg-white',
        'flex flex-col gap-[30px]',
        'p-4',
        className
      )}
    >
      <div className="mb-3 space-y-2">
        <h3 className="font-semibold text-[#F7941E]">{title}</h3>
        <p className="text-sm text-[#B1B1B1]">Module</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-[#717171]">
        <div className="space-y-2">
          <p>Technical Competencies</p>
          <p>
            {technical && technical.length > 0 ? technical.join(', ') : '-'}
          </p>
        </div>
        <div className="space-y-2">
          <p>Soft Competencies</p>
          <p>{soft && soft.length > 0 ? soft.join(', ') : '-'}</p>
        </div>
      </div>
    </div>
  );
}
