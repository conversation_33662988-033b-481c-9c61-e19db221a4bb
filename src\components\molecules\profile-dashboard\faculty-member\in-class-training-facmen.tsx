'use client';

import React, { useMemo, useState } from 'react';
import CardInClassTraining from './card-in-class-training';
import PaginationComp from '../../pagination';

type TrainingItem = {
  id: string;
  eventName: string;
  title: string;
  instructor: string;
  start: Date;
  end: Date;
  startTime: string;
  endTime: string;
  company: string;
};

const PAGE_SIZE = 10;

const makeId = () =>
  typeof crypto !== 'undefined' && 'randomUUID' in crypto
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);

/** Format: "12 Sep 2023" */
const fmtDate = (d: Date) =>
  d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });

/** Format: "12 Sep 2023 - 13 Sep 2023" */
const fmtDateRange = (s: Date, e: Date) => `${fmtDate(s)} - ${fmtDate(e)}`;

function generateDummyInClassTraining(total = 100): TrainingItem[] {
  const titles = [
    'ACC Basic Training – Jumper 4',
    'ACC Advance Coaching – Batch 2',
    'ACC Leadership Bootcamp – Wave 1',
    'ACC Service Excellence – Sprint',
    'ACC Risk & Compliance – Essentials',
    'ACC Data Literacy – Fundamentals',
  ];
  const instructors = [
    'Andrea Regina',
    'Kevin Jaya',
    'Michelle Tan',
    'Rama Pratama',
    'Yohana Putri',
    'David Seto',
  ];
  const companies = [
    'Astra Credit Companies',
    'Astra International',
    'Astra Digital',
    'Astra Graphia',
  ];

  const base = new Date(2023, 8, 1); // 1 Sep 2023 (hanya dummy agar mirip contoh)
  const out: TrainingItem[] = [];

  for (let i = 0; i < total; i++) {
    const s = new Date(base);
    s.setDate(base.getDate() + (i % 27) + 1);
    const e = new Date(s);
    e.setDate(s.getDate() + 1);

    out.push({
      id: makeId(),
      eventName: 'Event Name',
      title: titles[i % titles.length],
      instructor: instructors[i % instructors.length],
      start: s,
      end: e,
      startTime: '07:00',
      endTime: '17:00',
      company: companies[i % companies.length],
    });
  }
  return out;
}

export default function InClassTrainingFacultyMember() {
  const [page, setPage] = useState(0);

  const allRows = useMemo(() => generateDummyInClassTraining(100), []);
  const total = allRows.length;

  const pageRows = useMemo(() => {
    const start = page * PAGE_SIZE;
    return allRows.slice(start, start + PAGE_SIZE);
  }, [allRows, page]);

  return (
    <div className="w-full">
      <div className="overflow-hidden bg-white flex flex-col max-h-[800px] md:max-h-[600px] rounded-md">
        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
            {pageRows.map((it) => (
              <CardInClassTraining
                key={it.id}
                eventName={it.eventName}
                title={it.title}
                instructor={it.instructor}
                dateRange={fmtDateRange(it.start, it.end)}
                timeRange={`${it.startTime} - ${it.endTime}`}
                company={it.company}
              />
            ))}
          </div>
        </div>

        <div className="pt-4">
          <PaginationComp
            page={page}
            totalEntries={total}
            pageSize={PAGE_SIZE}
            onPageChange={(p) => setPage(p)}
            isMobile={false}
            hideSummary
          />
        </div>
      </div>
    </div>
  );
}
