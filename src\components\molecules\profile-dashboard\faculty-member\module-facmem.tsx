'use client';

import React, { useMemo, useState } from 'react';
import PaginationComp from '../../pagination';
import CardModule from './card-module';

type ModuleItem = {
  id: string;
  title: string;
  technical: string[];
  soft: string[];
};

const PAGE_SIZE = 10;

const makeId = () =>
  typeof crypto !== 'undefined' && 'randomUUID' in crypto
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);

function generateDummyModules(total = 100): ModuleItem[] {
  const titles = [
    'Leaders Eat Last - Simon Sinek',
    'Atomic Habits - James Clear',
    'Deep Work - Cal Newport',
    'Crucial Conversations',
    'High Output Management',
    'The Culture Map',
  ];
  const technicalPool = [
    'TypeScript',
    'SQL',
    'Data Analysis',
    'Microservices',
    'Testing',
    'Kubernetes',
    'Networking',
  ];
  const softPool = [
    'Leadership',
    'Communication',
    'Teamwork',
    'Problem Solving',
    'Time Management',
    'Negotiation',
  ];

  const out: ModuleItem[] = [];
  for (let i = 0; i < total; i++) {
    const hasTech = i % 3 === 0;
    const hasSoft = i % 4 === 0;

    const pick = (pool: string[], n: number) =>
      Array.from({ length: n }, (_, idx) => pool[(i + idx) % pool.length]);

    out.push({
      id: makeId(),
      title: titles[i % titles.length],
      technical: hasTech ? pick(technicalPool, (i % 2) + 1) : [],
      soft: hasSoft ? pick(softPool, (i % 2) + 1) : [],
    });
  }
  return out;
}

export default function ModuleFacultyMember() {
  const [page, setPage] = useState(0);

  const allRows = useMemo(() => generateDummyModules(100), []);
  const total = allRows.length;

  const pageRows = useMemo(() => {
    const start = page * PAGE_SIZE;
    return allRows.slice(start, start + PAGE_SIZE);
  }, [allRows, page]);

  return (
    <div className="w-full">
      <div className="overflow-hidden bg-white flex flex-col max-h-[800px] md:max-h-[600px] rounded-md">
        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          <div className="grid grid-cols-1 gap-4">
            {pageRows.map((it) => (
              <CardModule
                key={it.id}
                title={it.title}
                technical={it.technical}
                soft={it.soft}
              />
            ))}
          </div>
        </div>

        <div className="pt-4">
          <PaginationComp
            page={page}
            totalEntries={total}
            pageSize={PAGE_SIZE}
            onPageChange={(p) => setPage(p)}
            isMobile={false}
            hideSummary
          />
        </div>
      </div>
    </div>
  );
}
