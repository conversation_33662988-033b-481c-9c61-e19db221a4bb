'use client';
import React, { useMemo, useState } from 'react';
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
  flexRender,
  SortingState,
} from '@tanstack/react-table';
import { useMediaQuery } from '@/hooks';
import { Triangle } from 'lucide-react';
import { BaseLabel } from '@/components/atoms/label';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectGroup,
  BaseSelectItem,
  BaseSelectLabel,
  BaseSelectSeparator,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import PaginationComp from '../../pagination';

type CoconutRow = {
  id: string;
  competency: string;
  levels: (number | string)[];
};

const TOTAL_ENTRIES = 80;
const PAGE_SIZE = 10;
const LEVEL_KEYS = ['1,00', '2,00', '3,00', '4,00', '5,00'] as const;

const IconSort = ({ direction }: { direction?: 'asc' | 'desc' }) => {
  return (
    <div className="flex flex-col items-center justify-center text-[#C6C6C6] w-4 h-4">
      <Triangle
        size={12}
        fill={`${direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}`}
        color={`${direction === 'asc' ? '#3C3C3C' : '#EBEBEB'}`}
      />
      <Triangle
        size={12}
        fill={`${direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}`}
        color={`${direction === 'desc' ? '#3C3C3C' : '#EBEBEB'}`}
        className="rotate-180"
      />
    </div>
  );
};

function LevelCell({ value }: { readonly value: string | number }) {
  return <div className="text-left">{value}</div>;
}

export default function HasilSoftCompetencies() {
  const [page, setPage] = useState(0);
  const [level, setLevel] = useState('');
  const [sort, setSort] = useState<SortingState>([]);

  const isMobile = useMediaQuery('(max-width: 1024px)');

  function renderSoftCompetenciesCell(info: any) {
    return <LevelCell value={info.getValue()} />;
  }

  const rows: CoconutRow[] = useMemo(
    () =>
      Array.from({ length: PAGE_SIZE }, () => ({
        id:
          typeof crypto !== 'undefined' && 'randomUUID' in crypto
            ? crypto.randomUUID()
            : Math.random().toString(36).slice(2),
        competency: '-',
        levels: ['-', '-', '-', '-', '-'],
      })),
    [page]
  );

  const columns = useMemo<ColumnDef<CoconutRow>[]>(() => {
    return [
      { header: 'Kompetensi', accessorKey: 'competency', id: 'competency' },
      ...LEVEL_KEYS.map((lvl, idx) => ({
        header: lvl,
        accessorFn: (row: CoconutRow) => row.levels[idx],
        id: `level-${idx}`,
        cell: renderSoftCompetenciesCell,
      })),
    ];
  }, []);

  const table = useReactTable({
    data: rows,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      sorting: sort,
    },
    onSortingChange: setSort,
  });

  return (
    <div className="flex flex-col gap-5">
      <div>
        <BaseLabel className="text-sm text-[#3C3C3C] mb-[10px]">
          Year Period
        </BaseLabel>
        <BaseSelect
          value={level}
          onValueChange={setLevel}
        >
          <BaseSelectTrigger className="w-[240px] h-11 rounded-md border border-[#DEDEDE] bg-white px-4 py-[13px] text-sm text-[#3C3C3C] data-[placeholder]:text-[#B1B1B1] cursor-pointer focus:outline-none">
            <BaseSelectValue placeholder="Semua level" />
          </BaseSelectTrigger>

          <BaseSelectContent className="bg-white border border-[#E6E6E6] rounded-md">
            <BaseSelectGroup>
              <BaseSelectLabel className="px-3 py-2 text-xs text-[#8C8C8C]">
                Level
              </BaseSelectLabel>

              <BaseSelectItem
                value="basic"
                className="text-sm px-3"
              >
                Dasar
              </BaseSelectItem>
              <BaseSelectItem
                value="intermediate"
                className="text-sm px-3"
              >
                Menengah
              </BaseSelectItem>
              <BaseSelectItem
                value="advanced"
                className="text-sm px-3"
              >
                Lanjut
              </BaseSelectItem>

              <BaseSelectSeparator className="my-1" />

              <BaseSelectItem
                value="expert"
                className="text-sm px-3"
              >
                Expert
              </BaseSelectItem>
            </BaseSelectGroup>
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <div className="w-full overflow-x-auto rounded-lg border border-[#EAEAEA] p-4">
        <table className="w-full border-collapse">
          <thead className="text-xs text-[#767676]">
            {table.getHeaderGroups().map((hg) => (
              <tr key={hg.id}>
                {hg.headers.map((header) => {
                  const sortDir = header.column.getIsSorted();

                  return (
                    <th
                      key={header.id}
                      onClick={header.column.getToggleSortingHandler()}
                      className={`py-4 px-3 font-medium text-left text-xs text-[#3C3C3C] cursor-pointer select-none ${
                        header.id === 'competency'
                          ? 'min-w-[105px] md:min-w-[229px]'
                          : 'min-w-[120px]'
                      }`}
                    >
                      <div className="flex items-center gap-1">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        <IconSort
                          direction={
                            sortDir === 'asc' || sortDir === 'desc'
                              ? sortDir
                              : undefined
                          }
                        />
                      </div>
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
          <tbody className="text-xs text-[#3C3C3C]">
            {table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className="odd:bg-[#FAFAFA] even:bg-white"
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className="py-5 px-3"
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {rows.length > 0 && (
        <PaginationComp
          page={page}
          totalEntries={TOTAL_ENTRIES}
          pageSize={PAGE_SIZE}
          onPageChange={setPage}
          isMobile={isMobile}
        />
      )}
    </div>
  );
}
