'use client';

import React from 'react';
import { BaseButton } from '@/components/atoms/button';
import {
  BaseAvatar,
  BaseAvatarImage,
  BaseAvatarFallback,
} from '@/components/atoms/avatar';
import DummyCertificate from '@/assets/images/dummy-certificate.png';
import Image from 'next/image';
import { IconArrow } from '@/assets/icons/IconArrow';
import dayjs from 'dayjs';

type CertificateDetailProps = {
  title: string;
  recipientName: string;
  recipientEmail: string;
  issuedDate: string;
  expiredDate: string;
  description: string;
  skills: string[];
  onBack: () => void;
};

export default function CertificateDetail({
  title,
  recipientName,
  recipientEmail,
  issuedDate,
  expiredDate,
  description,
  skills,
  onBack,
}: Readonly<CertificateDetailProps>) {
  return (
    <div className="flex flex-col gap-4 w-full">
      <BaseButton
        type="button"
        onClick={onBack}
        className="w-fit text-xs text-[#F7941E] border border-[#F7941E] bg-white hover:bg-[#FFF3E6] rounded-md px-3 py-1"
      >
        <IconArrow /> Kembali
      </BaseButton>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sertifikat */}
        <div className="flex-1">
          <Image
            src={DummyCertificate}
            alt="certificate"
            className="w-full"
            width={841}
            height={595}
          />
        </div>

        {/* Sidebar Info */}
        <div className="w-full lg:max-w-[265px] flex flex-col gap-4">
          <div className="border border-[#EAEAEA] rounded-md p-4 flex flex-col gap-3">
            <div className="flex flex-col gap-3">
              <p className="text-sm font-semibold text-[#3C3C3C]">
                Certificate Recipient
              </p>
              <div className="flex items-center gap-2">
                <BaseAvatar className="w-12 h-12">
                  <BaseAvatarImage src="https://github.com/shadcn.png" />
                  <BaseAvatarFallback>
                    {recipientName
                      .split(' ')
                      .map((s) => s[0])
                      .slice(0, 2)
                      .join('')}
                  </BaseAvatarFallback>
                </BaseAvatar>
                <div>
                  <p className="text-sm font-medium text-[#3C3C3C]">
                    {recipientName}
                  </p>
                  <p className="text-xs text-[#767676]">{recipientEmail}</p>
                </div>
              </div>
            </div>

            <div className="w-full flex flex-row lg:justify-center gap-3 py-2 px-4 lg:px-3 border border-[#DEDEDE] rounded-sm">
              <div className="w-full">
                <p className="text-[#767676] text-[10px] leading-[14px]">
                  Issued Date:
                </p>
                <p className="text-[#3C3C3C] text-xs font-semibold text-nowrap">
                  {dayjs(issuedDate).format('DD MMMM YYYY')}
                </p>
              </div>
              <div className="border border-[#DEDEDE]"></div>
              <div className="w-full">
                <p className="text-[#EA2B1F] text-[10px] leading-[14px]">
                  Issued Date:
                </p>
                <p className="text-[#EA2B1F] text-xs font-semibold text-nowrap">
                  {dayjs(expiredDate).format('DD MMMM YYYY')}
                </p>
              </div>
            </div>

            <BaseButton className="bg-[#F7941E] h-11 text-white w-full p-2 rounded-md">
              Download Certificate
            </BaseButton>
          </div>

          <div className="border border-[#DEDEDE] rounded-md p-4 flex flex-col gap-6">
            <p className="font-semibold text-[#3C3C3C]">{title}</p>
            <p className="text-sm text-[#767676]">{description}</p>
            <div className="flex flex-col gap-4">
              <p className="text-xs text-[#3C3C3C] font-semibold">
                Related Skills
              </p>
              <div className="flex flex-wrap gap-3">
                {skills.map((s) => (
                  <span
                    key={s}
                    className="p-2 rounded-md border border-[#DEDEDE] text-sm text-[#3C3C3C]"
                  >
                    {s}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
