import { BaseBadge } from '@/components/atoms/badge';
import { ChevronDown, Clock, List } from 'lucide-react';
import Image from 'next/image';
import Rating from './rating';
import ProgressBelajar from './progress-belajar';

export type Course = {
  id: string;
  title: string;
  cover: string;
  module: string;
  level: string;
  tagsCount: number;
  sections: number;
  durationLabel: string;
  rating: number;
  ratingCount: number;
  progressPct: number;
  priority?: 'High' | 'Normal';
};

export default function CourseCard({ data }: Readonly<{ data: Course }>) {
  return (
    <div className="bg-white border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <div className="relative w-full h-44">
        <Image
          src={data.cover}
          alt={data.title}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 33vw"
        />
      </div>

      <div className="flex flex-col">
        <div className="p-4 flex flex-col gap-2 md:gap-5">
          <div className="flex gap-2 flex-wrap xl:flex-nowrap">
            <BaseBadge className="rounded-sm font-normal text-[#FB9223] bg-[#FFF4E9] py-[3px] px-2">
              {data.module}
            </BaseBadge>
            <BaseBadge className="rounded-sm text-[#2C598D] bg-[#EAEEF4] max-w-[55px]">
              {data.level}
            </BaseBadge>
            <BaseBadge
              variant="secondary"
              className="rounded-sm flex justify-between gap-2 max-w-[75px]"
            >
              <span>{data.tagsCount} Tags</span>
              <span>
                <ChevronDown size={16} />
              </span>
            </BaseBadge>
          </div>

          <span className="font-bold text-base line-clamp-2 lg:min-h-12">
            {data.title}
          </span>

          <div className="flex flex-col gap-3">
            <div className="flex gap-3 flex-wrap text-[#767676]">
              <div className="flex gap-2 items-center">
                <List size={16} />
                <span className="text-sm">{data.sections} Section</span>
              </div>
              <div className="flex gap-2 items-center">
                <Clock size={16} />
                <span className="text-sm">{data.durationLabel}</span>
              </div>
            </div>

            <Rating
              value={data.rating}
              count={data.ratingCount}
            />
          </div>
        </div>

        <ProgressBelajar progress={data.progressPct} />
      </div>
    </div>
  );
}
