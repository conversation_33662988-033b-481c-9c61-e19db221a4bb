'use client';

import React, { useMemo } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { formatDateID } from '@/hooks';
import { IconCalendarDayOne } from '@/assets/icons/IconCalendarDayOne';

type InClassItem = {
  id: string;
  title: string;
  batch: string;
  dateISO: string;
  preTest: number;
  postTest: number;
  inClassPoint: number;
};

const ITEMS: InClassItem[] = [
  {
    id: '1',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 1',
    dateISO: '2025-08-01',
    preTest: 0,
    postTest: 20,
    inClassPoint: 75,
  },
  {
    id: '2',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 1',
    dateISO: '2025-08-02',
    preTest: 0,
    postTest: 20,
    inClassPoint: 75,
  },
  {
    id: '3',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 1',
    dateISO: '2025-08-03',
    preTest: 0,
    postTest: 20,
    inClassPoint: 75,
  },
  {
    id: '4',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 1',
    dateISO: '2025-08-04',
    preTest: 0,
    postTest: 20,
    inClassPoint: 75,
  },
  {
    id: '5',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 1',
    dateISO: '2025-08-05',
    preTest: 0,
    postTest: 20,
    inClassPoint: 75,
  },
  {
    id: '6',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 2',
    dateISO: '2025-08-06',
    preTest: 10,
    postTest: 30,
    inClassPoint: 80,
  },
  {
    id: '7',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 2',
    dateISO: '2025-08-07',
    preTest: 15,
    postTest: 35,
    inClassPoint: 82,
  },
  {
    id: '8',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 2',
    dateISO: '2025-08-08',
    preTest: 12,
    postTest: 32,
    inClassPoint: 81,
  },
  {
    id: '9',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 3',
    dateISO: '2025-08-09',
    preTest: 20,
    postTest: 40,
    inClassPoint: 85,
  },
  {
    id: '10',
    title: 'Training Technical Skill MT MCS',
    batch: 'Batch 3',
    dateISO: '2025-08-10',
    preTest: 18,
    postTest: 38,
    inClassPoint: 84,
  },
];

export default function InClassTraining() {
  const [visibleCount, setVisibleCount] = React.useState<number>(5);
  const visibleItems = useMemo(
    () => ITEMS.slice(0, visibleCount),
    [visibleCount]
  );

  const canLoadMore = visibleCount < ITEMS.length;
  const loadMore = () => setVisibleCount((c) => Math.min(c + 5, ITEMS.length));

  return (
    <div className="w-full">
      <div className="overflow-hidden bg-white flex flex-col max-h-[800px] md:max-h-[600px]">
        <div className="hidden xl:block bg-white sticky top-0 z-10">
          <div
            className={`grid xl:grid-cols-[minmax(0,1fr)_100px_100px_100px] 2xl:grid-cols-[minmax(0,1fr)_140px_140px_140px] items-center py-4 ${
              canLoadMore ? 'pr-4' : 'pr-8'
            } text-xs text-[#8C8C8C]`}
          >
            <div className="font-medium text-left"> </div>
            <div className="text-xs text-[#3C3C3C] font-medium">Pre-Test</div>
            <div className="text-xs text-[#3C3C3C] font-medium">Post-Test</div>
            <div className="text-xs text-[#3C3C3C] font-medium">
              In-class Point
            </div>
          </div>
        </div>

        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          <div className="pb-4 space-y-4">
            {visibleItems.map((it) => (
              <div
                key={it.id}
                className={`flex flex-col justify-start items-start xl:items-center xl:grid xl:grid-cols-[minmax(0,1fr)_100px_100px_100px] 2xl:grid-cols-[minmax(0,1fr)_140px_140px_140px] rounded-md border border-[#DEDEDE] bg-white p-4`}
              >
                <div className="flex flex-row gap-6 items-center pb-4 xl:pb-0">
                  <div className="flex flex-col gap-1 min-w-[100px] md:min-w-[200px]">
                    <p className="text-xs font-semibold text-[#F7941E]">
                      {it.title}
                    </p>
                    <div className="flex flex-row items-center gap-1">
                      <IconCalendarDayOne />
                      <span className="text-xs text-[#B1B1B1]">
                        {formatDateID(it.dateISO)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-center">
                    <p className="rounded-sm bg-[#FEF9E9] px-2 py-[3px] text-xs text-[#AE861A] text-nowrap">
                      {it.batch}
                    </p>
                  </div>
                </div>

                <div className="text-left text-xs xl:font-semibold text-[#767676] flex gap-2 items-center xl:block mb-[10px] xl:mb-0">
                  <p className="xl:hidden">Pre-Test</p>
                  <p className="xl:hidden">:</p>
                  <p>{it.preTest}</p>
                </div>
                <div className="text-left text-xs xl:font-semibold text-[#767676] flex gap-2 items-center xl:block mb-[10px] xl:mb-0">
                  <p className="xl:hidden">Pre-Test</p>
                  <p className="xl:hidden">:</p>
                  <p>{it.postTest}</p>
                </div>
                <div className="text-left text-xs xl:font-semibold text-[#767676] flex gap-2 items-center xl:block">
                  <p className="xl:hidden">Pre-Test</p>
                  <p className="xl:hidden">:</p>
                  <p>{it.inClassPoint}</p>
                </div>
              </div>
            ))}
          </div>

          {canLoadMore && (
            <div className="flex items-center justify-center pt-4 w-full border-t border-[#EAEAEA] bg-white">
              <BaseButton
                type="button"
                onClick={loadMore}
                className="w-fit h-fit text-xs p-0 text-[#F7941E] bg-white hover:bg-white hover:opacity-80"
              >
                Muat lebih banyak
              </BaseButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
