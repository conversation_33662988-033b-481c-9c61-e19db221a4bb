'use client';

import React, { useMemo } from 'react';
import { BaseButton } from '@/components/atoms/button';
import CourseCard, { Course } from './course-card';

const ALL_COURSES: Course[] = [
  {
    id: 'c1',
    title: 'The Basic of User Interface Design',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 2',
    tagsCount: 5,
    sections: 8,
    durationLabel: '3 Jam 19 Menit',
    rating: 4.5,
    ratingCount: 1278,
    progressPct: 75,
  },
  {
    id: 'c2',
    title: 'Design System Fundamentals',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 1',
    tagsCount: 3,
    sections: 6,
    durationLabel: '2 Jam 10 Menit',
    rating: 4.6,
    ratingCount: 864,
    progressPct: 30,
  },
  {
    id: 'c3',
    title: 'Intro to Usability Testing',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 2',
    tagsCount: 4,
    sections: 7,
    durationLabel: '2 Jam 45 Menit',
    rating: 4.4,
    ratingCount: 523,
    progressPct: 50,
  },
  {
    id: 'c4',
    title: 'Figma for Rapid Prototyping',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 3',
    tagsCount: 6,
    sections: 10,
    durationLabel: '4 Jam 05 Menit',
    rating: 4.7,
    ratingCount: 1920,
    progressPct: 12,
  },
  {
    id: 'c5',
    title: 'Accessibility in Practice',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 2',
    tagsCount: 5,
    sections: 9,
    durationLabel: '3 Jam 00 Menit',
    rating: 4.8,
    ratingCount: 610,
    progressPct: 90,
  },
  {
    id: 'c6',
    title: 'UX Writing Essentials',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 1',
    tagsCount: 2,
    sections: 5,
    durationLabel: '1 Jam 55 Menit',
    rating: 4.3,
    ratingCount: 301,
    progressPct: 45,
  },
  {
    id: 'c7',
    title: 'Responsive Layout Patterns',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 2',
    tagsCount: 4,
    sections: 8,
    durationLabel: '2 Jam 30 Menit',
    rating: 4.5,
    ratingCount: 1002,
    progressPct: 65,
  },
  {
    id: 'c8',
    title: 'Color & Typography for UI',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 1',
    tagsCount: 3,
    sections: 6,
    durationLabel: '2 Jam 05 Menit',
    rating: 4.2,
    ratingCount: 270,
    progressPct: 20,
  },
  {
    id: 'c9',
    title: 'Design Handoff to Developer',
    cover: '/images/image.png',
    module: 'Core Module',
    level: 'Level 3',
    tagsCount: 5,
    sections: 9,
    durationLabel: '3 Jam 10 Menit',
    rating: 4.9,
    ratingCount: 2110,
    progressPct: 33,
    priority: 'High',
  },
];

export default function OnlineLearning() {
  const [visibleCount, setVisibleCount] = React.useState<number>(6);
  const visibleItems = useMemo(
    () => ALL_COURSES.slice(0, visibleCount),
    [visibleCount]
  );

  const canLoadMore = visibleCount < ALL_COURSES.length;
  const loadMore = () =>
    setVisibleCount((c) => Math.min(c + 5, ALL_COURSES.length));

  return (
    <div className="w-full">
      {/* Kontainer utama: fleks kolom + tinggi maksimum + overflow internal */}
      <div className="overflow-hidden bg-white flex flex-col rounded-md max-h-[800px] md:max-h-[600px]">
        {/* Area scrollable */}
        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          {/* Grid kartu di dalam area scroll */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 pb-4 md:pb-6 gap-4 md:gap-6">
            {visibleItems.map((c) => (
              <CourseCard
                key={c.id}
                data={c}
              />
            ))}
          </div>

          {/* Tombol muat lebih banyak berada DI DALAM area scroll (harus scroll untuk mencapainya) */}
          {canLoadMore && (
            <div className="flex items-center justify-center pt-4 w-full border-t border-[#EAEAEA] bg-white">
              <BaseButton
                type="button"
                onClick={loadMore}
                className="w-fit h-fit text-xs p-0 text-[#F7941E] bg-white hover:bg-white hover:opacity-80"
              >
                Muat lebih banyak
              </BaseButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
