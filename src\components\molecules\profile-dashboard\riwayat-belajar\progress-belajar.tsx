import ProgressBar from '@ramonak/react-progress-bar';

export default function ProgressBelajar({
  progress,
}: Readonly<{ progress: number }>) {
  const pct = Math.max(0, Math.min(100, progress ?? 0));

  return (
    <div className="flex flex-col gap-2 py-3 px-4">
      <span className="text-[#767676] text-xs">Progress belajar</span>

      <div className="w-full">
        <ProgressBar
          completed={pct}
          maxCompleted={100}
          height="20px"
          bgColor="#FB9223"
          baseBgColor="#F5F5F5"
          borderRadius="9999px"
          className="w-full"
          customLabel={`${pct}%`}
          labelAlignment="right"
          labelColor="#FFFFFF"
          labelSize="10px"
          customLabelStyles={{ lineHeight: '14px', marginRight: 8 }}
          animateOnRender
          transitionDuration="0.3s"
        />
      </div>
    </div>
  );
}
