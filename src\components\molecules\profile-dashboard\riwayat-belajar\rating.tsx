import { Star } from 'lucide-react';

export default function Rating({
  value = 4.5,
  max = 5,
  count = 0,
}: Readonly<{
  value?: number;
  max?: number;
  count?: number;
}>) {
  return (
    <div className="flex flex-wrap gap-2 items-center">
      <div className="flex gap-1 items-center">
        <span className="text-[#F7941E] font-semibold">{value.toFixed(1)}</span>
        <div className="flex items-center gap-1">
          {Array.from({ length: max }).map((_, i) => {
            const starValue = i + 1;
            const isFull = starValue <= Math.floor(value);
            const isHalf = value < starValue && value > starValue - 1;

            return (
              <div
                key={`star-${i + 1}`}
                className="relative leading-none"
              >
                {isFull && (
                  <Star
                    size={20}
                    className="fill-[#F8BF25] text-[#F8BF25]"
                  />
                )}
                {isHalf && (
                  <div className="relative w-5 h-5">
                    <Star
                      size={20}
                      className="text-[#EBEBEB] fill-[#EBEBEB] absolute"
                    />
                    <div className="w-1/2 overflow-hidden absolute top-0 left-0">
                      <Star
                        size={20}
                        className="fill-[#F8BF25] text-[#F8BF25]"
                      />
                    </div>
                  </div>
                )}
                {!isFull && !isHalf && (
                  <Star
                    size={20}
                    className="text-[#EBEBEB] fill-[#EBEBEB]"
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
      <span className="text-[#B1B1B1] text-sm">({count})</span>
    </div>
  );
}
