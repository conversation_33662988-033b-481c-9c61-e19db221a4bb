import FilterButton from '@/components/atoms/filter-button';
import SearchBar from '@/components/atoms/search-bar';
import SortButton from '@/components/atoms/sort-button';
import { useState } from 'react';

export default function ToolbarFilter() {
  const [category, setCategory] = useState('judul');
  const [keyword, setKeyword] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [sort, setSort] = useState('start_desc');

  return (
    <div className="flex flex-col xl:flex-row items-center gap-3 xl:gap-[46px]">
      <SearchBar
        categories={[
          { label: 'Cari berdasarkan', value: 'judul' },
          { label: 'Instruktur', value: 'instruktur' },
          { label: 'Kategori', value: 'kategori' },
        ]}
        category={category}
        onCategoryChange={setCategory}
        value={keyword}
        onChange={setKeyword}
        onSubmit={() => {
          console.log({ category, keyword, sort });
        }}
      />

      <div className="w-full flex flex-row justify-between md:justify-end gap-3">
        <FilterButton
          active={filterOpen}
          onClick={() => setFilterOpen((s) => !s)}
        />

        <SortButton
          options={[
            { label: 'Tanggal Mulai Terbaru', value: 'start_desc' },
            { label: 'Tanggal Mulai Terlama', value: 'start_asc' },
            { label: 'A → Z', value: 'az' },
            { label: 'Z → A', value: 'za' },
          ]}
          value={sort}
          onChange={setSort}
        />
      </div>
    </div>
  );
}
