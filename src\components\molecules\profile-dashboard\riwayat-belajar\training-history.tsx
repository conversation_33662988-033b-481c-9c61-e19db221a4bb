'use client';

import React, { useMemo, useState } from 'react';
import { BaseButton } from '@/components/atoms/button';
import { formatDateID } from '@/hooks';
import { IconCalendarDayOne } from '@/assets/icons/IconCalendarDayOne';
import { cn } from '@/lib/utils';

export type InClassItem = {
  id: string;
  title: string;
  status: 'online' | 'offline';
  dateStart: string;
  dateEnd: string;
  duration: number;
  institution: string;
};

const ITEMS: InClassItem[] = [
  {
    id: '1',
    title: 'Training Basic Excel',
    status: 'online',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '2',
    title: 'Training Basic Excel',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '3',
    title: 'Training Technical Skill MT MCS',
    status: 'online',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '4',
    title: 'Training Basic Excel',
    status: 'online',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '5',
    title: 'Training Technical Skill MT MCS',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '6',
    title: 'Training Technical Skill MT MCS',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '7',
    title: 'Training Basic Excel',
    status: 'online',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '8',
    title: 'Training Basic Excel',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '9',
    title: 'Training Basic Excel',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
  {
    id: '10',
    title: 'Training Technical Skill MT MCS',
    status: 'offline',
    dateStart: '2025-08-01',
    dateEnd: '2025-08-03',
    duration: 8,
    institution: 'ACC',
  },
];

export default function TrainingHistory({
  onSelect,
}: Readonly<{ onSelect: (item: InClassItem) => void }>) {
  const [visibleCount, setVisibleCount] = useState<number>(5);

  const visibleItems = useMemo(
    () => ITEMS.slice(0, visibleCount),
    [visibleCount]
  );

  const canLoadMore = visibleCount < ITEMS.length;
  const loadMore = () => setVisibleCount((c) => Math.min(c + 5, ITEMS.length));

  return (
    <div className="w-full">
      <div className="overflow-hidden bg-white flex flex-col max-h-[800px] md:max-h-[600px]">
        <div className="flex-1 min-h-0 overflow-y-auto scrollbar-hide">
          <div className="space-y-4 pb-4">
            {visibleItems.map((it) => (
              <div
                key={it.id}
                className="w-full flex flex-row rounded-md border border-[#DEDEDE] bg-white p-4"
              >
                <div className="w-full flex flex-col lg:flex-row gap-4 xl:gap-14 items-start md:items-center">
                  <div className="w-full lg:w-fit flex flex-row xl:gap-6">
                    <div className="flex flex-col gap-1 min-w-[200px]">
                      <button
                        className="hidden lg:block text-xs font-semibold text-left text-[#F7941E] cursor-pointer"
                        onClick={() => onSelect(it)}
                      >
                        {it.title}
                      </button>
                      <p className="lg:hidden text-xs font-semibold text-[#F7941E]">
                        {it.title}
                      </p>
                      <div className="flex flex-row items-center gap-1">
                        <IconCalendarDayOne />
                        <span className="text-xs text-[#B1B1B1]">
                          {formatDateID(it.dateStart)} -{' '}
                          {formatDateID(it.dateEnd)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-center">
                      <p
                        className={cn(
                          'rounded-sm px-2 py-[3px] text-xs text-nowrap capitalize',
                          it.status === 'online'
                            ? 'bg-[#EAEEF4] text-[#2C598D]'
                            : 'bg-[#FEF9E9] text-[#AE861A]'
                        )}
                      >
                        {it.status}
                      </p>
                    </div>
                  </div>

                  <div className="w-full flex flex-row gap-4 lg:gap-6 xl:gap-14">
                    <div className="w-full lg:w-fit flex flex-col gap-2 text-left text-xs text-[#767676] mb-[10px] xl:mb-0">
                      <p className="text-nowrap">Durasi (jam)</p>
                      <p className="font-semibold">{it.duration}</p>
                    </div>
                    <div className="w-full lg:w-fit flex flex-col gap-2 text-left text-xs text-[#767676]">
                      <p>Institusi</p>
                      <p className="font-semibold">{it.institution}</p>
                    </div>
                  </div>

                  <BaseButton
                    className="w-full py-3 lg:hidden text-xs font-medium text-[#F7941E] bg-white rounded-md shadow-none border border-[#F7941E] hover:bg-white hover:opacity-80"
                    onClick={() => onSelect(it)}
                    type="button"
                  >
                    Unduh Sertifikat
                  </BaseButton>
                </div>
              </div>
            ))}
          </div>

          {canLoadMore && (
            <div className="flex items-center justify-center pt-4 w-full border-t border-[#EAEAEA] bg-white">
              <BaseButton
                type="button"
                onClick={loadMore}
                className="w-fit h-fit text-xs p-0 text-[#F7941E] bg-white hover:bg-white hover:opacity-80"
              >
                Muat lebih banyak
              </BaseButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
