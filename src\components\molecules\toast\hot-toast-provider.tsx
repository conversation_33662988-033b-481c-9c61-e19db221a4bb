'use client';

import { Toaster } from 'react-hot-toast';
import { useEffect, useState } from 'react';

export default function HotToastProvider() {
  const [position, setPosition] = useState<'bottom-right' | 'bottom-center'>(
    'bottom-right'
  );

  useEffect(() => {
    const checkMobile = () => {
      const isMobile = window.innerWidth <= 640;
      setPosition(isMobile ? 'bottom-center' : 'bottom-right');
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <Toaster
      position={position}
      gutter={12}
      toastOptions={{ duration: 3000 }}
    />
  );
}
