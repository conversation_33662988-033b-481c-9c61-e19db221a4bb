import toast from 'react-hot-toast';
import { IconCheck } from '@/assets/icons/IconCheck';
import { IconX } from '@/assets/icons/IconX';
import { IconXMark } from '@/assets/icons/IconXMark';

type Variant = 'success' | 'error';

const colors = {
  success: { accent: '#2BBD71', icon: '#16a34a', border: '#E6F7EE' },
  error: { accent: '#EF4444', icon: '#dc2626', border: '#FDECEC' },
};

function Card({
  tId,
  variant,
  title,
  description,
}: Readonly<{
  tId: string;
  variant: Variant;
  title: string;
  description?: string;
}>) {
  const c = colors[variant];

  return (
    <div
      className="relative overflow-hidden rounded-md border bg-white shadow-[0_6px_24px_rgba(0,0,0,0.06)]"
      style={{
        borderColor: c.border,
        width: 350,
        height: 60,
      }}
      role="status"
      aria-live="polite"
    >
      {/* strip kiri */}
      <span
        className="absolute inset-y-0 left-0 w-2"
        style={{ background: c.accent }}
      />

      {/* body */}
      <div className="flex items-start gap-3 p-3 pl-5">
        <span
          className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full"
          style={{
            color: c.icon,
            background: `${c.accent}1A`,
            minWidth: 20,
          }}
        >
          {variant === 'success' ? (
            <IconCheck
              size={16}
              color={c.icon}
            />
          ) : (
            <IconXMark
              size={16}
              color={c.icon}
            />
          )}
        </span>

        <div className="flex-1 overflow-hidden">
          <div
            className="truncate"
            style={{
              fontSize: 14,
              fontWeight: 500, // medium
              color: '#3C3C3C',
              lineHeight: '18px',
            }}
          >
            {title}
          </div>
          {description && (
            <div
              className="truncate"
              style={{
                fontSize: 14,
                fontWeight: 400, // regular
                color: '#767676',
                lineHeight: '18px',
              }}
            >
              {description}
            </div>
          )}
        </div>

        <button
          aria-label="Close"
          onClick={() => toast.dismiss(tId)}
          className="ml-auto opacity-60 transition-opacity hover:opacity-100 cursor-pointer"
        >
          <IconX
            size={20}
            color="#767676"
          />
        </button>
      </div>
    </div>
  );
}

export function notifyHotSuccess(title: string, description?: string) {
  toast.custom(
    (t) =>
      t.visible ? (
        <Card
          tId={t.id}
          variant="success"
          title={title}
          description={description}
        />
      ) : null,
    { duration: 3000 }
  );
}

export function notifyHotError(title: string, description?: string) {
  toast.custom(
    (t) =>
      t.visible ? (
        <Card
          tId={t.id}
          variant="error"
          title={title}
          description={description}
        />
      ) : null,
    { duration: 3000 }
  );
}
