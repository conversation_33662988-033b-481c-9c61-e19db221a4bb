'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { BaseInput } from '@/components/atoms/input';
import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
import Image from 'next/image';
import LogoNew from '@/assets/images/logo-new.png';
import Pattern from '@/assets/images/pattern.png';
import Waves from '@/assets/images/waves.png';
import LoginAiOtpMethodDialog from '@/components/molecules/login-ai/OtpMethodDialog';
import LoginAiOtpInput from '@/components/molecules/login-ai/OtpInput';
import LoginAiStepBackButton from '@/components/molecules/login-ai/StepBackButton';
import LoginAiStepHeader from '@/components/molecules/login-ai/StepHeader';
import {
  LoginAiNotifyError,
  LoginAiNotifySuccess,
} from '@/components/molecules/login-ai/Toast';
import { IconCopy } from '@/assets/icons/IconCopy';
import LoginAiInstructionDialog from '@/components/molecules/login-ai/InstructionDialog';
import { useMediaQuery } from '@/hooks';
import QRCode from 'react-qr-code';
import { authenticator } from 'otplib';

const dummyUsers = [
  {
    npk: '123456',
    name: 'Katon',
    email: '<EMAIL>',
    phone: '+6282188888888',
    password: 'lemon123',
    status: 'active',
  },
  {
    npk: '654321',
    name: 'New User',
    email: '<EMAIL>',
    phone: '+6282180000000',
    password: '',
    status: 'active',
  },
  {
    npk: '987654',
    name: 'Candidate',
    email: '<EMAIL>',
    phone: '+6282181111111',
    password: '',
    status: 'inactive',
  },
];

type Step1Values = { email: string };
type Step2Values = { password: string };
type Step3Values = { otp: string };
type Step4Values = { otpMfa: string };
type Step5Values = { newPassword: string; confirmPassword: string };

const schemaStep1 = yup.object({
  email: yup.string().required('Cannot find your NPK, Email, or Phone Number'),
});

const schemaStep2 = yup.object({
  password: yup
    .string()
    .min(6, 'Password minimal 6 karakter')
    .required('Password wajib'),
});

const schemaStep3 = yup.object({
  otp: yup
    .string()
    .matches(/^\d{6}$/, 'You have entered an invalid OTP. Please try again!')
    .required('You have entered an invalid OTP. Please try again!'),
});

const schemaStep4 = yup.object({
  otpMfa: yup
    .string()
    .matches(/^\d{6}$/, 'You have entered an invalid OTP. Please try again!')
    .required('You have entered an invalid OTP. Please try again!'),
});

const schemaStep5 = yup.object({
  newPassword: yup
    .string()
    .min(
      8,
      'Password must be at least 8 character and contains uppercase, lowercase letters, and numbers.'
    )
    .required(
      'Password must be at least 8 character and contains uppercase, lowercase letters, and numbers.'
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('newPassword')], 'Passwords do not match')
    .required('Passwords do not match'),
});

const LoginAiComp: React.FC = () => {
  const isMobile = useMediaQuery('(max-width: 767px)');

  const step1Form = useForm<Step1Values>({
    resolver: yupResolver(schemaStep1),
  });
  const step2Form = useForm<Step2Values>({
    resolver: yupResolver(schemaStep2),
  });
  const step3Form = useForm<Step3Values>({
    resolver: yupResolver(schemaStep3),
  });
  const step4Form = useForm<Step4Values>({
    resolver: yupResolver(schemaStep4),
  });
  const step5Form = useForm<Step5Values>({
    resolver: yupResolver(schemaStep5),
  });

  const [secretKey, setSecretKey] = useState('');
  const [step, setStep] = useState<1 | 2 | 3 | 4 | 5>(1);
  const [otpDialogOpen, setOtpDialogOpen] = useState(false);
  const [instructionDialogOpen, setInstructionDialogOpen] = useState(false);
  const [otpMethod, setOtpMethod] = useState<'phone' | 'email' | null>(null);
  const [resendTimer, setResendTimer] = useState<number>(90);
  const [selectedUser, setSelectedUser] = useState<
    null | (typeof dummyUsers)[0]
  >(null);
  const issuerName = 'ACC LEMON';

  const onSubmitStep1 = (data: Step1Values) => {
    const found = dummyUsers.find(
      (user) =>
        user.email === data.email ||
        user.npk === data.email ||
        user.phone === data.email
    );
    if (!found) {
      alert('User not found');
      return;
    }
    step2Form.reset({ password: '' });
    step2Form.clearErrors();
    step3Form.reset({ otp: '' });
    step3Form.clearErrors();
    step4Form.reset({ otpMfa: '' });
    step4Form.clearErrors();

    setSelectedUser(found);
    if (found.password && found.status === 'active') setStep(2);
    if (!found.password && found.status === 'active') setOtpDialogOpen(true);
    if (!found.password && found.status === 'inactive') setStep(4);
  };

  const onSubmitStep2 = (data: Step2Values) => {
    if (selectedUser && data.password === selectedUser.password)
      alert('Login berhasil!');
    else alert('Password salah!');
  };

  const onSubmitStep3 = (data: Step3Values) => {
    if (data.otp !== '123456') {
      LoginAiNotifyError(
        'Invalid OTP code',
        'You have entered an invalid OTP. Please try again!'
      );
      return;
    }
    step5Form.reset({ newPassword: '', confirmPassword: '' });
    step5Form.clearErrors();
    LoginAiNotifySuccess('Success', 'Your OTP has been verified');
    setStep(5);
  };

  const onSubmitStep4 = (data: Step4Values) => {
    if (data.otpMfa !== '123456') {
      LoginAiNotifyError(
        'Invalid OTP code',
        'You have entered an invalid OTP. Please try again!'
      );
      return;
    }
    step5Form.reset({ newPassword: '', confirmPassword: '' });
    step5Form.clearErrors();
    LoginAiNotifySuccess('Success', 'Your OTP has been verified');
    setStep(5);
  };

  const onSubmitStep5 = (data: Step5Values) => {
    console.log('Step 5 data:', data);
  };

  const formatTimer = (seconds: number) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return `${m}:${s}`;
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (step === 3 && resendTimer > 0) {
      timer = setInterval(() => setResendTimer((prev) => prev - 1), 1000);
    }
    return () => clearInterval(timer);
  }, [step, resendTimer]);

  useEffect(() => {
    if (step === 4) {
      setSecretKey(authenticator.generateSecret());
    }
  }, [step, selectedUser?.email]);

  const keyUri = useMemo(() => {
    if (!secretKey || !selectedUser?.email) return '';
    return authenticator.keyuri(selectedUser.email, issuerName, secretKey);
  }, [secretKey, selectedUser?.email]);

  const copySecret = async () => {
    try {
      await navigator.clipboard.writeText(secretKey);
      LoginAiNotifySuccess('Copied', 'Secret key copied to clipboard');
    } catch {
      LoginAiNotifyError('Copy failed', 'Could not copy the secret key');
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-white">
      {/* LEFT SIDE — DESKTOP ONLY */}
      <div
        className="hidden md:flex w-[57.5%] relative items-center justify-end overflow-hidden"
        style={{
          backgroundImage: `url(${Waves.src}), url(${Pattern.src})`,
          backgroundRepeat: 'no-repeat, no-repeat',
          backgroundSize: 'auto 100%, cover',
          backgroundPosition: 'left center, center',
        }}
      >
        <div className="relative z-10 flex justify-center pb-[180px] pr-[50px]">
          <Image
            src={LogoNew}
            alt="Logo"
            width={2674}
            height={1414}
            priority
            className="h-auto"
            style={{ width: 'clamp(300px, 46.4vw, 668px)' }}
          />
        </div>
      </div>

      {/* RIGHT SIDE — FORMS (mobile contains hero bg + bottom sheet) */}
      <div className="relative flex w-full md:w-[42.5%] min-h-[100dvh] md:min-h-screen md:px-4 lg:px-8 xl:px-16">
        {/* MOBILE HERO (background inside right side) */}
        <div
          className="absolute h-full inset-x-0 top-0 md:hidden overflow-hidden"
          style={{
            backgroundImage: `url(${Waves.src}), url(${Pattern.src})`,
            backgroundRepeat: 'no-repeat, no-repeat',
            backgroundSize: 'auto 175%, cover',
            backgroundPosition: 'left bottom, center',
          }}
        >
          <div className="absolute top-4 left-0 right-0 flex justify-center">
            <Image
              src={LogoNew}
              alt="Logo"
              width={600}
              height={320}
              priority
              className="h-auto"
              style={{ width: 'clamp(188px, 45vw, 260px)' }}
            />
          </div>
        </div>

        {/* MOBILE bottom sheet; desktop becomes centered content */}
        <div
          className="
            absolute inset-x-0 bottom-0 md:static
            w-full bg-white
            rounded-t-2xl shadow-[0_-6px_24px_rgba(0,0,0,0.06)]
            py-6 px-4 
            md:rounded-none md:shadow-none md:p-0 md:m-auto md:max-w-lg
          "
        >
          {/* STEP 1 */}
          {step === 1 && (
            <>
              <LoginAiStepHeader
                title={`Welcome back!`}
                subtitle={`Feel the taste of knowledge with Lemon`}
              />
              <form onSubmit={step1Form.handleSubmit(onSubmitStep1)}>
                <div
                  className={`${
                    step1Form.formState.errors.email ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <BaseLabel
                    htmlFor="email"
                    className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
                  >
                    NPK, Email, or Phone Number
                  </BaseLabel>
                  <BaseInput
                    id="email"
                    type="text"
                    placeholder="Input your NPK, Email, or Phone Number"
                    {...step1Form.register('email')}
                    className={`w-full text-sm md:text-base ${
                      step1Form.formState.errors.email ? 'border-[#EA2B1F]' : ''
                    }`}
                  />
                  {step1Form.formState.errors.email && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step1Form.formState.errors.email.message}
                    </p>
                  )}
                </div>
                <BaseButton
                  type="submit"
                  className="w-full bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
                >
                  Continue
                </BaseButton>
              </form>
            </>
          )}

          {/* STEP 2 */}
          {step === 2 && (
            <>
              <LoginAiStepBackButton onBack={() => setStep(1)} />
              <LoginAiStepHeader
                title={`Welcome back, ${selectedUser?.name?.split(' ')[0]}`}
                subtitle={`Please fill in your password`}
              />
              <form onSubmit={step2Form.handleSubmit(onSubmitStep2)}>
                <div
                  className={`${
                    step2Form.formState.errors.password ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <BaseLabel
                    htmlFor="password"
                    className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
                  >
                    Password
                  </BaseLabel>
                  <BaseInput
                    id="password"
                    type="password"
                    placeholder="Input password"
                    {...step2Form.register('password')}
                    className={`w-full text-sm md:text-base ${
                      step2Form.formState.errors.password
                        ? 'border-[#EA2B1F]'
                        : ''
                    }`}
                  />
                  {step2Form.formState.errors.password && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step2Form.formState.errors.password.message}
                    </p>
                  )}
                </div>

                <BaseButton
                  type="submit"
                  className="w-full bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer mb-4 md:mb-6"
                >
                  Login
                </BaseButton>

                <div className="flex justify-start items-center gap-2">
                  <p className="text-[#3C3C3C] text-xs md:text-base font-medium">
                    {`Don't remember your password?`}
                  </p>
                  <button
                    className="text-[#F7941E] text-xs md:text-base cursor-pointer"
                    onClick={() => setStep(4)}
                  >
                    Forgot Password
                  </button>
                </div>
              </form>
            </>
          )}

          {/* STEP 3 */}
          {step === 3 && (
            <>
              <LoginAiStepBackButton onBack={() => setStep(1)} />
              <LoginAiStepHeader
                title={`Welcome to Lemon, ${selectedUser?.name?.split(' ')[0]}`}
                subtitle={`Let's create a password for you`}
                addOn={
                  <>
                    We have sent an OTP Code to{' '}
                    <span className="font-bold">
                      {selectedUser ? selectedUser[otpMethod || 'phone'] : ''}
                    </span>
                    .<br />
                    Please check your message and input the OTP Code here.
                  </>
                }
              />
              <form onSubmit={step3Form.handleSubmit(onSubmitStep3)}>
                <div
                  className={`${
                    step3Form.formState.errors.otp ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <LoginAiOtpInput
                    value={step3Form.watch('otp')}
                    onChange={(val) => step3Form.setValue('otp', val)}
                    error={step3Form.formState.errors.otp?.message}
                  />
                  {step3Form.formState.errors.otp && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step3Form.formState.errors.otp.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-4">
                  <BaseButton
                    type="submit"
                    className="w-full bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
                  >
                    Verify OTP Code
                  </BaseButton>
                  <BaseButton
                    type="button"
                    onClick={() => {
                      LoginAiNotifySuccess('Success', 'Your OTP has been sent');
                      setResendTimer(90);
                    }}
                    disabled={resendTimer > 0}
                    className={`w-full text-xs md:text-base bg-white border border-[#DEDEDE] text-[#3C3C3C] h-11 md:h-12 rounded-[8px] hover:bg-white ${
                      resendTimer > 0
                        ? 'bg-[#0202020A] border-[#A4A4A4] text-[#A4A4A4] cursor-not-allowed'
                        : 'hover:opacity-80 cursor-pointer'
                    }`}
                  >
                    {resendTimer > 0
                      ? `Resend Code in ${formatTimer(resendTimer)}`
                      : 'Resend Code'}
                  </BaseButton>
                </div>
              </form>
            </>
          )}

          {/* STEP 4 */}
          {step === 4 && (
            <>
              <LoginAiStepBackButton onBack={() => setStep(1)} />
              <p className="text-[18px] leading-[140%] font-medium text-[#3C3C3C] mb-6">
                2 Factor Authenticator
              </p>
              <div className="flex flex-col gap-3 mb-6">
                <div className="w-[158px] h-[158px] p-1 border border-[#DEDEDE] self-center bg-white flex items-center justify-center">
                  {keyUri ? (
                    <QRCode
                      value={keyUri}
                      size={148}
                    />
                  ) : (
                    <span className="text-xs text-[#767676]">Generating…</span>
                  )}
                </div>
                <div className="flex flex-col">
                  <p className="text-sm text-[#3C3C3C] mb-1">Secret Key</p>
                  <div className="flex py-3 lg:py-[14.5px] flex-row gap-4 justify-center items-center bg-[#F6F6F6]">
                    <p className="text-xs lg:text-[15px] leading-[140%] text-[#3C3C3C] font-medium">
                      {secretKey || '—'}
                    </p>
                    <button
                      className="cursor-pointer"
                      onClick={copySecret}
                      disabled={!secretKey}
                    >
                      <IconCopy size={isMobile ? 14 : 16} />
                    </button>
                  </div>
                </div>
                <div className="text-xs">
                  <p className="text-[#3C3C3C]">
                    Pindai QR code ini menggunakan Microsoft Authenticator.{' '}
                    <button
                      className="text-[#0095FF] font-medium underline cursor-pointer"
                      onClick={() => setInstructionDialogOpen(true)}
                    >
                      Lihat Instruksi
                    </button>
                  </p>
                </div>
              </div>
              <form onSubmit={step4Form.handleSubmit(onSubmitStep4)}>
                <div
                  className={`${
                    step4Form.formState.errors.otpMfa ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <BaseLabel
                    htmlFor="otpMfa"
                    className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
                  >
                    Authentication Code
                  </BaseLabel>
                  <LoginAiOtpInput
                    value={step4Form.watch('otpMfa')}
                    onChange={(val) => step4Form.setValue('otpMfa', val)}
                    error={step4Form.formState.errors.otpMfa?.message}
                  />
                  {step4Form.formState.errors.otpMfa && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step4Form.formState.errors.otpMfa.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-4">
                  <BaseButton
                    type="submit"
                    className="w-full bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
                  >
                    Verify OTP Code
                  </BaseButton>
                </div>
              </form>
            </>
          )}

          {/* STEP 5 */}
          {step === 5 && (
            <>
              <LoginAiStepBackButton onBack={() => setStep(1)} />
              <LoginAiStepHeader
                title={`Welcome back, ${selectedUser?.name?.split(' ')[0]}`}
                subtitle={`Let's create a password for you`}
              />
              <form onSubmit={step5Form.handleSubmit(onSubmitStep5)}>
                <div
                  className={`${
                    step5Form.formState.errors.newPassword ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <BaseLabel
                    htmlFor="newPassword"
                    className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
                  >
                    New Password
                  </BaseLabel>
                  <BaseInput
                    id="newPassword"
                    type="password"
                    placeholder="New password"
                    {...step5Form.register('newPassword')}
                    className={`w-full text-sm md:text-base ${
                      step5Form.formState.errors.newPassword
                        ? 'border-[#EA2B1F]'
                        : ''
                    }`}
                  />
                  {step5Form.formState.errors.newPassword && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step5Form.formState.errors.newPassword.message}
                    </p>
                  )}
                </div>

                <div
                  className={`${
                    step5Form.formState.errors.confirmPassword ? 'mb-5' : 'mb-6'
                  }`}
                >
                  <BaseLabel
                    htmlFor="confirmPassword"
                    className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
                  >
                    Confirm Password
                  </BaseLabel>
                  <BaseInput
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm password"
                    {...step5Form.register('confirmPassword')}
                    className={`w-full text-sm md:text-base ${
                      step5Form.formState.errors.confirmPassword
                        ? 'border-[#EA2B1F]'
                        : ''
                    }`}
                  />
                  {step5Form.formState.errors.confirmPassword && (
                    <p className="text-[#EA2B1F] text-xs mt-1">
                      {step5Form.formState.errors.confirmPassword.message}
                    </p>
                  )}
                </div>

                <BaseButton
                  type="submit"
                  className="w-full bg-[#F7941E] text-white h-11 md:h-12 rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
                >
                  Create Password & Login
                </BaseButton>
              </form>
            </>
          )}
        </div>
      </div>

      <LoginAiOtpMethodDialog
        open={otpDialogOpen}
        onClose={() => setOtpDialogOpen(false)}
        onSelect={(method) => {
          setOtpDialogOpen(false);
          setOtpMethod(method);
          setResendTimer(90);
          setStep(3);
        }}
      />

      <LoginAiInstructionDialog
        open={instructionDialogOpen}
        onClose={() => setInstructionDialogOpen(false)}
      />
    </div>
  );
};

export default LoginAiComp;
