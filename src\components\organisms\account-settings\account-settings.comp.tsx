'use client';

import React, { useState } from 'react';
import { IconUser } from '@/assets/icons/IconUser';
import { IconLock } from '@/assets/icons/IconLock';
import { IconQuestion } from '@/assets/icons/IconQuestion';
import { cn } from '@/lib/utils';
import UserInformation from '@/components/molecules/account-settings/user-information';
import ChangePassword from '@/components/molecules/account-settings/change-password';
import SupportCenter from '@/components/molecules/account-settings/support-center';

type TMenuMenu = 'user-information' | 'change-password' | 'support-center';

type MenuItem = {
  title: string;
  menu: TMenuMenu;
  icon: React.FC<{ size?: number; color?: string }>;
};

const items: MenuItem[] = [
  { title: 'User Information', menu: 'user-information', icon: IconUser },
  { title: 'Change Password', menu: 'change-password', icon: IconLock },
  { title: 'Support Center', menu: 'support-center', icon: IconQuestion },
];

const AccountSettingsComp: React.FC = () => {
  const [menu, setMenu] = useState<TMenuMenu>('user-information');

  const renderContent = () => {
    switch (menu) {
      case 'user-information':
        return <UserInformation />;
      case 'change-password':
        return <ChangePassword />;
      case 'support-center':
        return <SupportCenter />;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col gap-6 md:gap-8">
      <h4 className="text-[#3C3C3C] font-bold text-lg md:text-[26px] leading-[30px]">
        Account Settings
      </h4>

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:max-w-[272px] h-fit rounded-xl border border-[#DEDEDE] bg-white overflow-hidden">
          <ul className="py-1">
            {items.map((item) => {
              const isActive = menu === item.menu;
              const iconColor = isActive ? '#3C3C3C' : '#6B7280';

              return (
                <li key={item.menu}>
                  <button
                    type="button"
                    onClick={() => setMenu(item.menu)}
                    aria-current={isActive ? 'page' : undefined}
                    className={cn(
                      'relative flex w-full items-center gap-3 px-4 py-3 text-left transition-colors focus:outline-none cursor-pointer',
                      isActive ? 'bg-[#F5F5F5]' : 'bg-white'
                    )}
                  >
                    <span
                      aria-hidden="true"
                      className={cn(
                        'absolute left-0 top-0 h-full w-[2px] rounded-r',
                        isActive ? 'bg-[#F7941E]' : 'bg-transparent'
                      )}
                    />
                    <item.icon
                      size={18}
                      color={iconColor}
                    />
                    <span className="text-sm text-[#3C3C3C]">{item.title}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        <div className="p-5 border border-[#DEDEDE] rounded-md w-full">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AccountSettingsComp;
