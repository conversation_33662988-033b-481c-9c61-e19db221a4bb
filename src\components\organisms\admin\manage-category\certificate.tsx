"use client";
import CertificateDetail from "@/components/molecules/profile-dashboard/riwayat-belajar/certificate-detail";
import { useRouter } from "next/navigation";
import React from "react";

type Props = {
  id: string;
};

const CertificateView = ({ id }: Props) => {
  const router = useRouter();
  return (
    <CertificateDetail
      title={`Safety Training ${id}`}
      recipientName="Alice Johnson"
      recipientEmail="<EMAIL>"
      issuedDate="2025-01-10T09:30:00.000Z"
      expiredDate="2026-01-10T23:59:59.999Z"
      description="Safety Training"
      skills={["Safety", "Safety"]}
      onBack={() => router.push("/admin/manage-certificate")}
    />
  );
};

export default CertificateView;
