/**
 * Organisms Index - Central Export Hub
 * 
 * This file serves as the central export hub for all organism components in the atomic design system.
 * Organisms are complex UI components that combine molecules and atoms to form distinct sections
 * of an interface, representing complete functional units.
 * 
 * Atomic Design Level: ORGANISM EXPORTS
 * - Combines multiple molecules and atoms for complete functionality
 * - More complex than molecules, represents complete interface sections
 * - Contains business logic and state management
 * - Reusable across different pages and contexts
 * - Building blocks for page templates
 * 
 * Key Concepts:
 * - Centralized export management for all organism components
 * - Type-safe imports with TypeScript support
 * - Consistent naming conventions and organization
 * - Easy component discovery and usage
 * - Maintainable import structure
 * - Support for tree-shaking and code splitting
 * - Documentation and usage examples
 * 
 * Usage Examples:
 * ```tsx
 * // Import individual organisms
 * import { Navbar, Sidebar, CourseCard } from '@/components/organisms';
 * 
 * // Import with aliases
 * import { 
 *   Navbar as MainNavbar, 
 *   Sidebar as MainSidebar 
 * } from '@/components/organisms';
 * 
 * // Import types
 * import type { 
 *   NavbarProps, 
 *   SidebarProps, 
 *   CourseCardProps 
 * } from '@/components/organisms';
 * 
 * // Use in page components
 * function CoursePage() {
 *   return (
 *     <div>
 *       <Navbar />
 *       <div className="flex">
 *         <Sidebar />
 *         <main>
 *           <CourseCard course={courseData} />
 *           <LessonList lessons={lessonData} />
 *         </main>
 *       </div>
 *     </div>
 *   );
 * }
 * ```
 */

// ===== ORGANISM COMPONENT EXPORTS =====

/**
 * Navigation Organisms
 * Components for site navigation and menu functionality
 */
// export { Navbar } from './Navbar';
// export type { NavbarProps, 
//   // NavItem, UserMenuProps 
// } from './Navbar';

// export { Sidebar } from './Sidebar';
// export type { 
//   SidebarProps, 
//   SidebarMenuItem, 
//   SidebarUser, 
//   // SidebarSection 
// } from './Sidebar';

/**
 * Content Display Organisms
 * Components for displaying complex content and data
 */
// export { CourseCard } from './CourseCard';
// export type { 
//   CourseCardProps, 
//   Course, 
//   // Instructor, 
//   CourseProgress, 
//   // CourseStats 
// } from './CourseCard';

// export { LessonList } from './LessonList';
// export type { 
//   LessonListProps, 
//   Lesson, 
//   LessonModule, 
//   LessonProgress, 
//   LessonFilter 
// } from './LessonList';

// ===== ORGANISM COLLECTIONS =====

/**
 * Navigation organism collection
 * All navigation-related organisms grouped together
 */
export const NavigationOrganisms = {
  // Navbar,
  // Sidebar
} as const;

/**
 * Content organism collection
 * All content display organisms grouped together
 */
export const ContentOrganisms = {
  // CourseCard,
  // LessonList
} as const;

/**
 * All organisms collection
 * Complete collection of all available organisms
 */
export const AllOrganisms = {
  ...NavigationOrganisms,
  ...ContentOrganisms
} as const;

// ===== TYPE COLLECTIONS =====

/**
 * Navigation organism props types
 */
export type NavigationOrganismProps = {
  // Navbar: NavbarProps;
  // Sidebar: SidebarProps;
};

/**
 * Content organism props types
 */
export type ContentOrganismProps = {
  // CourseCard: CourseCardProps;
  // LessonList: LessonListProps;
};

/**
 * All organism props types
 */
export type OrganismProps = NavigationOrganismProps & ContentOrganismProps;

/**
 * Organism component names
 */
export type OrganismName = keyof typeof AllOrganisms;

/**
 * Organism categories
 */
export type OrganismCategory = 'navigation' | 'content';

// ===== UTILITY TYPES =====

/**
 * Generic organism component type
 */
export type OrganismComponent<T = any> = React.ComponentType<T>;

/**
 * Organism with props type
 */
export type OrganismWithProps<K extends OrganismName> = {
  component: typeof AllOrganisms[K];
  props: OrganismProps[K];
};

// ===== ORGANISM METADATA =====

/**
 * Organism metadata for documentation and tooling
 */
export const OrganismMetadata = {
  Navbar: {
    category: 'navigation' as const,
    description: 'Main navigation bar with user menu and branding',
    complexity: 'high' as const,
    dependencies: ['Button', 'Badge', 'SearchBox'],
    useCases: ['site header', 'main navigation', 'user authentication']
  },
  Sidebar: {
    category: 'navigation' as const,
    description: 'Collapsible sidebar navigation with menu items',
    complexity: 'high' as const,
    dependencies: ['Button', 'Badge'],
    useCases: ['admin panel', 'dashboard navigation', 'course navigation']
  },
  CourseCard: {
    category: 'content' as const,
    description: 'Comprehensive course information display card',
    complexity: 'high' as const,
    dependencies: ['Button', 'Badge', 'Image'],
    useCases: ['course catalog', 'course grid', 'course recommendations']
  },
  LessonList: {
    category: 'content' as const,
    description: 'Interactive lesson list with progress tracking',
    complexity: 'high' as const,
    dependencies: ['Button', 'Badge', 'SearchBox'],
    useCases: ['course content', 'lesson navigation', 'progress tracking']
  }
} as const;

/**
 * Get organisms by category
 */
export const getOrganismsByCategory = (category: OrganismCategory) => {
  return Object.entries(OrganismMetadata)
    .filter(([_, metadata]) => metadata.category === category)
    .map(([name]) => name as OrganismName);
};

/**
 * Get organism metadata
 */
export const getOrganismMetadata = (name: OrganismName) => {
  return OrganismMetadata[name];
};

// ===== DEFAULT EXPORT =====

/**
 * Default export with all organisms
 * Useful for dynamic imports or when you need all organisms
 */
export default AllOrganisms;

/**
 * Development Notes:
 * 
 * 1. Export Strategy:
 *    - Named exports for individual components and types
 *    - Grouped exports for related organisms
 *    - Default export for complete collection
 *    - Metadata exports for documentation and tooling
 *    - Utility functions for organism management
 * 
 * 2. Atomic Design Principles:
 *    - Organisms are complex components combining molecules and atoms
 *    - Each organism represents a distinct section of interface
 *    - Organisms contain business logic and state management
 *    - Building blocks for page templates and layouts
 *    - Reusable across different contexts and pages
 * 
 * 3. Import Patterns:
 *    - Use named imports for specific organisms
 *    - Import types separately for better tree-shaking
 *    - Use grouped imports for related organisms
 *    - Leverage TypeScript for type safety
 * 
 * 4. Type Safety:
 *    - All component props are properly typed
 *    - Generic types for flexible organism usage
 *    - Utility types for organism management
 *    - Metadata types for documentation
 * 
 * 5. Documentation:
 *    - Each organism has comprehensive JSDoc comments
 *    - Usage examples in component files
 *    - Metadata for automated documentation
 *    - Clear categorization and organization
 * 
 * 6. Maintenance:
 *    - Consistent naming conventions
 *    - Clear file organization
 *    - Easy to add new organisms
 *    - Supports refactoring and updates
 *    - Version-safe exports
 * 
 * 7. Testing:
 *    - All exports can be easily imported for testing
 *    - Type definitions support test type checking
 *    - Metadata supports automated test generation
 *    - Clear component boundaries for unit testing
 * 
 * Usage in Applications:
 * ```tsx
 * // Page composition with organisms
 * import { Navbar, Sidebar, CourseCard, LessonList } from '@/components/organisms';
 * 
 * function CoursePage({ course, lessons }: CoursePageProps) {
 *   return (
 *     <div className="min-h-screen bg-neutral-50">
 *       <Navbar user={currentUser} />
 *       <div className="flex">
 *         <Sidebar 
 *           menuItems={courseMenuItems}
 *           user={currentUser}
 *           collapsed={sidebarCollapsed}
 *         />
 *         <main className="flex-1 p-6">
 *           <CourseCard 
 *             course={course}
 *             userProgress={userProgress}
 *             onEnroll={handleEnroll}
 *           />
 *           <LessonList 
 *             lessons={lessons}
 *             userProgress={lessonProgress}
 *             onLessonClick={handleLessonClick}
 *           />
 *         </main>
 *       </div>
 *     </div>
 *   );
 * }
 * 
 * // Dynamic organism loading
 * const loadOrganism = async (name: OrganismName) => {
 *   const organisms = await import('@/components/organisms');
 *   return organisms[name];
 * };
 * ```
 */