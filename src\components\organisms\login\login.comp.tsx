"use client";

import React, { useEffect } from "react";
import { useLoginFlow } from "@/hooks/useLoginFlow";
import { createLoginHandlers } from "@/utils/helper/login/login-handlers";
import {
  LoginStep1,
  LoginStep2,
  LoginStep3,
  LoginStep4,
  LoginStep5,
  Lo<PERSON><PERSON>ackground,
} from "@/components/molecules/login";
import OtpMethodDialog from "@/components/molecules/login/otp-method-dialog";
import InstructionDialog from "@/components/molecules/login/instruction-dialog";
import { useSession } from "@/hooks/useSession";
import { getCookie } from "cookies-next/client";

const LoginComp: React.FC = () => {
  const loginFlow = useLoginFlow();
  const handlers = createLoginHandlers(loginFlow);
  const { authenticated, loading } = useSession();
  const needUpdatePassword = getCookie("need_update_password");

  const {
    step,
    setStep,
    step1Form,
    step2Form,
    step3Form,
    step4Form,
    step5Form,
    otpDialog,
    setOtpDialog,
    instructionDialogOpen,
    setInstructionDialogOpen,
    selectedUser,
    otpMethod,
    resendTimer,
    mfaData,
    isMfaOtp,
    formatTimer,
    // Loading states
    isVerifyUserPending,
    isLoginUserPending,
    isCheckMfaRequirementPending,
    isEnrollMfaPending,
    isSendOtpPending,
    isVerifyOtpPending,
    isVerifyMfaPending,
    isVerifyMfaSetupPending,
    isUpdatePasswordPending,
  } = loginFlow;

  // case user not finish update password
  useEffect(() => {
    if (authenticated && needUpdatePassword) {
      setStep(5);
    }
  }, [authenticated, needUpdatePassword]);

  const renderCurrentStep = () => {
    switch (step) {
      case 1:
        return (
          <LoginStep1
            form={step1Form}
            onSubmit={handlers.handleStep1Submit}
            isLoading={isVerifyUserPending}
          />
        );
      case 2:
        return (
          <LoginStep2
            form={step2Form}
            onSubmit={handlers.handleStep2Submit}
            onBack={handlers.handleBackToStep1}
            onOpenOtpMethodDialog={(type) =>
              handlers.handleOpenOtpMethodDialog(type)
            }
            selectedUser={selectedUser}
            isLoading={
              isLoginUserPending ||
              isCheckMfaRequirementPending ||
              isEnrollMfaPending
            }
          />
        );
      case 3:
        return (
          <LoginStep3
            form={step3Form}
            otpType={otpDialog.type}
            onSubmit={handlers.handleStep3Submit}
            onBack={handlers.handleBackToStep1}
            onResendOtp={handlers.handleResendOtp}
            selectedUser={selectedUser}
            otpMethod={otpMethod}
            resendTimer={resendTimer}
            formatTimer={formatTimer}
            isMfaOtp={isMfaOtp}
            isVerifyLoading={isVerifyOtpPending || isVerifyMfaPending}
            isResendLoading={isSendOtpPending}
          />
        );
      case 4:
        return (
          <LoginStep4
            form={step4Form}
            onSubmit={handlers.handleStep4Submit}
            onBack={handlers.handleBackToStep1}
            onCopySecret={handlers.handleCopySecret}
            onOpenInstructions={handlers.handleOpenInstructions}
            mfaData={mfaData}
            isLoading={isVerifyOtpPending || isVerifyMfaSetupPending}
          />
        );
      case 5:
        return (
          <LoginStep5
            form={step5Form}
            onSubmit={handlers.handleStep5Submit}
            onBack={handlers.handleBackToStep1}
            selectedUser={selectedUser}
            isLoading={isUpdatePasswordPending}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-white">
      <LoginBackground />

      {/* RIGHT SIDE — FORMS (mobile contains hero bg + bottom sheet) */}
      <div className="relative flex w-full md:w-[42.5%] min-h-[100dvh] md:min-h-screen md:px-4 lg:px-8 xl:px-16">
        {/* MOBILE bottom sheet; desktop becomes centered content */}
        <div className="absolute inset-x-0 bottom-0 md:static w-full bg-white rounded-t-2xl shadow-[0_-6px_24px_rgba(0,0,0,0.06)] py-6 px-4 md:rounded-none md:shadow-none md:p-0 md:m-auto md:max-w-lg">
          {renderCurrentStep()}
        </div>
      </div>

      <OtpMethodDialog
        open={otpDialog.open}
        onClose={() => setOtpDialog({ ...otpDialog, open: false })}
        isPending={isSendOtpPending}
        onSelect={(method) => handlers.handleSelectOtpMethod(method)}
      />

      <InstructionDialog
        open={instructionDialogOpen}
        onClose={() => setInstructionDialogOpen(false)}
      />
    </div>
  );
};

export default LoginComp;
