/**
 * Hooks Index
 *
 * This file serves as a central export hub for all custom React hooks.
 * It provides a unified interface for accessing authentication, course management,
 * and other utility hooks throughout the application.
 *
 * Key Features:
 * - Authentication hooks
 * - Course management hooks
 * - Utility hooks
 * - Performance hooks
 * - Storage hooks
 * - API hooks
 * - Form hooks
 * - UI state hooks
 *
 * Usage Examples:
 * ```tsx
 * // Import specific hooks
 * import {
 *   useAuth,
 *   useCourse,
 *   useLocalStorage,
 *   useDebounce,
 *   useApi
 * } from '@/hooks';
 *
 * // Use in components
 * const { user, login, logout } = useAuth();
 * const { course, updateCourse } = useCourse(courseId);
 * const [value, setValue] = useLocalStorage('key', defaultValue);
 * const debouncedValue = useDebounce(searchTerm, 300);
 * ```
 */

import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import 'dayjs/locale/id';

dayjs.locale('id');

export function formatDateID(iso: string) {
  const d = dayjs(iso);
  if (!d.isValid()) return iso;
  return d.format('DD MMM YYYY');
}

export const slugify = (s: string) =>
  s
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(?:^-+|-+$)/g, '');

// // ===== AUTHENTICATION HOOKS =====

// // ===== COURSE HOOKS =====

// // ===== UTILITY HOOKS =====

// /**
//  * Hook for debouncing values
//  */

// /**
//  * Hook for throttling function calls
//  */

// /**
//  * Hook for previous value tracking
//  */

// /**
//  * Hook for component mount status
//  */

// /**
//  * Hook for safe async operations
//  */

// /**
//  * Hook for toggle state
//  */

// /**
//  * Hook for counter state
//  */

// /**
//  * Hook for array state management
//  */

// // ===== STORAGE HOOKS =====

// /**
//  * Hook for localStorage
//  */

// /**
//  * Hook for sessionStorage
//  */

// // ===== API HOOKS =====

// /**
//  * Generic API hook for data fetching
//  */

// /**
//  * Hook for async operations with loading state
//  */

// // ===== FORM HOOKS =====

// /**
//  * Hook for form state management
//  */

// // ===== UI HOOKS =====

// /**
//  * Hook for window dimensions
//  */

// /**
//  * Hook for media queries
//  */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const media = window.matchMedia(query);

    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);

    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
};

// /**
//  * Hook for click outside detection
//  */

// /**
//  * Hook for keyboard shortcuts
//  */

// /**
//  * Hook for scroll position
//  */

// /**
//  * Hook for intersection observer
//  */

// // ===== PERFORMANCE HOOKS =====

// /**
//  * Hook for performance monitoring
//  */

/**
 * Development Notes:
 *
 * 1. Hook Organization:
 *    - Authentication and authorization
 *    - Course and content management
 *    - Utility and helper hooks
 *    - Storage and persistence
 *    - API and data fetching
 *    - Form and input handling
 *    - UI and interaction hooks
 *    - Performance monitoring
 *
 * 2. Type Safety:
 *    - Full TypeScript support
 *    - Generic hooks where appropriate
 *    - Proper return type definitions
 *    - Type-safe event handlers
 *
 * 3. Performance:
 *    - Memoized callbacks
 *    - Optimized dependencies
 *    - Efficient state updates
 *    - Memory leak prevention
 *
 * 4. Reusability:
 *    - Generic implementations
 *    - Configurable options
 *    - Composable patterns
 *    - Consistent APIs
 *
 * 5. Error Handling:
 *    - Safe async operations
 *    - Graceful degradation
 *    - Comprehensive logging
 *    - User-friendly errors
 *
 * Usage Examples:
 * ```tsx
 * // Authentication
 * const { user, login, logout, hasPermission } = useAuth();
 *
 * // Course management
 * const { courses, loading, fetchCourses } = useCourses();
 * const { course, updateCourse } = useCourse(courseId);
 * const { enroll, isEnrolled } = useCourseEnrollment();
 *
 * // Utilities
 * const debouncedSearch = useDebounce(searchTerm, 300);
 * const [isOpen, toggle] = useToggle(false);
 * const { count, increment } = useCounter(0);
 *
 * // Storage
 * const [settings, setSettings] = useLocalStorage('settings', defaultSettings);
 * const [sessionData, setSessionData] = useSessionStorage('session', {});
 *
 * // API
 * const { data, loading, error, ref=nulletch } = useApi(() => fetchUserData());
 * const { execute: saveData } = useAsync(saveUserData);
 *
 * // Forms
 * const { values, errors, handleChange, validate } = useForm(initialValues, rules);
 *
 * // UI
 * const { width, height } = useWindowSize();
 * const isMobile = useMediaQuery('(max-width: 768px)');
 * const isVisible = useIntersectionObserver(ref);
 *
 * // Performance
 * const { measure } = usePerformance('expensive-operation');
 * const result = measure(() => expensiveCalculation());
 * ```
 */
