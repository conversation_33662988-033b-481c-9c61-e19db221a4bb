/**
 * Authentication Hooks
 *
 * This file provides custom React hooks for authentication state management.
 * It integrates with Redux store and provides convenient methods for
 * authentication operations.
 *
 * Key Features:
 * - Authentication state access
 * - Login/logout operations
 * - User registration
 * - Token management
 * - Session handling
 * - Permission checking
 * - Loading states
 * - Error handling
 *
 * Usage Examples:
 * ```tsx
 * // Basic authentication
 * const { user, isAuthenticated, login, logout } = useAuth();
 *
 * // Login with credentials
 * const handleLogin = async () => {
 *   const result = await login({
 *     email: '<EMAIL>',
 *     password: 'password123'
 *   });
 *
 *   if (result.success) {
 *     navigate('/dashboard');
 *   }
 * };
 *
 * // Check permissions
 * const { hasPermission } = useAuth();
 * const canEdit = hasPermission('course:edit');
 *
 * // Registration
 * const { register } = useAuth();
 * const handleRegister = async () => {
 *   await register({
 *     email: '<EMAIL>',
 *     password: 'password123',
 *     name: '<PERSON>'
 *   });
 * };
 * ```
 */

// import { useCallback, useEffect, useMemo } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
// import {
//   loginRequest,
//   loginSuccess,
//   loginFailure,
//   logoutRequest,
//   registerRequest,
//   registerSuccess,
//   registerFailure,
//   refreshTokenRequest,
//   refreshTokenSuccess,
//   refreshTokenFailure,
//   clearAuthError,
//   updateProfile,
//   type LoginCredentials,
//   type RegisterData,
//   type User
// } from '@/store/actions';
// import {
//   selectAuthState,
//   selectUser,
//   selectIsAuthenticated,
//   selectAuthLoading,
//   selectAuthError,
//   selectToken,
//   selectRefreshToken,
//   selectTokenExpiry,
//   selectIsTokenExpired,
//   selectUserPermissions,
//   selectUserRole,
//   type RootState
// } from '@/store/reducers';
// import { authApi } from '@/services/api';
// import { logger } from '@/utils';

// // ===== TYPES =====

// export interface AuthResult {
//   success: boolean;
//   error?: string;
//   user?: User;
// }

// export interface UseAuthReturn {
//   // State
//   user: User | null;
//   isAuthenticated: boolean;
//   isLoading: boolean;
//   error: string | null;
//   token: string | null;
//   refreshToken: string | null;
//   tokenExpiry: number | null;
//   isTokenExpired: boolean;
//   permissions: string[];
//   role: string | null;

//   // Actions
//   login: (credentials: LoginCredentials) => Promise<AuthResult>;
//   logout: () => Promise<void>;
//   register: (data: RegisterData) => Promise<AuthResult>;
//   refreshTokens: () => Promise<boolean>;
//   updateUserProfile: (updates: Partial<User>) => Promise<AuthResult>;
//   clearError: () => void;

//   // Utilities
//   hasPermission: (permission: string) => boolean;
//   hasRole: (role: string) => boolean;
//   hasAnyPermission: (permissions: string[]) => boolean;
//   hasAllPermissions: (permissions: string[]) => boolean;
//   canAccessRoute: (route: string) => boolean;
//   getAuthHeaders: () => Record<string, string>;
// }

// // ===== ROUTE PERMISSIONS =====

// const ROUTE_PERMISSIONS: Record<string, string[]> = {
//   '/admin': ['admin:access'],
//   '/admin/users': ['admin:users:read'],
//   '/admin/courses': ['admin:courses:read'],
//   '/admin/analytics': ['admin:analytics:read'],
//   '/courses/create': ['course:create'],
//   '/courses/:id/edit': ['course:edit'],
//   '/courses/:id/delete': ['course:delete'],
//   '/profile': ['user:profile:read'],
//   '/profile/edit': ['user:profile:edit'],
//   '/settings': ['user:settings:read']
// };

// // ===== MAIN HOOK =====

// /**
//  * Main authentication hook
//  * Provides comprehensive authentication functionality
//  */
// export const useAuth = (): UseAuthReturn => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();

//   // Selectors
//   const authState = useSelector(selectAuthState);
//   const user = useSelector(selectUser);
//   const isAuthenticated = useSelector(selectIsAuthenticated);
//   const isLoading = useSelector(selectAuthLoading);
//   const error = useSelector(selectAuthError);
//   const token = useSelector(selectToken);
//   const refreshToken = useSelector(selectRefreshToken);
//   const tokenExpiry = useSelector(selectTokenExpiry);
//   const isTokenExpired = useSelector(selectIsTokenExpired);
//   const permissions = useSelector(selectUserPermissions);
//   const role = useSelector(selectUserRole);

//   // ===== ACTIONS =====

//   /**
//    * Login with credentials
//    */
//   const login = useCallback(async (credentials: LoginCredentials): Promise<AuthResult> => {
//     try {
//       dispatch(loginRequest());

//       const response = await authApi.login(credentials);

//       if (response.success && response.data) {
//         dispatch(loginSuccess({
//           user: response.data.user,
//           token: response.data.token,
//           refreshToken: response.data.refreshToken,
//           expiresIn: response.data.expiresIn
//         }));

//         logger.info('User logged in successfully', {
//           userId: response.data.user.id,
//           email: response.data.user.email
//         });

//         return {
//           success: true,
//           user: response.data.user
//         };
//       } else {
//         const errorMessage = response.error || 'Login failed';
//         dispatch(loginFailure(errorMessage));

//         logger.warn('Login failed', {
//           email: credentials.email,
//           error: errorMessage
//         });

//         return {
//           success: false,
//           error: errorMessage
//         };
//       }
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : 'Login failed';
//       dispatch(loginFailure(errorMessage));

//       logger.error('Login error', {
//         email: credentials.email,
//         error: errorMessage
//       });

//       return {
//         success: false,
//         error: errorMessage
//       };
//     }
//   }, [dispatch]);

//   /**
//    * Logout user
//    */
//   const logout = useCallback(async (): Promise<void> => {
//     try {
//       dispatch(logoutRequest());

//       // Call logout API if token exists
//       if (token) {
//         await authApi.logout();
//       }

//       logger.info('User logged out successfully', {
//         userId: user?.id
//       });

//       // Navigate to login page
//       navigate('/login');
//     } catch (error) {
//       logger.error('Logout error', {
//         error: error instanceof Error ? error.message : 'Unknown error'
//       });

//       // Still perform logout even if API call fails
//       navigate('/login');
//     }
//   }, [dispatch, token, user?.id, navigate]);

//   /**
//    * Register new user
//    */
//   const register = useCallback(async (data: RegisterData): Promise<AuthResult> => {
//     try {
//       dispatch(registerRequest());

//       const response = await authApi.register(data);

//       if (response.success && response.data) {
//         dispatch(registerSuccess({
//           user: response.data.user,
//           token: response.data.token,
//           refreshToken: response.data.refreshToken,
//           expiresIn: response.data.expiresIn
//         }));

//         logger.info('User registered successfully', {
//           userId: response.data.user.id,
//           email: response.data.user.email
//         });

//         return {
//           success: true,
//           user: response.data.user
//         };
//       } else {
//         const errorMessage = response.error || 'Registration failed';
//         dispatch(registerFailure(errorMessage));

//         logger.warn('Registration failed', {
//           email: data.email,
//           error: errorMessage
//         });

//         return {
//           success: false,
//           error: errorMessage
//         };
//       }
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : 'Registration failed';
//       dispatch(registerFailure(errorMessage));

//       logger.error('Registration error', {
//         email: data.email,
//         error: errorMessage
//       });

//       return {
//         success: false,
//         error: errorMessage
//       };
//     }
//   }, [dispatch]);

//   /**
//    * Refresh authentication tokens
//    */
//   const refreshTokens = useCallback(async (): Promise<boolean> => {
//     try {
//       if (!refreshToken) {
//         logger.warn('No refresh token available');
//         return false;
//       }

//       dispatch(refreshTokenRequest());

//       const response = await authApi.refreshToken(refreshToken);

//       if (response.success && response.data) {
//         dispatch(refreshTokenSuccess({
//           token: response.data.token,
//           refreshToken: response.data.refreshToken,
//           expiresIn: response.data.expiresIn
//         }));

//         logger.info('Tokens refreshed successfully');
//         return true;
//       } else {
//         const errorMessage = response.error || 'Token refresh failed';
//         dispatch(refreshTokenFailure(errorMessage));

//         logger.warn('Token refresh failed', { error: errorMessage });
//         return false;
//       }
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : 'Token refresh failed';
//       dispatch(refreshTokenFailure(errorMessage));

//       logger.error('Token refresh error', { error: errorMessage });
//       return false;
//     }
//   }, [dispatch, ref=nullreshToken]);

//   /**
//    * Update user profile
//    */
//   const updateUserProfile = useCallback(async (updates: Partial<User>): Promise<AuthResult> => {
//     try {
//       if (!user) {
//         return {
//           success: false,
//           error: 'No user logged in'
//         };
//       }

//       const response = await authApi.updateProfile(updates);

//       if (response.success && response.data) {
//         dispatch(updateProfile(response.data));

//         logger.info('Profile updated successfully', {
//           userId: user.id,
//           updates: Object.keys(updates)
//         });

//         return {
//           success: true,
//           user: response.data
//         };
//       } else {
//         const errorMessage = response.error || 'Profile update failed';

//         logger.warn('Profile update failed', {
//           userId: user.id,
//           error: errorMessage
//         });

//         return {
//           success: false,
//           error: errorMessage
//         };
//       }
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : 'Profile update failed';

//       logger.error('Profile update error', {
//         userId: user?.id,
//         error: errorMessage
//       });

//       return {
//         success: false,
//         error: errorMessage
//       };
//     }
//   }, [dispatch, user]);

//   /**
//    * Clear authentication error
//    */
//   const clearError = useCallback((): void => {
//     dispatch(clearAuthError());
//   }, [dispatch]);

//   // ===== UTILITIES =====

//   /**
//    * Check if user has specific permission
//    */
//   const hasPermission = useCallback((permission: string): boolean => {
//     return permissions.includes(permission);
//   }, [permissions]);

//   /**
//    * Check if user has specific role
//    */
//   const hasRole = useCallback((targetRole: string): boolean => {
//     return role === targetRole;
//   }, [role]);

//   /**
//    * Check if user has any of the specified permissions
//    */
//   const hasAnyPermission = useCallback((targetPermissions: string[]): boolean => {
//     return targetPermissions.some(permission => permissions.includes(permission));
//   }, [permissions]);

//   /**
//    * Check if user has all of the specified permissions
//    */
//   const hasAllPermissions = useCallback((targetPermissions: string[]): boolean => {
//     return targetPermissions.every(permission => permissions.includes(permission));
//   }, [permissions]);

//   /**
//    * Check if user can access specific route
//    */
//   const canAccessRoute = useCallback((route: string): boolean => {
//     const requiredPermissions = ROUTE_PERMISSIONS[route];

//     if (!requiredPermissions || requiredPermissions.length === 0) {
//       return true; // No permissions required
//     }

//     return hasAnyPermission(requiredPermissions);
//   }, [hasAnyPermission]);

//   /**
//    * Get authentication headers for API requests
//    */
//   const getAuthHeaders = useCallback((): Record<string, string> => {
//     const headers: Record<string, string> = {};

//     if (token) {
//       headers.Authorization = `Bearer ${token}`;
//     }

//     return headers;
//   }, [token]);

//   // ===== EFFECTS =====

//   /**
//    * Auto-refresh tokens when they're about to expire
//    */
//   useEffect(() => {
//     if (!isAuthenticated || !tokenExpiry || !refreshToken) {
//       return;
//     }

//     const now = Date.now();
//     const timeUntilExpiry = tokenExpiry - now;
//     const refreshThreshold = 5 * 60 * 1000; // 5 minutes

//     if (timeUntilExpiry <= refreshThreshold && timeUntilExpiry > 0) {
//       // Token is about to expire, ref=nullresh it
//       refreshTokens();
//     } else if (timeUntilExpiry > refreshThreshold) {
//       // Set up timer to refresh token before it expires
//       const timeoutId = setTimeout(() => {
//         refreshTokens();
//       }, timeUntilExpiry - refreshThreshold);

//       return () => clearTimeout(timeoutId);
//     }
//   }, [isAuthenticated, tokenExpiry, ref=nullreshToken, ref=nullreshTokens]);

//   /**
//    * Logout user if token is expired and refresh fails
//    */
//   useEffect(() => {
//     if (isAuthenticated && isTokenExpired && !refreshToken) {
//       logger.warn('Token expired and no refresh token available, logging out');
//       logout();
//     }
//   }, [isAuthenticated, isTokenExpired, ref=nullreshToken, logout]);

//   // ===== MEMOIZED RETURN =====

//   return useMemo(() => ({
//     // State
//     user,
//     isAuthenticated,
//     isLoading,
//     error,
//     token,
//     refreshToken,
//     tokenExpiry,
//     isTokenExpired,
//     permissions,
//     role,

//     // Actions
//     login,
//     logout,
//     register,
//     refreshTokens,
//     updateUserProfile,
//     clearError,

//     // Utilities
//     hasPermission,
//     hasRole,
//     hasAnyPermission,
//     hasAllPermissions,
//     canAccessRoute,
//     getAuthHeaders
//   }), [
//     user,
//     isAuthenticated,
//     isLoading,
//     error,
//     token,
//     refreshToken,
//     tokenExpiry,
//     isTokenExpired,
//     permissions,
//     role,
//     login,
//     logout,
//     register,
//     refreshTokens,
//     updateUserProfile,
//     clearError,
//     hasPermission,
//     hasRole,
//     hasAnyPermission,
//     hasAllPermissions,
//     canAccessRoute,
//     getAuthHeaders
//   ]);
// };

// // ===== ADDITIONAL HOOKS =====

// /**
//  * Hook for checking authentication status
//  */
// export const useAuthStatus = () => {
//   const isAuthenticated = useSelector(selectIsAuthenticated);
//   const isLoading = useSelector(selectAuthLoading);
//   const user = useSelector(selectUser);

//   return useMemo(() => ({
//     isAuthenticated,
//     isLoading,
//     isGuest: !isAuthenticated && !isLoading,
//     hasUser: !!user
//   }), [isAuthenticated, isLoading, user]);
// };

// /**
//  * Hook for permission checking
//  */
// export const usePermissions = () => {
//   const permissions = useSelector(selectUserPermissions);
//   const role = useSelector(selectUserRole);

//   const hasPermission = useCallback((permission: string): boolean => {
//     return permissions.includes(permission);
//   }, [permissions]);

//   const hasRole = useCallback((targetRole: string): boolean => {
//     return role === targetRole;
//   }, [role]);

//   const hasAnyPermission = useCallback((targetPermissions: string[]): boolean => {
//     return targetPermissions.some(permission => permissions.includes(permission));
//   }, [permissions]);

//   const hasAllPermissions = useCallback((targetPermissions: string[]): boolean => {
//     return targetPermissions.every(permission => permissions.includes(permission));
//   }, [permissions]);

//   return useMemo(() => ({
//     permissions,
//     role,
//     hasPermission,
//     hasRole,
//     hasAnyPermission,
//     hasAllPermissions
//   }), [permissions, role, hasPermission, hasRole, hasAnyPermission, hasAllPermissions]);
// };

// /**
//  * Hook for user profile management
//  */
// export const useUserProfile = () => {
//   const user = useSelector(selectUser);
//   const { updateUserProfile } = useAuth();

//   const updateProfile = useCallback(async (updates: Partial<User>) => {
//     return await updateUserProfile(updates);
//   }, [updateUserProfile]);

//   return useMemo(() => ({
//     user,
//     updateProfile,
//     hasProfile: !!user,
//     isProfileComplete: user ? (
//       !!user.name &&
//       !!user.email &&
//       !!user.avatar
//     ) : false
//   }), [user, updateProfile]);
// };

/**
 * Development Notes:
 *
 * 1. Authentication Flow:
 *    - Login/register with credentials
 *    - Store tokens and user data
 *    - Auto-refresh tokens before expiry
 *    - Handle token expiration gracefully
 *    - Logout and cleanup on errors
 *
 * 2. Permission System:
 *    - Role-based access control
 *    - Fine-grained permissions
 *    - Route-level protection
 *    - Component-level guards
 *
 * 3. State Management:
 *    - Redux integration
 *    - Optimistic updates
 *    - Error handling
 *    - Loading states
 *
 * 4. Security:
 *    - Token-based authentication
 *    - Automatic token refresh
 *    - Secure storage
 *    - CSRF protection
 *
 * 5. Performance:
 *    - Memoized selectors
 *    - Optimized re-renders
 *    - Efficient state updates
 *    - Minimal API calls
 */
