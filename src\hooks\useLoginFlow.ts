import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useRouter } from "next/navigation";
import { getVisitorId, checkPrivateMode } from "@mfa-client/frontend";
import {
  useLoginUserMutation,
  useUpdatePasswordMutation,
  useVerifyUserMutation,
} from "@/services/mutation/login/user";
import {
  useCheckMfaRequirementMutation,
  useEnrollMfaMutation,
  useSendOtpMutation,
  useVerifyMfaMutation,
  useVerifyMfaSetupMutation,
  useVerifyOtpMutation,
} from "@/services/mutation/login/mfa";
import { IEnrollMfaResponse } from "@/interfaces/user/mfa";
import { encryptAES } from "@/utils/common/encryption";
import {
  notifyHotError,
  notifyHotSuccess,
} from "@/components/molecules/toast/hot-toast";
import { useSession } from "./useSession";

export type { IEnrollMfaResponse };

export type Step1Values = { email: string };
export type Step2Values = { password: string };
export type Step3Values = { otp: string };
export type Step4Values = { otpMfa: string };
export type Step5Values = { newPassword: string; confirmPassword: string };

export type SelectedUser = {
  user_id: number;
  name: string;
  email: string;
  phone: string;
  password: string;
  status: string;
  is_new_user: boolean;
  key: string;
};

const schemaStep1 = yup.object({
  email: yup.string().required("Cannot find your NPK, Email, or Phone Number"),
});

const schemaStep2 = yup.object({
  password: yup
    .string()
    .min(6, "Password minimal 6 karakter")
    .required("Password wajib"),
});

const schemaStep3 = yup.object({
  otp: yup
    .string()
    .matches(/^\d{6}$/, "You have entered an invalid OTP. Please try again!")
    .required("You have entered an invalid OTP. Please try again!"),
});

const schemaStep4 = yup.object({
  otpMfa: yup
    .string()
    .matches(/^\d{6}$/, "You have entered an invalid OTP. Please try again!")
    .required("You have entered an invalid OTP. Please try again!"),
});

const schemaStep5 = yup.object({
  newPassword: yup
    .string()
    .min(
      8,
      "Password must be at least 8 character and contains uppercase, lowercase letters, and numbers."
    )
    .required(
      "Password must be at least 8 character and contains uppercase, lowercase letters, and numbers."
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("newPassword")], "Passwords do not match")
    .required("Passwords do not match"),
});

export const useLoginFlow = () => {
  const router = useRouter();
  const { session } = useSession();

  // Forms
  const step1Form = useForm<Step1Values>({
    resolver: yupResolver(schemaStep1),
  });
  const step2Form = useForm<Step2Values>({
    resolver: yupResolver(schemaStep2),
  });
  const step3Form = useForm<Step3Values>({
    resolver: yupResolver(schemaStep3),
  });
  const step4Form = useForm<Step4Values>({
    resolver: yupResolver(schemaStep4),
  });
  const step5Form = useForm<Step5Values>({
    resolver: yupResolver(schemaStep5),
  });

  // State
  const [step, setStep] = useState<1 | 2 | 3 | 4 | 5>(1);
  const [otpDialog, setOtpDialog] = useState<{
    open: boolean;
    type: "login" | "forgot_password";
  }>({
    open: false,
    type: "login",
  });
  const [instructionDialogOpen, setInstructionDialogOpen] = useState(false);
  const [otpMethod, setOtpMethod] = useState<"phone" | "email" | null>(null);
  const [resendTimer, setResendTimer] = useState<number>(90);
  const [selectedUser, setSelectedUser] = useState<null | SelectedUser>(null);
  const [mfaData, setMfaData] = useState<null | IEnrollMfaResponse>(null);
  const [isMfaOtp, setIsMfaOtp] = useState(false);

  // Mutations
  const { mutateAsync: verifyUser, isPending: isVerifyUserPending } =
    useVerifyUserMutation();
  const { mutateAsync: loginUser, isPending: isLoginUserPending } =
    useLoginUserMutation();
  const { mutateAsync: sendOtp, isPending: isSendOtpPending } =
    useSendOtpMutation();
  const { mutateAsync: verifyOtp, isPending: isVerifyOtpPending } =
    useVerifyOtpMutation();
  const { mutateAsync: updatePassword, isPending: isUpdatePasswordPending } =
    useUpdatePasswordMutation();
  const {
    mutateAsync: checkMfaRequirement,
    isPending: isCheckMfaRequirementPending,
  } = useCheckMfaRequirementMutation();
  const { mutateAsync: enrollMfa, isPending: isEnrollMfaPending } =
    useEnrollMfaMutation();
  const { mutateAsync: verifyMfaSetup, isPending: isVerifyMfaSetupPending } =
    useVerifyMfaSetupMutation();
  const { mutateAsync: verifyMfa, isPending: isVerifyMfaPending } =
    useVerifyMfaMutation();

  // const issuerName = "ACC LEMON";

  // Timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (step === 3 && resendTimer > 0) {
      timer = setInterval(() => setResendTimer((prev) => prev - 1), 1000);
    }
    return () => clearInterval(timer);
  }, [step, resendTimer]);

  // Computed values
  // const keyUri = useMemo(() => {
  //   if (!mfaData?.setupKey || !selectedUser?.email) return "";
  //   return authenticator.keyuri(
  //     selectedUser.email,
  //     issuerName,
  //     mfaData.setupKey
  //   );
  // }, [mfaData?.setupKey, selectedUser?.email]);

  const formatTimer = (seconds: number) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  const resetForms = () => {
    step2Form.reset({ password: "" });
    step2Form.clearErrors();
    step3Form.reset({ otp: "" });
    step3Form.clearErrors();
    step4Form.reset({ otpMfa: "" });
    step4Form.clearErrors();
  };

  return {
    // Forms
    step1Form,
    step2Form,
    step3Form,
    step4Form,
    step5Form,

    // State
    step,
    setStep,
    otpDialog,
    setOtpDialog,
    instructionDialogOpen,
    setInstructionDialogOpen,
    otpMethod,
    setOtpMethod,
    resendTimer,
    setResendTimer,
    selectedUser,
    setSelectedUser,
    mfaData,
    setMfaData,
    isMfaOtp,
    setIsMfaOtp,

    // Loading states
    isVerifyUserPending,
    isLoginUserPending,
    isSendOtpPending,
    isVerifyOtpPending,
    isUpdatePasswordPending,
    isCheckMfaRequirementPending,
    isEnrollMfaPending,
    isVerifyMfaSetupPending,
    isVerifyMfaPending,

    // Mutations
    verifyUser,
    loginUser,
    sendOtp,
    verifyOtp,
    updatePassword,
    checkMfaRequirement,
    enrollMfa,
    verifyMfaSetup,
    verifyMfa,

    // Computed
    // keyUri,
    formatTimer,
    resetForms,

    // Utils
    router,
    notifyHotError,
    notifyHotSuccess,
    encryptAES,
    getVisitorId,
    checkPrivateMode,

    // Session
    session,
  };
};
