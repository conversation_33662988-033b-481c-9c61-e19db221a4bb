import { useQuery } from "@tanstack/react-query";

export function useSession() {
  const { data, isPending } = useQuery({
    queryKey: ["session"],
    queryFn: async () => {
      const res = await fetch("/api/session");
      return res.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  return {
    authenticated: !!data?.accessToken,
    loading: isPending,
    session: data,
  };
}
