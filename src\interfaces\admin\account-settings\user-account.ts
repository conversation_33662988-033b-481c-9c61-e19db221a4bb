import * as yup from 'yup';

// interface get user account detail response
export const getUserAccountFormSchema = yup.object({
  name: yup.string().required(),
  npk: yup.string().required(),
  job_name: yup.string().required(),
  entity_name: yup.string().required(),
  email: yup.string().required(),
  second_email: yup
    .string()
    .email('Please enter a valid email address')
    .required('Secondary email is required'),
  phone_number: yup.string().required(),
});

export interface IGetUserAccountForm
  extends yup.InferType<typeof getUserAccountFormSchema> {}

export interface IGetUserAccountDetailResponse {
  user_id: number;
  name: string | null;
  avatar: string | null;
  job_name: string | null;
  npk: string | null;
  entity_name: string | null;
  email: string | null;
  second_email: string | null;
  phone_number: string | null;
}

// form update user account schema
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const fileSizeValidator = (value: any, options: any) => {
  if (value && !(value instanceof File)) {
    return true;
  }
  if (value && value.size > MAX_FILE_SIZE) {
    return options.createError({
      message: `Ukuran file maksimal ${MAX_FILE_SIZE / 1024 / 1024}MB`,
    });
  }
  return true;
};

export const updateUserAccountFormSchema = yup.object({
  second_email: yup.string().email('Masukkan email yang valid'),
  avatar: yup
    .mixed<File>()
    .test('fileSize', 'Ukuran file terlalu besar', fileSizeValidator),
});

export interface IUpdateUserAccountForm
  extends yup.InferType<typeof updateUserAccountFormSchema> {}

export interface IUpdateUserAccountBody {
  second_email?: string;
  avatar?: File;
}

//interface verify password
export interface IVerifyPasswordBody {
  old_password: string;
}

//form change password schema
export const changePasswordFormSchema = yup.object({
  old_password: yup.string().required('Password saat ini wajib diisi'),
  new_password: yup
    .string()
    .min(8, 'Password baru minimal 8 karakter')
    .required('Password baru wajib diisi')
    .test(
      'not-same-as-old',
      'Password baru tidak boleh sama dengan password saat ini',
      function (value) {
        const { old_password } = this.parent as { old_password?: string };
        if (!value) return true;
        return value !== old_password;
      }
    ),

  new_password_confirm: yup
    .string()
    .required('Konfirmasi password wajib diisi')
    .oneOf([yup.ref('new_password')], 'Password tidak cocok')
    .test(
      'confirm-not-same-as-old',
      'Konfirmasi password tidak boleh sama dengan password saat ini',
      function (value) {
        const { old_password } = this.parent as { old_password?: string };
        if (!value) return true;
        return value !== old_password;
      }
    ),
});

export interface IChangePasswordForm
  extends yup.InferType<typeof changePasswordFormSchema> {}

export interface IChangePasswordBody {
  old_password: string;
  new_password: string;
  new_password_confirm: string;
}
