export interface ICategory {
  id: number;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

export interface ISubCategory {
  id: number;
  subcategory_name: string | null;
  category_id: number | null;
  category_name: string | null;
  created_at: string | null;
  created_by: string | null;
  last_updated: string | null;
  updated_by: string | null;
}

export interface IGetListCategoryQuery {
  page: number;
  limit?: number;
  search?: string;
  search_by?: "category_id" | "category_name" | "created_by" | "created_at";
}

export interface IGetListSubCategoryQuery {
  page: number;
  search?: string;
  search_by?:
    | "sub_category_id"
    | "sub_category_name"
    | "category_name"
    | "created_by"
    | "created_at";
}
