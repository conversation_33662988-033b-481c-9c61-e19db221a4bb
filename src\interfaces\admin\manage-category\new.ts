/* eslint-disable @typescript-eslint/no-empty-object-type */
import * as yup from "yup";

export const createCategoryBodySchema = yup.object({
  id: yup.string().optional(),
  name: yup.string().required(),
});

export const createSubCategoryBodySchema = yup.object({
  id: yup.string().optional(),
  name: yup.string().required(),
  category_id: yup.string().optional(),
});

export interface ICreateCategoryBody
  extends yup.InferType<typeof createCategoryBodySchema> {}
export interface ICreateSubCategoryBody
  extends yup.InferType<typeof createSubCategoryBodySchema> {}

export interface ICreateCategoryPayload {
  category_name: string;
}

export interface ICreateSubCategoryPayload {
  subcategory_name: string;
  category_id?: number;
}

export interface IUpdateCategory extends ICreateCategoryPayload {
  is_deleted?: boolean;
}

export interface IUpdateSubCategory extends ICreateSubCategoryPayload {
  is_deleted?: boolean;
}

export interface IUpdateCategoryPayload {
  id: number;
  body?: IUpdateCategory;
}

export interface IUpdateSubCategoryPayload {
  id: number;
  body: IUpdateSubCategory;
}
