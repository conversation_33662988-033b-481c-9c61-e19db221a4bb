export interface IFaq {
  id: string;
  question: string;
  answer: string;
}

export interface IGetListFaqParams {
  page: number;
  limit: number;
  search?: string;
  search_by?: string;
}

export interface IGetListFaqResponse {
  id: string;
  question: string;
  answer: string;
}

export interface IGetListTagParams {
  search?: string;
}

export interface IGetListTagResponse {
  tag_id: string;
  tag_name: string;
}

export interface IGetDetailFaqResponse {
  id: string;
  question: string;
  answer: string;
  imgfaq: string;
  tag: {
    tag_id: string;
    tag_name: string;
  }[];
}
