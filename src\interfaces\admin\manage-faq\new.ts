import * as yup from "yup";

export const createFaqBodySchema = yup.object({
  question: yup.string().required(),
  answer: yup.string().required(),
  image: yup.mixed().required(),
  tag_id: yup
    .array()
    .of(
      yup.object({
        value: yup.string().required(),
        label: yup.string().required(),
      })
    )
    .required(),
});
export interface ICreateFaqBody
  extends yup.InferType<typeof createFaqBodySchema> {}

export interface ICreateFaqPayload {
  question: string;
  answer: string;
  tags: string[];
  imgfaq: string;
}
