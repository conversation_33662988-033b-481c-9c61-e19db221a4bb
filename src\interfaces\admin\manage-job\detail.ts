export interface IGetJobPositionDetailResponse {
  id: number;
  job_id: string | null;
  job_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  department_id: string | null;
  department_name: string | null;
  job_function_id: number | null;
  job_function: string | null;
  level: number | null;
  level_id: number | null;
  job_position_type: string | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  starter_module_priority: string | null;
  last_updated: string | null;
  updated_by: string | null;
  is_active: boolean | null;
}

export interface IGetJobPositionDetailParams {
  id: number | null;
}
