export interface IGetJobPositionListResponse {
  id: number;
  job_id: string | null;
  job_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  department_name: string | null;
  job_function: string | null;
  job_position_type: string | null;
  level: number | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  starter_module_priority: string | null;
  last_updated: string | null;
  updated_by: string | null;
  is_active: boolean | null;
}

export interface IGetJobPositionListQuery {
  search?: string;
  search_by?: "job_name" | "job_function";
  job_position_type?: string;
  level?: number;
  is_neop?: boolean;
  starter_modul_priority?: string;
  page?: number;
  limit?: number;
}
