import * as yup from "yup";

export const createJobPositionFormSchema = yup.object({
  id: yup.string().required(),
  name: yup.string().required(),
  position_type: yup.string().required(),
  department_id: yup.number().optional(),
  job_function_id: yup.number().optional(),
  status: yup.boolean().required(),
  starting_level_id: yup.number().required(),
  entity_id: yup.number().required(),
  is_neop: yup.boolean().required(),
  welcoming_kit: yup.boolean().required(),
  starter_module_priority: yup.string().required(),
});

export interface ICreateJobPositionForm
  extends yup.InferType<typeof createJobPositionFormSchema> {}

export interface ICreateJobPositionBody {
  job_id: string;
  job_name?: string;
  job_position_type?: string;
  department_id?: string;
  department_name?: string;
  job_function_id?: number;
  job_function?: string;
  is_active?: boolean;
  level?: number;
  level_id?: number;
  level_name?: string;
  ho_dept?: string;
  entity_id?: number;
  is_need_neop?: boolean;
  is_need_welcoming_kit?: boolean;
  starter_module_priority?: string;
}
