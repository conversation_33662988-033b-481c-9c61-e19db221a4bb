export interface IUpdateJobPositionParams {
  id: number;
}

export interface IUpdateJobPositionBody {
  job_name?: string;
  job_position_type?: string;
  department_id?: string;
  department_name?: string;
  job_function_id?: number;
  job_function?: string;
  is_active?: boolean;
  level?: number;
  level_id?: number;
  level_name?: string;
  ho_dept?: string;
  entity_id?: number;
  is_need_neop?: boolean;
  is_need_welcoming_kit?: boolean;
  starter_module_priority?: string;
  is_deleted?: boolean;
}
