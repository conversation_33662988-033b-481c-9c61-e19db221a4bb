export interface IGetLearningCodeResponse {
  learning_id: number;
  learning_code: string;
  learning_code_name: string;
  related_job_position: string[];
  assosiated_module: string[];
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  is_active: boolean;
}

export interface IGetLearningLevelResponse {
  level_id: number;
  level: number;
  level_name: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  is_active: boolean;
}
