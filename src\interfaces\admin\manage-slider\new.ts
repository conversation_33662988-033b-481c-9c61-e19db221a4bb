import * as yup from "yup";
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const fileSizeValidator = (value: any, options: any) => {
  if (value && !(value instanceof File)) {
    return true;
  }
  if (value && value.size > MAX_FILE_SIZE) {
    return options.createError({
      message: `Ukuran file maksimal ${MAX_FILE_SIZE / 1024 / 1024}MB`,
    });
  }
  return true;
};

export const createSliderFormSchema = yup.object({
  slider_name: yup.string().required("Nama slider wajib diisi"),
  slider_desktop: yup
    .mixed<File>()
    .test("required", "Wajib unggah file desktop", (v) => v instanceof File) // Check if a file is present
    .test("fileSize", "Ukuran file terlalu besar", fileSizeValidator), // Validate file size
  slider_mobile: yup
    .mixed<File>()
    .test("required", "Wajib unggah file mobile", (v) => v instanceof File) // Check if a file is present
    .test("fileSize", "Ukuran file terlalu besar", fileSizeValidator), // Validate file size
  link: yup.string().required("Link wajib diisi"),
});

export interface ICreateSliderForm
  extends yup.InferType<typeof createSliderFormSchema> {}

export interface ICreateSliderBody {
  slider_name: string;
  slider_desktop: File;
  slider_mobile: File;
  link: string;
}
