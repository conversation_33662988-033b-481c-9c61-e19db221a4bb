/* eslint-disable @typescript-eslint/no-empty-object-type */
import * as yup from "yup";

export const createImageRepositoryBodySchema = yup.object({
  id: yup.string().optional(),
  name: yup.string().required(),
  level: yup.string().required(),
  image: yup.mixed().required(),
  category: yup
    .array(
      yup.object({
        label: yup.string(),
        value: yup.string(),
      })
    )
    .required()
    .min(1, "Category is required"),
});

export interface ICreateImageRepositoryBody
  extends yup.InferType<typeof createImageRepositoryBodySchema> {}
