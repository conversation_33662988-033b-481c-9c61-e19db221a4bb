/* eslint-disable @typescript-eslint/no-empty-object-type */
import * as yup from "yup";

export const createQuestionBankBodySchema = yup.object({
  id: yup.string().optional(),
  howToAddQuestionImage: yup.string().required(),
  imageFromRepository: yup.string().required(),
  image: yup.mixed().required(),
  category: yup
    .array(
      yup.object({
        label: yup.string(),
        value: yup.string(),
      })
    )
    .required()
    .min(1, "Category is required"),
  level: yup.string().required(),
  questionType: yup.string().required(),
  question: yup.string().required(),
  option: yup.array(yup.string()).optional(),
  keyAnswer: yup.string().required(),
});

export interface ICreateQuestionBankBody
  extends yup.InferType<typeof createQuestionBankBodySchema> {}
