export interface IQuestionBank {
  questionId: number;
  category: string;
  level: string;
  question_type: string;
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  key_answer: string;
  with_image: boolean;
  associated_section: string;
  correct_answer_percentage: string;
  created_at: string;
  created_by: string;
  last_updated: string;
  updated_by: string;
}
