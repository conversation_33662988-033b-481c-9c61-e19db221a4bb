/* eslint-disable @typescript-eslint/no-empty-object-type */
import * as yup from "yup";

export const createTemplateQuestionBodySchema = yup.object({
  id: yup.string().optional(),
  name: yup.string().required(),
  question: yup.array(yup.object({})).required().min(1, "Question is required"),
});

export interface ICreateTemplateQuestionBody
  extends yup.InferType<typeof createTemplateQuestionBodySchema> {}
