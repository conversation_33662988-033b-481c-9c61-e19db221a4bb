export interface IUserManagement {
  id: number;
  npk: string;
  name: string;
  job_position: string;
  primary_email: string;
  secondary_email: string;
  phone: string;
  user_type: string;
  user_role: string;
  entity: string;
  neop: boolean;
  welcoming_kit: boolean;
  last_updated: string;
  updated_by: string;
  status: boolean;
}

export interface IProgressHistory {
  filename: string;
  status: string;
  success: number | null;
  failed: number | null;
  uploaded_at: string;
  uploaded_by: string;
  failed_log: string | null;
}

export interface IGetUserListResponse {
  id: number | null;
  npk: string | null;
  name: string | null;
  job_name: string | null;
  email: string | null;
  second_email: string | null;
  phone_number: string | null;
  user_type_id: number | null;
  user_type_name: string | null;
  user_role_id: number | null;
  user_role_name: string | null;
  entity_id: number | null;
  entity_name: string | null;
  is_need_neop: boolean | null;
  is_need_welcoming_kit: boolean | null;
  updated_at: Date | null;
  updated_by: string | null;
  is_active: boolean | null;
  is_new_user: boolean | null;
}

export interface IGetUserListQuery {
  search?: string;
  search_by?:
    | "npk"
    | "name"
    | "job_name"
    | "email"
    | "second_email"
    | "phone_number"
    | "updated_by";
  type_id?: number;
  role_id?: number;
  entity_id?: number;
  is_neop?: boolean;
  is_new_user?: boolean;
  page?: number;
  limit?: number;
}
