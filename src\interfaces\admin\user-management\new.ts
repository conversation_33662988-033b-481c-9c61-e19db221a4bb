/* eslint-disable @typescript-eslint/no-empty-object-type */
import * as yup from "yup";

export const createUserFormSchema = yup.object({
  npk: yup.string().required(),
  name: yup.string().required(),
  job_name_id: yup.number().required(),
  email: yup.string().email().required(),
  second_email: yup.string().email().optional(),
  phone_number: yup.string().required(),
  user_type_id: yup.number().required(),
  supervisor_id: yup.number().optional(),
  user_role_id: yup.number().required(),
  forum_title_id: yup.number().required(),
  is_need_neop: yup.boolean().required(),
  entity_id: yup.number().required(),
  is_active: yup.boolean().required(),
  is_need_welcoming_kit: yup.boolean().required(),
  user_signature: yup.mixed().optional(),
});

export interface ICreateUserForm
  extends yup.InferType<typeof createUserFormSchema> {}

export interface ICreateUserBody {
  npk: string;
  email: string;
  second_email?: string;
  point?: number | null;
  forum_title: string;
  name: string;
  job_name: string;
  password: string;
  job_name_id: number;
  phone_number: string;
  user_type_id: number;
  user_role_id: number;
  supervisor_id?: number;
  supervisor_name?: string;
  supervisor_npk?: string;
  is_active: boolean;
  is_need_neop: boolean;
  is_new_user: boolean;
  is_deleted: boolean;
  created_by: string;
  updated_by: string;
  forum_title_id: number;
  entity_id: number;
  is_need_welcoming_kit: boolean;
}
