export interface IUpdateUserBody {
  npk?: string;
  email?: string;
  second_email?: string;
  point?: number | null;
  forum_title?: string;
  name?: string;
  job_name?: string;
  password?: string;
  job_name_id?: number;
  phone_number?: string;
  user_type_id?: number;
  user_role_id?: number;
  supervisor_id?: number;
  supervisor_name?: string;
  supervisor_npk?: string;
  is_active?: boolean;
  is_need_neop?: boolean;
  is_new_user?: boolean;
  is_deleted?: boolean;
  created_by?: string;
  updated_by?: string;
  forum_title_id?: number;
  entity_id?: number;
  is_need_welcoming_kit?: boolean;
}
