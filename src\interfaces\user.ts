/**
 * User Interface Definitions
 * 
 * This file contains TypeScript interface definitions for user-related data structures.
 * These interfaces provide type safety and consistency across the application for
 * user management, authentication, profiles, preferences, and related functionality.
 * 
 * Key Concepts:
 * - Shared type definitions for user domain
 * - Type safety for user-related operations
 * - Consistent data structures across components
 * - Support for different user roles and permissions
 * - Profile management and preferences
 * - Authentication and authorization
 * - User activity and analytics
 * 
 * Usage Examples:
 * ```tsx
 * // Import user types
 * import type { User, UserProfile, UserPreferences } from '@/interfaces/user';
 * 
 * // Use in component props
 * interface UserProfileProps {
 *   user: User;
 *   onUpdate: (profile: UserProfile) => void;
 * }
 * 
 * // Use in API responses
 * const fetchUser = async (id: string): Promise<User> => {
 *   // API implementation
 * };
 * 
 * // Use in state management
 * interface AuthState {
 *   currentUser: User | null;
 *   isAuthenticated: boolean;
 *   permissions: UserPermission[];
 * }
 * ```
 */

// ===== BASE TYPES =====

/**
 * User role types
 */
export type UserRole = 'student' | 'instructor' | 'admin' | 'moderator' | 'support';

/**
 * User account status
 */
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending' | 'banned';

/**
 * User subscription status
 */
export type SubscriptionStatus = 'free' | 'premium' | 'pro' | 'enterprise' | 'trial' | 'expired' | 'cancelled';

/**
 * Authentication provider types
 */
export type AuthProvider = 'email' | 'google' | 'facebook' | 'github' | 'linkedin' | 'apple';

/**
 * User verification status
 */
export type VerificationStatus = 'unverified' | 'pending' | 'verified' | 'rejected';

/**
 * Notification types
 */
export type NotificationType = 
  | 'course_update'
  | 'new_lesson'
  | 'assignment_due'
  | 'grade_posted'
  | 'message_received'
  | 'achievement_earned'
  | 'payment_reminder'
  | 'system_announcement'
  | 'marketing';

/**
 * Privacy levels
 */
export type PrivacyLevel = 'public' | 'friends' | 'private';

/**
 * User activity types
 */
export type ActivityType = 
  | 'login'
  | 'logout'
  | 'course_enrolled'
  | 'lesson_completed'
  | 'quiz_taken'
  | 'assignment_submitted'
  | 'certificate_earned'
  | 'profile_updated'
  | 'password_changed'
  | 'payment_made';

// ===== CORE USER INTERFACES =====

/**
 * Main user interface
 */
export interface User {
  /** User unique identifier */
  id: string;
  
  /** User email address */
  email: string;
  
  /** User first name */
  firstName: string;
  
  /** User last name */
  lastName: string;
  
  /** User full name (computed) */
  fullName: string;
  
  /** User display name/username */
  username?: string;
  
  /** User profile picture URL */
  avatar?: string;
  
  /** User role */
  role: UserRole;
  
  /** User account status */
  status: UserStatus;
  
  /** User subscription status */
  subscriptionStatus: SubscriptionStatus;
  
  /** Whether user email is verified */
  isEmailVerified: boolean;
  
  /** Whether user profile is verified */
  isProfileVerified: boolean;
  
  /** User verification status */
  verificationStatus: VerificationStatus;
  
  /** User timezone */
  timezone: string;
  
  /** User preferred language */
  language: string;
  
  /** User country/region */
  country?: string;
  
  /** User city */
  city?: string;
  
  /** User phone number */
  phone?: string;
  
  /** User date of birth */
  dateOfBirth?: Date;
  
  /** User bio/description */
  bio?: string;
  
  /** User website URL */
  website?: string;
  
  /** User social media links */
  socialLinks?: UserSocialLinks;
  
  /** User preferences */
  preferences: UserPreferences;
  
  /** User permissions */
  permissions: UserPermission[];
  
  /** User metadata */
  metadata?: Record<string, any>;
  
  /** User registration date */
  createdAt: Date;
  
  /** User last update date */
  updatedAt: Date;
  
  /** User last login date */
  lastLoginAt?: Date;
  
  /** User last activity date */
  lastActiveAt?: Date;
}

/**
 * User profile interface (public information)
 */
export interface UserProfile {
  /** User unique identifier */
  id: string;
  
  /** User full name */
  fullName: string;
  
  /** User display name */
  username?: string;
  
  /** User profile picture URL */
  avatar?: string;
  
  /** User bio/description */
  bio?: string;
  
  /** User title/position */
  title?: string;
  
  /** User company/organization */
  company?: string;
  
  /** User website URL */
  website?: string;
  
  /** User location */
  location?: string;
  
  /** User social media links */
  socialLinks?: UserSocialLinks;
  
  /** User skills/expertise */
  skills: string[];
  
  /** User interests */
  interests: string[];
  
  /** User achievements */
  achievements: UserAchievement[];
  
  /** User statistics */
  stats: UserStats;
  
  /** Whether profile is public */
  isPublic: boolean;
  
  /** User join date */
  joinedAt: Date;
}

/**
 * User social media links
 */
export interface UserSocialLinks {
  /** Twitter profile URL */
  twitter?: string;
  
  /** LinkedIn profile URL */
  linkedin?: string;
  
  /** GitHub profile URL */
  github?: string;
  
  /** YouTube channel URL */
  youtube?: string;
  
  /** Instagram profile URL */
  instagram?: string;
  
  /** Facebook profile URL */
  facebook?: string;
  
  /** Personal website URL */
  website?: string;
}

/**
 * User preferences interface
 */
export interface UserPreferences {
  /** Theme preference */
  theme: 'light' | 'dark' | 'system';
  
  /** Language preference */
  language: string;
  
  /** Timezone preference */
  timezone: string;
  
  /** Date format preference */
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  
  /** Time format preference */
  timeFormat: '12h' | '24h';
  
  /** Currency preference */
  currency: string;
  
  /** Notification preferences */
  notifications: NotificationPreferences;
  
  /** Privacy preferences */
  privacy: PrivacyPreferences;
  
  /** Learning preferences */
  learning: LearningPreferences;
  
  /** Email preferences */
  email: EmailPreferences;
  
  /** Accessibility preferences */
  accessibility: AccessibilityPreferences;
}

/**
 * Notification preferences
 */
export interface NotificationPreferences {
  /** Enable push notifications */
  push: boolean;
  
  /** Enable email notifications */
  email: boolean;
  
  /** Enable SMS notifications */
  sms: boolean;
  
  /** Enable in-app notifications */
  inApp: boolean;
  
  /** Notification types to receive */
  types: Record<NotificationType, boolean>;
  
  /** Quiet hours */
  quietHours?: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
  
  /** Digest frequency */
  digestFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly' | 'never';
}

/**
 * Privacy preferences
 */
export interface PrivacyPreferences {
  /** Profile visibility */
  profileVisibility: PrivacyLevel;
  
  /** Course progress visibility */
  progressVisibility: PrivacyLevel;
  
  /** Achievement visibility */
  achievementVisibility: PrivacyLevel;
  
  /** Activity visibility */
  activityVisibility: PrivacyLevel;
  
  /** Allow search engines to index profile */
  allowSearchEngineIndexing: boolean;
  
  /** Allow others to find by email */
  allowFindByEmail: boolean;
  
  /** Allow others to find by phone */
  allowFindByPhone: boolean;
  
  /** Show online status */
  showOnlineStatus: boolean;
  
  /** Show last seen */
  showLastSeen: boolean;
}

/**
 * Learning preferences
 */
export interface LearningPreferences {
  /** Preferred learning pace */
  pace: 'slow' | 'normal' | 'fast';
  
  /** Preferred content types */
  contentTypes: ('video' | 'text' | 'audio' | 'interactive')[];
  
  /** Auto-play videos */
  autoPlayVideos: boolean;
  
  /** Video playback speed */
  videoSpeed: number;
  
  /** Video quality preference */
  videoQuality: 'auto' | '720p' | '1080p' | '4k';
  
  /** Enable captions by default */
  enableCaptions: boolean;
  
  /** Caption language */
  captionLanguage: string;
  
  /** Study reminders */
  studyReminders: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'custom';
    time: string;
    days: number[];
  };
  
  /** Goal setting */
  goals: {
    dailyStudyTime: number;
    weeklyStudyTime: number;
    coursesPerMonth: number;
  };
}

/**
 * Email preferences
 */
export interface EmailPreferences {
  /** Marketing emails */
  marketing: boolean;
  
  /** Course updates */
  courseUpdates: boolean;
  
  /** New course recommendations */
  recommendations: boolean;
  
  /** Weekly digest */
  weeklyDigest: boolean;
  
  /** Achievement notifications */
  achievements: boolean;
  
  /** Payment receipts */
  receipts: boolean;
  
  /** Security alerts */
  security: boolean;
}

/**
 * Accessibility preferences
 */
export interface AccessibilityPreferences {
  /** High contrast mode */
  highContrast: boolean;
  
  /** Large text */
  largeText: boolean;
  
  /** Reduce motion */
  reduceMotion: boolean;
  
  /** Screen reader support */
  screenReader: boolean;
  
  /** Keyboard navigation */
  keyboardNavigation: boolean;
  
  /** Focus indicators */
  focusIndicators: boolean;
}

// ===== AUTHENTICATION INTERFACES =====

/**
 * User authentication interface
 */
export interface UserAuth {
  /** User ID */
  userId: string;
  
  /** Authentication provider */
  provider: AuthProvider;
  
  /** Provider user ID */
  providerUserId: string;
  
  /** Provider email */
  providerEmail?: string;
  
  /** Access token */
  accessToken?: string;
  
  /** Refresh token */
  refreshToken?: string;
  
  /** Token expiry date */
  expiresAt?: Date;
  
  /** Whether this is the primary auth method */
  isPrimary: boolean;
  
  /** Authentication creation date */
  createdAt: Date;
  
  /** Last authentication date */
  lastUsedAt?: Date;
}

/**
 * User session interface
 */
export interface UserSession {
  /** Session unique identifier */
  id: string;
  
  /** User ID */
  userId: string;
  
  /** Session token */
  token: string;
  
  /** Device information */
  device?: {
    type: 'desktop' | 'mobile' | 'tablet';
    os: string;
    browser: string;
    userAgent: string;
  };
  
  /** IP address */
  ipAddress: string;
  
  /** Location information */
  location?: {
    country: string;
    city: string;
    region: string;
  };
  
  /** Session creation date */
  createdAt: Date;
  
  /** Session expiry date */
  expiresAt: Date;
  
  /** Last activity date */
  lastActiveAt: Date;
  
  /** Whether session is active */
  isActive: boolean;
}

// ===== PERMISSION INTERFACES =====

/**
 * User permission interface
 */
export interface UserPermission {
  /** Permission unique identifier */
  id: string;
  
  /** Permission name */
  name: string;
  
  /** Permission description */
  description: string;
  
  /** Permission resource */
  resource: string;
  
  /** Permission action */
  action: 'create' | 'read' | 'update' | 'delete' | 'manage';
  
  /** Permission scope */
  scope: 'global' | 'organization' | 'course' | 'self';
  
  /** Permission conditions */
  conditions?: Record<string, any>;
}

/**
 * User role definition
 */
export interface UserRoleDefinition {
  /** Role name */
  name: UserRole;
  
  /** Role display name */
  displayName: string;
  
  /** Role description */
  description: string;
  
  /** Role permissions */
  permissions: UserPermission[];
  
  /** Role hierarchy level */
  level: number;
  
  /** Whether role is system role */
  isSystem: boolean;
}

// ===== ACTIVITY INTERFACES =====

/**
 * User activity interface
 */
export interface UserActivity {
  /** Activity unique identifier */
  id: string;
  
  /** User ID */
  userId: string;
  
  /** Activity type */
  type: ActivityType;
  
  /** Activity description */
  description: string;
  
  /** Activity metadata */
  metadata?: Record<string, any>;
  
  /** Related resource ID */
  resourceId?: string;
  
  /** Related resource type */
  resourceType?: string;
  
  /** IP address */
  ipAddress?: string;
  
  /** User agent */
  userAgent?: string;
  
  /** Activity timestamp */
  timestamp: Date;
}

/**
 * User statistics interface
 */
export interface UserStats {
  /** Total courses enrolled */
  coursesEnrolled: number;
  
  /** Total courses completed */
  coursesCompleted: number;
  
  /** Total lessons completed */
  lessonsCompleted: number;
  
  /** Total study time in minutes */
  totalStudyTime: number;
  
  /** Total certificates earned */
  certificatesEarned: number;
  
  /** Total achievements unlocked */
  achievementsUnlocked: number;
  
  /** Current learning streak in days */
  currentStreak: number;
  
  /** Longest learning streak in days */
  longestStreak: number;
  
  /** Average course rating given */
  averageRatingGiven: number;
  
  /** Total course ratings given */
  ratingsGiven: number;
  
  /** Total forum posts */
  forumPosts: number;
  
  /** Total forum replies */
  forumReplies: number;
  
  /** User rank/level */
  rank: number;
  
  /** Experience points */
  experiencePoints: number;
}

/**
 * User achievement interface
 */
export interface UserAchievement {
  /** Achievement unique identifier */
  id: string;
  
  /** Achievement name */
  name: string;
  
  /** Achievement description */
  description: string;
  
  /** Achievement icon URL */
  icon: string;
  
  /** Achievement category */
  category: 'learning' | 'social' | 'milestone' | 'special';
  
  /** Achievement rarity */
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  
  /** Experience points awarded */
  experiencePoints: number;
  
  /** Achievement unlock date */
  unlockedAt: Date;
  
  /** Achievement progress */
  progress?: {
    current: number;
    target: number;
    percentage: number;
  };
}

// ===== SUBSCRIPTION INTERFACES =====

/**
 * User subscription interface
 */
export interface UserSubscription {
  /** Subscription unique identifier */
  id: string;
  
  /** User ID */
  userId: string;
  
  /** Subscription plan */
  plan: SubscriptionPlan;
  
  /** Subscription status */
  status: SubscriptionStatus;
  
  /** Subscription start date */
  startDate: Date;
  
  /** Subscription end date */
  endDate?: Date;
  
  /** Next billing date */
  nextBillingDate?: Date;
  
  /** Subscription amount */
  amount: number;
  
  /** Subscription currency */
  currency: string;
  
  /** Billing interval */
  interval: 'monthly' | 'yearly' | 'lifetime';
  
  /** Payment method ID */
  paymentMethodId?: string;
  
  /** Subscription metadata */
  metadata?: Record<string, any>;
  
  /** Subscription creation date */
  createdAt: Date;
  
  /** Subscription last update date */
  updatedAt: Date;
}

/**
 * Subscription plan interface
 */
export interface SubscriptionPlan {
  /** Plan unique identifier */
  id: string;
  
  /** Plan name */
  name: string;
  
  /** Plan description */
  description: string;
  
  /** Plan price */
  price: number;
  
  /** Plan currency */
  currency: string;
  
  /** Billing interval */
  interval: 'monthly' | 'yearly' | 'lifetime';
  
  /** Plan features */
  features: string[];
  
  /** Plan limits */
  limits: {
    courses: number;
    storage: number;
    downloads: number;
  };
  
  /** Whether plan is active */
  isActive: boolean;
  
  /** Plan creation date */
  createdAt: Date;
}

// ===== UTILITY TYPES =====

/**
 * User creation payload
 */
export type CreateUserPayload = Omit<User, 'id' | 'fullName' | 'createdAt' | 'updatedAt' | 'lastLoginAt' | 'lastActiveAt'>;

/**
 * User update payload
 */
export type UpdateUserPayload = Partial<Pick<User, 'firstName' | 'lastName' | 'username' | 'avatar' | 'bio' | 'website' | 'phone' | 'dateOfBirth' | 'country' | 'city' | 'socialLinks' | 'preferences'>>;

/**
 * User profile update payload
 */
export type UpdateUserProfilePayload = Partial<Pick<UserProfile, 'username' | 'avatar' | 'bio' | 'title' | 'company' | 'website' | 'location' | 'socialLinks' | 'skills' | 'interests' | 'isPublic'>>;

/**
 * User filter options
 */
export interface UserFilters {
  /** Filter by role */
  role?: UserRole[];
  
  /** Filter by status */
  status?: UserStatus[];
  
  /** Filter by subscription status */
  subscriptionStatus?: SubscriptionStatus[];
  
  /** Filter by verification status */
  verificationStatus?: VerificationStatus[];
  
  /** Filter by country */
  country?: string[];
  
  /** Filter by registration date range */
  registrationDateRange?: {
    startDate: Date;
    endDate: Date;
  };
  
  /** Filter by last activity date range */
  lastActivityRange?: {
    startDate: Date;
    endDate: Date;
  };
  
  /** Filter verified users only */
  verifiedOnly?: boolean;
  
  /** Filter active users only */
  activeOnly?: boolean;
}

/**
 * User sort options
 */
export type UserSortBy = 
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'createdAt'
  | 'lastLoginAt'
  | 'lastActiveAt'
  | 'coursesCompleted'
  | 'experiencePoints';

/**
 * Sort direction
 */
export type SortDirection = 'asc' | 'desc';

/**
 * User search and filter interface
 */
export interface UserSearchParams {
  /** Search query */
  query?: string;
  
  /** Filters */
  filters?: UserFilters;
  
  /** Sort by */
  sortBy?: UserSortBy;
  
  /** Sort direction */
  sortDirection?: SortDirection;
  
  /** Page number */
  page?: number;
  
  /** Items per page */
  limit?: number;
}

/**
 * User credentials for authentication
 */
export interface UserCredentials {
  /** User email address */
  email: string;
  /** User password */
  password: string;
  /** Remember user session */
  rememberMe?: boolean;
  /** Two-factor authentication code */
  twoFactorCode?: string;
}

/**
 * User registration data
 */
export interface UserRegistration {
  /** User email address */
  email: string;
  /** User password */
  password: string;
  /** Password confirmation */
  confirmPassword: string;
  /** User first name */
  firstName: string;
  /** User last name */
  lastName: string;
  /** Optional username */
  username?: string;
  /** User role (defaults to 'student') */
  role?: UserRole;
  /** User timezone */
  timezone?: string;
  /** User language preference */
  language?: string;
  /** User country */
  country?: string;
  /** Terms of service acceptance */
  acceptTerms: boolean;
  /** Privacy policy acceptance */
  acceptPrivacy: boolean;
  /** Marketing emails opt-in */
  acceptMarketing?: boolean;
  /** Referral code */
  referralCode?: string;
}

/**
 * Development Notes:
 * 
 * 1. Type Safety:
 *    - All interfaces use strict TypeScript typing
 *    - Proper use of union types for enums
 *    - Optional properties marked with ?
 *    - Consistent naming conventions
 * 
 * 2. Data Modeling:
 *    - Comprehensive user profile management
 *    - Flexible permission system
 *    - Rich preference and settings support
 *    - Activity tracking and analytics
 * 
 * 3. Security:
 *    - Separation of public and private data
 *    - Proper authentication interfaces
 *    - Session management support
 *    - Permission-based access control
 * 
 * 4. Privacy:
 *    - Granular privacy controls
 *    - GDPR compliance considerations
 *    - User consent management
 *    - Data minimization principles
 * 
 * 5. Extensibility:
 *    - Easy to add new user roles
 *    - Flexible metadata system
 *    - Extensible preference structure
 *    - Support for future features
 * 
 * 6. Integration:
 *    - Compatible with authentication providers
 *    - Support for subscription systems
 *    - Analytics and tracking integration
 *    - Social media integration
 */