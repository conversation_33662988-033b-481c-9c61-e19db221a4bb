export type IVerifyUserResponse = {
  id: number;
  name: string;
  is_new_user: boolean;
  is_active: boolean;
};

export interface IUpdatePasswordRequest {
  new_password: string;
  confirm_new_password: string;
}

export type IUpdatePasswordResponse = {
  message: string;
};

export interface ILoginUserRequest {
  username: string;
  password: string;
}

export type ILoginUserResponse = {
  user_id: number;
  npk: string;
  username: string;
  phone: string;
  email: string;
  role_id: number;
  role_name: string;
  forum_title_id: number;
  forum_title: string;
  is_new_user: boolean;
  is_need_neop: boolean;
  is_need_welcoming_kit: boolean;
  token: ITokenResponse | null;
};

export type ITokenResponse = {
  accessToken: string;
  refreshToken: string;
};
