import { ITokenResponse } from "./login";

export interface ICheckMfaRequirementRequest {
  userIdentifier: string;
  riskLevel: "LOW" | "MEDIUM" | "HIGH"; // restrict to enum if backend enforces it
  visitorId: string;
  isPrivate: boolean;
  forceMfa: boolean;
}

export interface ICheckMfaRequirementResponse {
  mfaRequired: boolean;
  isEnrolled: boolean;
  reason: string;
  token?: ITokenResponse;
}

export interface IEnrollMfaRequest {
  userIdentifier: string;
  userName: string;
}

export interface IEnrollMfaResponse {
  setupKey: string;
  qrCodeDataUrl: string;
  recoveryCodes: string[];
}

export interface IVerifyMfaSetupRequest {
  userIdentifier: string;
  token: string;
}

export interface IVerifyMfaRequest {
  userIdentifier: string;
  token: string;
}

export interface IVerifyMfaResponse {
  token?: ITokenResponse;
}

export interface ISendOtpRequest {
  user_id: number;
  send_to: string;
  provider: "email" | "phone";
  otp_type: "login" | "forgot_password" | "changepass";
}

export interface ISendOtpResponse {
  key: string;
}

export interface IVerifyOtpRequest {
  otp: string;
  otp_type: "login" | "forgot_password" | "changepass";
  key: string;
}

export interface IVerifyOtpResponse {
  user_id: number;
  npk: string;
  username: string;
  phone: string;
  email: string;
  role_id: number;
  role_name: string;
  forum_title_id: number;
  forum_title: string;
  is_new_user: boolean;
  is_need_neop: boolean;
  is_need_welcoming_kit: boolean;
  token?: ITokenResponse;
}
