import { NextRequest, NextResponse } from "next/server";
import { getNeedUpdatePassword, getSession } from "./services/session/session";

const PUBLIC_PATHS = ["/login"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = await getSession();

  const isPublicPath = PUBLIC_PATHS.includes(pathname);
  const isAuthenticated = !!token.accessToken;
  const needUpdatePassword = await getNeedUpdatePassword();

  // case user not finish update the password
  // if (isAuthenticated && needUpdatePassword && !isPublicPath) {
  //   return NextResponse.redirect(new URL("/login", request.url));
  // }

  // if (isPublicPath && isAuthenticated && !needUpdatePassword) {
  //   return NextResponse.redirect(new URL("/homepage", request.url));
  // }

  // if (!isPublicPath && !isAuthenticated) {
  //   return NextResponse.redirect(new URL("/login", request.url));
  // }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
  ],
};
