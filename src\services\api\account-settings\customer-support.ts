'use server';

import { ICustomerServiceBody } from '@/interfaces/admin/account-settings/customer-support';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import { api } from '@/services/satellite';
import { handleAxiosError } from '@/utils/common/axios';

// send message to customer service
export const apiCustomerService = async (body: ICustomerServiceBody) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      '/int/customer-support',
      body
    );

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};
