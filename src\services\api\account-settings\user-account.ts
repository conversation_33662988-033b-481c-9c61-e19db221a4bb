'use server';

import {
  IChangePasswordBody,
  IGetUserAccountDetailResponse,
  IVerifyPasswordBody,
} from '@/interfaces/admin/account-settings/user-account';
import { IGlobalResponseDto } from '@/interfaces/global/response';
import { api } from '@/services/satellite';
import { handleAxiosError } from '@/utils/common/axios';

// get user account detail
export const apiGetUserAccountDetail = async () => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetUserAccountDetailResponse>
    >('/general/user/account/detail');

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};

// update user account detail
export const apiUpdateUserAccountDetail = async (formData: FormData) => {
  try {
    console.log('formData apiUpdateUserAccountDetail', formData);

    const response = await api.post<IGlobalResponseDto>(
      '/general/user/account/update',
      formData
    );

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};

// update password account detail
export const apiVerifyPasswordUserAccount = async (
  body: IVerifyPasswordBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      '/general/user/account/verify-password',
      body
    );

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};

// update password account detail
export const apiChangePasswordUserAccount = async (
  body: IChangePasswordBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      '/general/user/account/change-password',
      body
    );

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};
