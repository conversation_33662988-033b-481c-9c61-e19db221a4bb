"use server";

import {
  IGetDetailFaqResponse,
  IGetListFaqParams,
  IGetListFaqResponse,
  IGetListTagParams,
  IGetListTagResponse,
} from "@/interfaces/admin/manage-faq/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListFaq = async (params?: IGetListFaqParams) => {
  const defaultParams = {
    page: 1,
    limit: 10,
  };
  const mergedParams = { ...defaultParams, ...params };
  try {
    const response = await api.get<IGlobalResponseDto<IGetListFaqResponse[]>>(
      "/cms/admin/faq-list",
      { params: mergedParams }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetListTag = async (params: IGetListTagParams) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetListTagResponse[]>>(
      "/cms/admin/master/tags",
      { params: params }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiInsertFaq = async (formData: FormData) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      "/cms/admin/faq-insert",
      formData
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdateFaq = async (formData: FormData, id: string) => {
  try {
    const response = await api.post<IGlobalResponseDto<any>>(
      `/admin/faq-update/${id}`,
      formData
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiDetailFaq = async (id: string) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetDetailFaqResponse>>(
      `/admin/faq-detail/${id}`
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
