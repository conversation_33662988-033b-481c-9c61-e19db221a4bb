"use server";

import {
  IGetListUserLogLoginQuery,
  IUserLog,
} from "@/interfaces/admin/user-log/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListUserLogLogin = async (
  params?: IGetListUserLogLoginQuery
) => {
  try {
    const response = await api.get<IGlobalResponseDto<IUserLog[]>>(
      "/cms/admin/log-login/list",
      { params }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiDownloadListLogUserLoginFile = async (
  params: IGetListUserLogLoginQuery
) => {
  try {
    const response = await api.get(`/cms/admin/log-login/export`, {
      params,
      responseType: "arraybuffer",
    });

    let filename = "log-login-user.xlsx";

    const disposition = response.headers["content-disposition"];
    if (disposition && disposition.includes("filename=")) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match?.[1]) {
        filename = match[1];
      }
    }

    const base64File = Buffer.from(response.data).toString("base64");

    return {
      filename,
      file: base64File,
    };
  } catch (error) {
    throw handleAxiosError(error);
  }
};
