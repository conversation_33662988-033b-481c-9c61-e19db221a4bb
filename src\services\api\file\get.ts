"use server";

import { IGetFileQuery } from "@/interfaces/file/get";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetFile = async (query: IGetFileQuery) => {
  try {
    const response = await api.get(`/int/file`, {
      params: query,
      responseType: "arraybuffer",
    });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
