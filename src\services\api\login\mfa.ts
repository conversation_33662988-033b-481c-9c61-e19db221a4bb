"use server";

import { IGlobalResponseDto } from "@/interfaces/global/response";
import { handleAxiosError } from "@/utils/common/axios";
import { api } from "@/services/satellite";
import {
  ICheckMfaRequirementRequest,
  ICheckMfaRequirementResponse,
  IEnrollMfaRequest,
  IEnrollMfaResponse,
  ISendOtpRequest,
  ISendOtpResponse,
  IVerifyMfaRequest,
  IVerifyMfaResponse,
  IVerifyMfaSetupRequest,
  IVerifyOtpRequest,
  IVerifyOtpResponse,
} from "@/interfaces/user/mfa";

export const apiCheckMfaRequirement = async (
  body: ICheckMfaRequirementRequest
) => {
  try {
    const response = await api.post<
      IGlobalResponseDto<ICheckMfaRequirementResponse>
    >("/int/mfa/check-requirement", body);

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiEnrollMfa = async (body: IEnrollMfaRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<IEnrollMfaResponse>>(
      "/int/mfa/enroll",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiVerifyMfaSetup = async (body: IVerifyMfaSetupRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<IVerifyMfaResponse>>(
      "/int/mfa/verify-setup",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiVerifyMfa = async (body: IVerifyMfaRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<IVerifyMfaResponse>>(
      "/int/mfa/verify",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiSendOtp = async (body: ISendOtpRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<ISendOtpResponse>>(
      "/int/otp/send-otp",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiVerifyOtp = async (body: IVerifyOtpRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<IVerifyOtpResponse>>(
      "/int/otp/verify-otp",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
