"use server";

import { IGlobalResponseDto } from "@/interfaces/global/response";
import {
  ILoginUserRequest,
  ILoginUserResponse,
  ITokenResponse,
  IUpdatePasswordRequest,
  IUpdatePasswordResponse,
  IVerifyUserResponse,
} from "@/interfaces/user/login";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiVerifyUser = async (body: { username: string }) => {
  try {
    const response = await api.post<IGlobalResponseDto<IVerifyUserResponse>>(
      "/general/user/verify-user",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiUpdatePassword = async (
  body: IUpdatePasswordRequest,
  userId: number
) => {
  try {
    const response = await api.put<IGlobalResponseDto<IUpdatePasswordResponse>>(
      "/general/user/update-password",
      body,
      {
        headers: {
          " app-token": `${userId}:1:1`,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiLoginUser = async (body: ILoginUserRequest) => {
  try {
    const response = await api.post<IGlobalResponseDto<ILoginUserResponse>>(
      "/general/user/login",
      body
    );

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

// logout
export const apiLogoutUser = async () => {
  try {
    const response = await api.post<
      IGlobalResponseDto<{
        message: string;
      }>
    >("/general/user/logout");

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

// refresh-token
export const apiRefreshToken = async () => {
  try {
    const response = await api.post<
      IGlobalResponseDto<{ token: ITokenResponse }>
    >("/general/user/refresh-token");

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
