"use server";

import {
  IGetJobPositionDepartmentListQuery,
  IGetJobPositionDepartmentListResponse,
} from "@/interfaces/admin/manage-job/department";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetJobPositionDepartmentList = async (
  query: IGetJobPositionDepartmentListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionDepartmentListResponse[]>
    >("/cms/admin/master/department", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
