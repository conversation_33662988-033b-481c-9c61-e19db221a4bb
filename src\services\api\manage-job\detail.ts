"use server";

import {
  IGetJobPositionDetailParams,
  IGetJobPositionDetailResponse,
} from "@/interfaces/admin/manage-job/detail";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetJobPositionDetail = async (
  params: IGetJobPositionDetailParams
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionDetailResponse>
    >(`/cms/admin/job-detail/${params.id}`);

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
