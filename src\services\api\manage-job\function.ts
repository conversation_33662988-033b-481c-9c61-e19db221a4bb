"use server";

import {
  IGetJobPositionFunctionListQuery,
  IGetJobPositionFunctionListResponse,
} from "@/interfaces/admin/manage-job/function";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetJobPositionFunctionList = async (
  query: IGetJobPositionFunctionListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionFunctionListResponse[]>
    >("/cms/admin/master/job-function", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
