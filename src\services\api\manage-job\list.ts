"use server";

import {
  IGetJobPositionListQuery,
  IGetJobPositionListResponse,
} from "@/interfaces/admin/manage-job/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetJobPositionList = async (
  query: IGetJobPositionListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionListResponse[]>
    >("/cms/admin/job-list", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
