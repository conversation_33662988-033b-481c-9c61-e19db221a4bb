"use server";

import { ICreateJobPositionBody } from "@/interfaces/admin/manage-job/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiCreateJobPosition = async (body: ICreateJobPositionBody) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      "/cms/admin/job-insert",
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
