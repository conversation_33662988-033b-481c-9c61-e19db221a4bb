"use server";

import {
  IGetJobPositionStartingLevelListQuery,
  IGetJobPositionStartingLevelListResponse,
} from "@/interfaces/admin/manage-job/starting-level";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetJobPositionStartingLevelList = async (
  query: IGetJobPositionStartingLevelListQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionStartingLevelListResponse[]>
    >("/cms/admin/master/starting-level", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
