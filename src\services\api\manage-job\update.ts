"use server";

import {
  IUpdateJobPositionBody,
  IUpdateJobPositionParams,
} from "@/interfaces/admin/manage-job/update";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite/";
import { handleAxiosError } from "@/utils/common/axios";

export const apiUpdateJobPosition = async (
  params: IUpdateJobPositionParams,
  body: IUpdateJobPositionBody
) => {
  try {
    const response = await api.post<IGlobalResponseDto>(
      `/cms/admin/job-update/${params.id}`,
      body
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
