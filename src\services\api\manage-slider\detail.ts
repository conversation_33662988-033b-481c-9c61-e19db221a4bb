"use server";

import {
  IGetSliderDetailParams,
  IGetSliderDetailResponse,
} from "@/interfaces/admin/manage-slider/detail";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetSliderDetail = async (params: IGetSliderDetailParams) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetSliderDetailResponse>
    >(`/cms/admin/slider-detail/${params.id}`);

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
