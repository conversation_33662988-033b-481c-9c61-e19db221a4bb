"use server";

import { IGetSliderListResponse } from "@/interfaces/admin/manage-slider/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetSliderList = async () => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetSliderListResponse[]>
    >("/cms/admin/slider-list");

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
