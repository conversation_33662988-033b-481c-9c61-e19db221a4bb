"use server";

import { ICreateSliderBody } from "@/interfaces/admin/manage-slider/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiCreateSlider = async (body: ICreateSliderBody) => {
  try {
    const form = new FormData();
    const { slider_desktop, slider_mobile, ...data } = body;

    form.append("data", JSON.stringify(data));
    form.append("slider_desktop", slider_desktop);
    form.append("slider_mobile", slider_mobile);

    const response = await api.post<IGlobalResponseDto>(
      "/cms/admin/slider-insert",
      form
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
