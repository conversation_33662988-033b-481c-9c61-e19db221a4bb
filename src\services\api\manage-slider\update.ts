"use server";

import { IUpdateSliderBody } from "@/interfaces/admin/manage-slider/update";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiUpdateSlider = async (body: IUpdateSliderBody) => {
  try {
    const form = new FormData();
    const { slider_desktop, slider_mobile, id, ...data } = body;

    form.append("data", JSON.stringify(data));

    if (slider_desktop) form.append("slider_desktop", slider_desktop);
    if (slider_mobile) form.append("slider_mobile", slider_mobile);

    const response = await api.post<IGlobalResponseDto>(
      `/cms/admin/slider-update/${id}`,
      form
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
