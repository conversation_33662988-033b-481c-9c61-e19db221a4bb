"use server";

import {
  IGetUserDetailParams,
  IGetUserDetailResponse,
} from "@/interfaces/admin/user-management/detail";

import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetUserDetail = async (params: IGetUserDetailParams) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetUserDetailResponse>>(
      `/cms/admin/user-detail/${params.id}`
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
