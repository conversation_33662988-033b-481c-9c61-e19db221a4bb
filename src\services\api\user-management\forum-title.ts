"use server";

import {
  IGetForumTitleQuery,
  IGetForumTitleResponse,
} from "@/interfaces/admin/user-management/forum-title";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetMasterForumTitle = async (query: IGetForumTitleQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetForumTitleResponse[]>
    >("/cms/admin/master/forum-title", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
