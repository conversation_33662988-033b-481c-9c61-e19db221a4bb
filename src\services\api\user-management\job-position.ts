"use server";

import {
  IGetJobPositionQuery,
  IGetJobPositionResponse,
} from "@/interfaces/admin/user-management/job-position";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetMasterJobPosition = async (query: IGetJobPositionQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetJobPositionResponse[]>
    >("/cms/admin/master/job-position", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
