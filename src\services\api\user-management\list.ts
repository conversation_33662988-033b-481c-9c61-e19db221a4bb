"use server";
import {
  IGetUserListQuery,
  IGetUserListResponse,
} from "@/interfaces/admin/user-management/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetUserList = async (query: IGetUserListQuery) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetUserListResponse[]>>(
      "/cms/admin/user-list",
      { params: query }
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
