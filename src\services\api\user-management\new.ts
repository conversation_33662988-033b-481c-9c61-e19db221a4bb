"use server";

import { ICreateUserBody } from "@/interfaces/admin/user-management/new";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiCreateNewUser = async (file: File, body: ICreateUserBody) => {
  try {
    const form = new FormData();
    form.append("file", file);
    form.append("data", JSON.stringify(body));

    const response = await api.post<IGlobalResponseDto>(
      "/cms/admin/user-insert",
      form
    );

    return response.data;
  } catch (error: any) {
    throw handleAxiosError(error);
  }
};
