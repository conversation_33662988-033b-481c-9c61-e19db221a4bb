"use server";

import { IUpdateUserBody } from "@/interfaces/admin/user-management/update";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiUpdateUser = async (
  id: number,
  file: File | null,
  body: IUpdateUserBody
) => {
  try {
    const form = new FormData();

    if (file) form.append("file", file);
    form.append("data", JSON.stringify(body));

    const response = await api.post<IGlobalResponseDto>(
      `/cms/admin/user-update/${id}`,
      form
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
