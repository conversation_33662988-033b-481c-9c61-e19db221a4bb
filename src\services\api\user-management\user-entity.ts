"use server";

import {
  IGetUserEntityQuery,
  IGetUserEntityResponse,
} from "@/interfaces/admin/user-management/user-entity";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetUserMasterEntity = async (query: IGetUserEntityQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetUserEntityResponse[]>
    >("/cms/admin/master/user-entity", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
