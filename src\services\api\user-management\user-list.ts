"use server";

import {
  IGetMasterUserListQuery,
  IGetMasterUserListResponse,
} from "@/interfaces/admin/user-management/user-list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetMasterUserList = async (query: IGetMasterUserListQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetMasterUserListResponse[]>
    >("/cms/admin/master/user-list", { params: query });

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
