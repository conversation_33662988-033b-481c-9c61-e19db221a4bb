"use server";

import {
  IGetUserRoleQuery,
  IGetUserRoleResponse,
} from "@/interfaces/admin/user-management/user-role";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetUserMasterRole = async (query: IGetUserRoleQuery) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetUserRoleResponse[]>>(
      "/cms/admin/master/user-role",
      { params: query }
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
