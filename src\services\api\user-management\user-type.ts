"use server";

import {
  IGetUserTypeQuery,
  IGetUserTypeResponse,
} from "@/interfaces/admin/user-management/user-type";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetUserMasterType = async (query: IGetUserTypeQuery) => {
  try {
    const response = await api.get<IGlobalResponseDto<IGetUserTypeResponse[]>>(
      "/cms/admin/master/user-Type",
      { params: query }
    );

    return response.data;
  } catch (error) {
    throw handleAxiosError(error);
  }
};
