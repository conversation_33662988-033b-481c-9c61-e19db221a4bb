import { ICustomerServiceBody } from '@/interfaces/admin/account-settings/customer-support';
import { apiCustomerService } from '@/services/api/account-settings/customer-support';
import { useMutation } from '@tanstack/react-query';

export const useCustomerServiceMutation = () => {
  return useMutation({
    mutationFn: async (body: ICustomerServiceBody) => {
      return await apiCustomerService(body);
    },
  });
};
