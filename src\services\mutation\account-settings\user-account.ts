import { useMutation } from '@tanstack/react-query';
import {
  IChangePasswordBody,
  IVerifyPasswordBody,
} from '@/interfaces/admin/account-settings/user-account';
import {
  apiChangePasswordUserAccount,
  apiUpdateUserAccountDetail,
  apiVerifyPasswordUserAccount,
} from '@/services/api/account-settings/user-account';

export const useUpdateUserAccountMutation = () => {
  return useMutation({
    mutationKey: ['update-user-account'],
    mutationFn: async (formData: FormData) => {
      return await apiUpdateUserAccountDetail(formData);
    },
  });
};

export const useVerifyPasswordUserAccountMutation = () => {
  return useMutation({
    mutationKey: ['verify-password-user-account'],
    mutationFn: async (body: IVerifyPasswordBody) => {
      return await apiVerifyPasswordUserAccount(body);
    },
  });
};

export const useChangePasswordUserAccountMutation = () => {
  return useMutation({
    mutationKey: ['change-password-user-account'],
    mutationFn: async (body: IChangePasswordBody) => {
      return await apiChangePasswordUserAccount(body);
    },
  });
};
