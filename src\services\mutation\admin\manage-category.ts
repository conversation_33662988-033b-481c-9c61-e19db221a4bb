import {
  ICreateCategoryPayload,
  ICreateSubCategoryPayload,
  IUpdateCategoryPayload,
  IUpdateSubCategoryPayload,
} from "@/interfaces/admin/manage-category/new";
import {
  apiInsertCategory,
  apiInsertSubCategory,
  apiUpdateCategory,
  apiUpdateSubCategory,
} from "@/services/api/admin/manage-category";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useInsertCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["insert-category"],
    mutationFn: async (body: ICreateCategoryPayload) => {
      return await apiInsertCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["category"],
      });
    },
  });
};

export const useInsertSubCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["insert-subcategory"],
    mutationFn: async (body: ICreateSubCategoryPayload) => {
      return await apiInsertSubCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};

export const useUpdateCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-category"],
    mutationFn: async (body: IUpdateCategoryPayload) => {
      return await apiUpdateCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["category"],
      });
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};

export const useUpdateSubCategoryMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-subcategory"],
    mutationFn: async (body: IUpdateSubCategoryPayload) => {
      return await apiUpdateSubCategory(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["subcategory"],
      });
    },
  });
};
