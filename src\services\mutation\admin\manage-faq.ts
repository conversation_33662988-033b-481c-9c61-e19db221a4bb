import { apiInsertFaq, apiUpdateFaq } from "@/services/api/admin/manage-faq";
import { faqQueryKeys } from "@/services/query/admin/manage-faq";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useInsertFaqMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["insert-faq"],
    mutationFn: async (formData: FormData) => {
      return await apiInsertFaq(formData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: faqQueryKeys.list(),
      });
    },
  });
};

export const useUpdateFaqMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["update-faq"],
    mutationFn: async ({
      formData,
      id,
    }: {
      formData: FormData;
      id: string;
    }) => {
      return await apiUpdateFaq(formData, id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: faqQueryKeys.list(),
      });
    },
  });
};
