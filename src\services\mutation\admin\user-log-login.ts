import { IGetListUserLogLoginQuery } from "@/interfaces/admin/user-log/list";
import { apiDownloadListLogUserLoginFile } from "@/services/api/admin/user-log-login";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useDownloadListLogUserLoginFileMutation = () => {
  return useMutation({
    mutationKey: ["log-login-user-file"],
    mutationFn: async (params: IGetListUserLogLoginQuery) => {
      const result = await apiDownloadListLogUserLoginFile(params);

      const byteCharacters = atob(result.file);
      const byteNumbers = new Array(byteCharacters.length)
        .fill(0)
        .map((_, i) => byteCharacters.charCodeAt(i));
      const byteArray = new Uint8Array(byteNumbers);

      const blob = new Blob([byteArray]);
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", result.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    },
    onError: (err) => {
      console.log(err);
      toast.error("Failed to download file. Try again!");
    },
  });
};
