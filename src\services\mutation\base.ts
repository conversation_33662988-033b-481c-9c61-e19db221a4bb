/**
 * Base Mutation
 * 
 * This file provides foundational mutation utilities for data modification operations.
 * It handles optimistic updates, rollback mechanisms, error handling, and cache invalidation.
 * 
 * Key Concepts:
 * - Optimistic updates
 * - Rollback mechanisms
 * - Cache invalidation
 * - Error handling and retries
 * - Loading state management
 * - Success/error callbacks
 * 
 * Usage Examples:
 * ```tsx
 * // Import base mutation
 * import { createMutation, MutationOptions } from '@/services/mutation/base';
 * 
 * // Create a course mutation
 * const createCourseMutation = createMutation<Course, CreateCourseData>({
 *   mutationFn: (data) => apiClient.post('/courses', data),
 *   onSuccess: (course) => {
 *     invalidateQueries('courses');
 *     showNotification('Course created successfully');
 *   },
 *   onError: (error) => {
 *     showNotification('Failed to create course', 'error');
 *   }
 * });
 * 
 * // Use in component
 * const { mutate, loading, error } = createCourseMutation.use();
 * ```
 */

// import { apiClient, type ApiResponse, type ApiError } from '../config/api';
// import { invalidateQueries } from '../fetcher/base';
// import { isDevelopment } from '../config/env';

// // ===== MUTATION TYPES =====

// /**
//  * Mutation state
//  */
// export interface MutationState<TData, TError = ApiError> {
//   /** Response data */
//   data: TData | null;
  
//   /** Loading state */
//   loading: boolean;
  
//   /** Error state */
//   error: TError | null;
  
//   /** Mutation status */
//   status: 'idle' | 'loading' | 'success' | 'error';
  
//   /** Last mutation timestamp */
//   lastMutation: number | null;
  
//   /** Mutation count */
//   mutationCount: number;
  
//   /** Retry count */
//   retryCount: number;
  
//   /** Is optimistic update active */
//   isOptimistic: boolean;
// }

// /**
//  * Mutation function type
//  */
// export type MutationFunction<TData, TVariables> = (
//   variables: TVariables
// ) => Promise<ApiResponse<TData>>;

// /**
//  * Mutation options
//  */
// export interface MutationOptions<TData, TVariables, TError = ApiError> {
//   /** Mutation function */
//   mutationFn: MutationFunction<TData, TVariables>;
  
//   /** Success callback */
//   onSuccess?: (data: TData, variables: TVariables) => void | Promise<void>;
  
//   /** Error callback */
//   onError?: (error: TError, variables: TVariables) => void | Promise<void>;
  
//   /** Loading callback */
//   onLoading?: (loading: boolean, variables?: TVariables) => void;
  
//   /** Mutation callback (called before mutation) */
//   onMutate?: (variables: TVariables) => void | Promise<void>;
  
//   /** Settled callback (called after success or error) */
//   onSettled?: (
//     data: TData | null,
//     error: TError | null,
//     variables: TVariables
//   ) => void | Promise<void>;
  
//   /** Retry count */
//   retryCount?: number;
  
//   /** Retry delay in milliseconds */
//   retryDelay?: number;
  
//   /** Enable optimistic updates */
//   optimisticUpdates?: boolean;
  
//   /** Optimistic update function */
//   optimisticUpdate?: (variables: TVariables) => TData;
  
//   /** Rollback function */
//   rollback?: (variables: TVariables) => void;
  
//   /** Cache invalidation patterns */
//   invalidateQueries?: (string | RegExp)[];
  
//   /** Data transformer */
//   transform?: (data: any) => TData;
  
//   /** Error transformer */
//   transformError?: (error: ApiError) => TError;
  
//   /** Should retry predicate */
//   shouldRetry?: (error: TError, retryCount: number) => boolean;
  
//   /** Mutation key for deduplication */
//   mutationKey?: string;
  
//   /** Enable mutation deduplication */
//   dedupe?: boolean;
// }

// /**
//  * Mutation result
//  */
// export interface MutationResult<TData, TVariables, TError = ApiError> {
//   /** Response data */
//   data: TData | null;
  
//   /** Loading state */
//   loading: boolean;
  
//   /** Error state */
//   error: TError | null;
  
//   /** Mutation status */
//   status: 'idle' | 'loading' | 'success' | 'error';
  
//   /** Is optimistic update active */
//   isOptimistic: boolean;
  
//   /** Mutate function */
//   mutate: (variables: TVariables) => Promise<TData | null>;
  
//   /** Async mutate function */
//   mutateAsync: (variables: TVariables) => Promise<TData>;
  
//   /** Reset mutation state */
//   reset: () => void;
  
//   /** Cancel ongoing mutation */
//   cancel: () => void;
// }

// /**
//  * Mutation instance
//  */
// export interface Mutation<TData, TVariables, TError = ApiError> {
//   /** Execute mutation */
//   mutate: (variables: TVariables) => Promise<TData | null>;
  
//   /** Execute mutation (async) */
//   mutateAsync: (variables: TVariables) => Promise<TData>;
  
//   /** Get current state */
//   getState: () => MutationState<TData, TError>;
  
//   /** Subscribe to state changes */
//   subscribe: (callback: (state: MutationState<TData, TError>) => void) => () => void;
  
//   /** Use hook for React components */
//   use: () => MutationResult<TData, TVariables, TError>;
  
//   /** Reset state */
//   reset: () => void;
  
//   /** Update options */
//   updateOptions: (options: Partial<MutationOptions<TData, TVariables, TError>>) => void;
// }

// // ===== MUTATION MANAGEMENT =====

// /**
//  * Pending mutations store for deduplication
//  */
// const pendingMutations = new Map<string, Promise<any>>();

// /**
//  * Mutation subscribers store
//  */
// const mutationSubscribers = new Map<string, Set<(state: any) => void>>();

// /**
//  * Generate mutation key
//  */
// const generateMutationKey = <TData, TVariables>(
//   options: MutationOptions<TData, TVariables>,
//   variables: TVariables
// ): string => {
//   if (options.mutationKey) {
//     return `${options.mutationKey}:${JSON.stringify(variables)}`;
//   }
  
//   return `mutation:${JSON.stringify(variables)}`;
// };

// /**
//  * Subscribe to mutation state changes
//  */
// const subscribeMutation = <TData, TError>(
//   mutationKey: string,
//   callback: (state: MutationState<TData, TError>) => void
// ): (() => void) => {
//   if (!mutationSubscribers.has(mutationKey)) {
//     mutationSubscribers.set(mutationKey, new Set());
//   }
  
//   const keySubscribers = mutationSubscribers.get(mutationKey)!;
//   keySubscribers.add(callback);
  
//   return () => {
//     keySubscribers.delete(callback);
//     if (keySubscribers.size === 0) {
//       mutationSubscribers.delete(mutationKey);
//     }
//   };
// };

// /**
//  * Notify mutation subscribers
//  */
// const notifyMutationSubscribers = <TData, TError>(
//   mutationKey: string,
//   state: MutationState<TData, TError>
// ): void => {
//   const keySubscribers = mutationSubscribers.get(mutationKey);
//   if (keySubscribers) {
//     keySubscribers.forEach(callback => {
//       try {
//         callback(state);
//       } catch (error) {
//         if (isDevelopment) {
//           console.error('Error in mutation subscriber:', error);
//         }
//       }
//     });
//   }
// };

// /**
//  * Sleep utility for retry delays
//  */
// const sleep = (ms: number): Promise<void> => {
//   return new Promise(resolve => setTimeout(resolve, ms));
// };

// // ===== MUTATION IMPLEMENTATION =====

// /**
//  * Create mutation instance
//  */
// export const createMutation = <TData, TVariables, TError = ApiError>(
//   options: MutationOptions<TData, TVariables, TError>
// ): Mutation<TData, TVariables, TError> => {
//   const mutationKey = options.mutationKey || `mutation-${Date.now()}-${Math.random()}`;
  
//   // Default options
//   const defaultOptions: Required<Omit<MutationOptions<TData, TVariables, TError>, 
//     'onSuccess' | 'onError' | 'onLoading' | 'onMutate' | 'onSettled' | 
//     'optimisticUpdate' | 'rollback' | 'invalidateQueries' | 'transform' | 
//     'transformError' | 'shouldRetry' | 'mutationKey'
//   >> = {
//     mutationFn: options.mutationFn,
//     retryCount: 3,
//     retryDelay: 1000,
//     optimisticUpdates: false,
//     dedupe: true
//   };
  
//   const finalOptions = { ...defaultOptions, ...options };
  
//   // State management
//   let state: MutationState<TData, TError> = {
//     data: null,
//     loading: false,
//     error: null,
//     status: 'idle',
//     lastMutation: null,
//     mutationCount: 0,
//     retryCount: 0,
//     isOptimistic: false
//   };
  
//   // Abort controller for cancellation
//   let abortController: AbortController | null = null;
  
//   /**
//    * Update state and notify subscribers
//    */
//   const updateState = (updates: Partial<MutationState<TData, TError>>): void => {
//     state = { ...state, ...updates };
//     notifyMutationSubscribers(mutationKey, state);
    
//     // Call callbacks
//     if (updates.loading !== undefined && finalOptions.onLoading) {
//       finalOptions.onLoading(updates.loading);
//     }
//   };
  
//   /**
//    * Execute mutation with retry logic
//    */
//   const executeMutation = async (variables: TVariables): Promise<TData | null> => {
//     const dedupeKey = generateMutationKey(finalOptions, variables);
    
//     try {
//       // Check for pending mutation (deduplication)
//       if (finalOptions.dedupe && pendingMutations.has(dedupeKey)) {
//         return await pendingMutations.get(dedupeKey);
//       }
      
//       // Create abort controller
//       abortController = new AbortController();
      
//       // Call onMutate callback
//       if (finalOptions.onMutate) {
//         await finalOptions.onMutate(variables);
//       }
      
//       // Handle optimistic updates
//       let optimisticData: TData | null = null;
//       if (finalOptions.optimisticUpdates && finalOptions.optimisticUpdate) {
//         optimisticData = finalOptions.optimisticUpdate(variables);
//         updateState({
//           data: optimisticData,
//           loading: true,
//           error: null,
//           status: 'loading',
//           isOptimistic: true
//         });
//       } else {
//         updateState({
//           loading: true,
//           error: null,
//           status: 'loading',
//           isOptimistic: false
//         });
//       }
      
//       // Create mutation promise
//       const mutationPromise = (async (): Promise<TData | null> => {
//         let retryCount = 0;
//         const maxRetries = finalOptions.retryCount;
        
//         const attemptMutation = async (): Promise<TData> => {
//           try {
//             const response = await finalOptions.mutationFn(variables);
            
//             // Transform response data
//             let responseData = response.data;
//             if (finalOptions.transform) {
//               responseData = finalOptions.transform(responseData);
//             }
            
//             return responseData;
            
//           } catch (error) {
//             // Handle abort
//             if (error instanceof Error && error.name === 'AbortError') {
//               throw error;
//             }
            
//             // Transform error
//             let mutationError = error as TError;
//             if (finalOptions.transformError) {
//               mutationError = finalOptions.transformError(error as ApiError);
//             }
            
//             // Check if should retry
//             const shouldRetry = finalOptions.shouldRetry 
//               ? finalOptions.shouldRetry(mutationError, retryCount)
//               : retryCount < maxRetries;
            
//             if (shouldRetry) {
//               retryCount++;
              
//               // Wait before retry
//               if (finalOptions.retryDelay) {
//                 await sleep(finalOptions.retryDelay * retryCount);
//               }
              
//               // Log retry attempt in development
//               if (isDevelopment) {
//                 console.warn(`Mutation retry ${retryCount}/${maxRetries}:`, {
//                   variables,
//                   error: mutationError
//                 });
//               }
              
//               return attemptMutation();
//             }
            
//             throw mutationError;
//           }
//         };
        
//         const result = await attemptMutation();
        
//         // Update state with success
//         updateState({
//           data: result,
//           loading: false,
//           error: null,
//           status: 'success',
//           lastMutation: Date.now(),
//           mutationCount: state.mutationCount + 1,
//           retryCount: 0,
//           isOptimistic: false
//         });
        
//         // Invalidate queries
//         if (finalOptions.invalidateQueries) {
//           finalOptions.invalidateQueries.forEach(pattern => {
//             invalidateQueries(pattern);
//           });
//         }
        
//         // Call success callback
//         if (finalOptions.onSuccess) {
//           await finalOptions.onSuccess(result, variables);
//         }
        
//         // Call settled callback
//         if (finalOptions.onSettled) {
//           await finalOptions.onSettled(result, null, variables);
//         }
        
//         return result;
//       })();
      
//       // Store pending mutation for deduplication
//       if (finalOptions.dedupe) {
//         pendingMutations.set(dedupeKey, mutationPromise);
//       }
      
//       const result = await mutationPromise;
      
//       // Clean up pending mutation
//       pendingMutations.delete(dedupeKey);
      
//       return result;
      
//     } catch (error) {
//       // Clean up pending mutation
//       pendingMutations.delete(dedupeKey);
      
//       // Handle abort
//       if (error instanceof Error && error.name === 'AbortError') {
//         return null;
//       }
      
//       const mutationError = error as TError;
      
//       // Rollback optimistic updates
//       if (finalOptions.optimisticUpdates && finalOptions.rollback) {
//         finalOptions.rollback(variables);
//       }
      
//       // Update state with error
//       updateState({
//         loading: false,
//         error: mutationError,
//         status: 'error',
//         retryCount: state.retryCount + 1,
//         isOptimistic: false
//       });
      
//       // Call error callback
//       if (finalOptions.onError) {
//         await finalOptions.onError(mutationError, variables);
//       }
      
//       // Call settled callback
//       if (finalOptions.onSettled) {
//         await finalOptions.onSettled(null, mutationError, variables);
//       }
      
//       throw mutationError;
//     }
//   };
  
//   // Mutation interface implementation
//   const mutation: Mutation<TData, TVariables, TError> = {
//     async mutate(variables: TVariables): Promise<TData | null> {
//       try {
//         return await executeMutation(variables);
//       } catch (error) {
//         // Return null for mutate (non-throwing version)
//         return null;
//       }
//     },
    
//     async mutateAsync(variables: TVariables): Promise<TData> {
//       const result = await executeMutation(variables);
//       if (result === null) {
//         throw new Error('Mutation failed');
//       }
//       return result;
//     },
    
//     getState(): MutationState<TData, TError> {
//       return { ...state };
//     },
    
//     subscribe(callback: (state: MutationState<TData, TError>) => void): () => void {
//       return subscribeMutation(mutationKey, callback);
//     },
    
//     use(): MutationResult<TData, TVariables, TError> {
//       // This would be implemented with React hooks in a real implementation
//       // For now, return a basic implementation
//       return {
//         data: state.data,
//         loading: state.loading,
//         error: state.error,
//         status: state.status,
//         isOptimistic: state.isOptimistic,
//         mutate: (variables: TVariables) => mutation.mutate(variables),
//         mutateAsync: (variables: TVariables) => mutation.mutateAsync(variables),
//         reset: () => {
//           updateState({
//             data: null,
//             loading: false,
//             error: null,
//             status: 'idle',
//             lastMutation: null,
//             mutationCount: 0,
//             retryCount: 0,
//             isOptimistic: false
//           });
//         },
//         cancel: () => {
//           if (abortController) {
//             abortController.abort();
//           }
//         }
//       };
//     },
    
//     reset(): void {
//       updateState({
//         data: null,
//         loading: false,
//         error: null,
//         status: 'idle',
//         lastMutation: null,
//         mutationCount: 0,
//         retryCount: 0,
//         isOptimistic: false
//       });
//     },
    
//     updateOptions(newOptions: Partial<MutationOptions<TData, TVariables, TError>>): void {
//       Object.assign(finalOptions, newOptions);
//     }
//   };
  
//   return mutation;
// };

// // ===== UTILITY FUNCTIONS =====

// /**
//  * Create a simple POST mutation
//  */
// export const createPostMutation = <TData, TVariables>(
//   endpoint: string,
//   options?: Partial<MutationOptions<TData, TVariables>>
// ): Mutation<TData, TVariables> => {
//   return createMutation({
//     mutationFn: (variables: TVariables) => apiClient.post(endpoint, variables),
//     ...options
//   });
// };

// /**
//  * Create a simple PUT mutation
//  */
// export const createPutMutation = <TData, TVariables>(
//   endpoint: string,
//   options?: Partial<MutationOptions<TData, TVariables>>
// ): Mutation<TData, TVariables> => {
//   return createMutation({
//     mutationFn: (variables: TVariables) => apiClient.put(endpoint, variables),
//     ...options
//   });
// };

// /**
//  * Create a simple PATCH mutation
//  */
// export const createPatchMutation = <TData, TVariables>(
//   endpoint: string,
//   options?: Partial<MutationOptions<TData, TVariables>>
// ): Mutation<TData, TVariables> => {
//   return createMutation({
//     mutationFn: (variables: TVariables) => apiClient.patch(endpoint, variables),
//     ...options
//   });
// };

// /**
//  * Create a simple DELETE mutation
//  */
// export const createDeleteMutation = <TData = any>(
//   endpoint: string,
//   options?: Partial<MutationOptions<TData, void>>
// ): Mutation<TData, void> => {
//   return createMutation({
//     mutationFn: () => apiClient.delete(endpoint),
//     ...options
//   });
// };

// /**
//  * Clear all pending mutations
//  */
// export const clearPendingMutations = (): void => {
//   pendingMutations.clear();
// };

// /**
//  * Get mutation stats
//  */
// export const getMutationStats = () => {
//   return {
//     pendingMutations: pendingMutations.size,
//     subscribers: Array.from(mutationSubscribers.values()).reduce((acc, set) => acc + set.size, 0)
//   };
// };

// // ===== EXPORTS =====

// export type {
//   MutationState,
//   MutationFunction,
//   MutationOptions,
//   MutationResult,
//   Mutation
// };

/**
 * Development Notes:
 * 
 * 1. Mutation Features:
 *    - Optimistic updates with rollback
 *    - Automatic retry logic
 *    - Request deduplication
 *    - Cache invalidation
 *    - Loading state management
 * 
 * 2. Error Handling:
 *    - Comprehensive error transformation
 *    - Retry strategies
 *    - Graceful degradation
 *    - Error callbacks
 * 
 * 3. Performance:
 *    - Request deduplication
 *    - Optimistic updates
 *    - Efficient state management
 *    - Memory cleanup
 * 
 * 4. Developer Experience:
 *    - TypeScript support
 *    - Flexible configuration
 *    - React hook integration
 *    - Debug utilities
 * 
 * 5. State Management:
 *    - Reactive state updates
 *    - Subscriber pattern
 *    - Status tracking
 *    - Mutation history
 * 
 * 6. Cache Integration:
 *    - Automatic cache invalidation
 *    - Query pattern matching
 *    - Optimistic cache updates
 *    - Cache rollback support
 * 
 * Usage Examples:
 * ```tsx
 * // Basic mutation
 * const createCourseMutation = createPostMutation<Course, CreateCourseData>(
 *   '/courses',
 *   {
 *     onSuccess: (course) => {
 *       invalidateQueries('courses');
 *       showNotification('Course created!');
 *     },
 *     onError: (error) => {
 *       showNotification('Failed to create course', 'error');
 *     }
 *   }
 * );
 * 
 * // Optimistic mutation
 * const updateCourseMutation = createMutation<Course, UpdateCourseData>({
 *   mutationFn: (data) => apiClient.put(`/courses/${data.id}`, data),
 *   optimisticUpdates: true,
 *   optimisticUpdate: (data) => ({ ...currentCourse, ...data }),
 *   rollback: () => {
 *     // Rollback optimistic update
 *   },
 *   invalidateQueries: ['courses', /^course-/]
 * });
 * 
 * // Use in React component
 * const CreateCourseForm = () => {
 *   const { mutate, loading, error } = createCourseMutation.use();
 *   
 *   const handleSubmit = (data: CreateCourseData) => {
 *     mutate(data);
 *   };
 *   
 *   return (
 *     <form onSubmit={handleSubmit}>
 *       <button disabled={loading}>Create Course</button>
 *       {error && <div>Error: {error.message}</div>}
 *     </form>
 *   );
 * };
 * ```
 */