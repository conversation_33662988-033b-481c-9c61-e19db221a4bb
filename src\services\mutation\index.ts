/**
 * Mutation Services Index
 * 
 * This file serves as a central export hub for all mutation-related modules.
 * It provides a unified interface for data modification operations, optimistic updates,
 * and cache invalidation across the application.
 * 
 * Key Concepts:
 * - Centralized mutation exports
 * - Pre-configured mutations for common operations
 * - Utility functions for mutation management
 * - Type-safe mutation interfaces
 * - Cache invalidation helpers
 * - Optimistic update utilities
 * 
 * Usage Examples:
 * ```tsx
 * // Import specific mutations
 * import { createCourseMutation, updateUserMutation } from '@/services/mutation';
 * 
 * // Import mutation utilities
 * import { createMutation, clearPendingMutations } from '@/services/mutation';
 * 
 * // Import types
 * import type { MutationOptions, MutationResult } from '@/services/mutation';
 * 
 * // Use pre-configured mutations
 * const { mutate, loading, error } = createCourseMutation.use();
 * 
 * // Create custom mutations
 * const customMutation = createMutation({
 *   mutationFn: (data) => apiClient.post('/custom', data),
 *   onSuccess: () => invalidateQueries('custom')
 * });
 * ```
 */

// ===== BASE MUTATION EXPORTS =====

// export {
//   createMutation,
//   createPostMutation,
//   createPutMutation,
//   createPatchMutation,
//   createDeleteMutation,
//   clearPendingMutations,
//   getMutationStats
// } from './base';

// export type {
//   MutationState,
//   MutationFunction,
//   MutationOptions,
//   MutationResult,
//   Mutation
// } from './base';

// // ===== IMPORTS FOR PRE-CONFIGURED MUTATIONS =====

// import { createMutation, createPostMutation, createPutMutation, createPatchMutation, createDeleteMutation } from './base';
// import { invalidateQueries } from '../fetcher/base';
// import type { Course, CreateCourseData, UpdateCourseData } from '../../interfaces/course';
// import type { User, CreateUserData, UpdateUserData } from '../../interfaces/user';

// // ===== PRE-CONFIGURED COURSE MUTATIONS =====

// /**
//  * Create course mutation
//  */
// export const createCourseMutation = createPostMutation<Course, CreateCourseData>(
//   '/courses',
//   {
//     onSuccess: (course) => {
//       // Invalidate course-related queries
//       invalidateQueries('courses');
//       invalidateQueries('course-list');
//       invalidateQueries('course-search');
      
//       console.log('Course created successfully:', course.title);
//     },
//     onError: (error) => {
//       console.error('Failed to create course:', error);
//     },
//     invalidateQueries: ['courses', 'course-list', 'course-search'],
//     retryCount: 2,
//     retryDelay: 1000
//   }
// );

// /**
//  * Update course mutation
//  */
// export const updateCourseMutation = createMutation<Course, UpdateCourseData & { id: string }>({
//   mutationFn: (data) => {
//     const { id, ...updateData } = data;
//     return fetch(`/api/courses/${id}`, {
//       method: 'PUT',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(updateData)
//     }).then(res => res.json());
//   },
//   onSuccess: (course, variables) => {
//     // Invalidate specific course and related queries
//     invalidateQueries(`course-${variables.id}`);
//     invalidateQueries('courses');
//     invalidateQueries('course-list');
    
//     console.log('Course updated successfully:', course.title);
//   },
//   onError: (error) => {
//     console.error('Failed to update course:', error);
//   },
//   optimisticUpdates: true,
//   optimisticUpdate: (variables) => {
//     // Return optimistic course data
//     return {
//       id: variables.id,
//       ...variables,
//       updatedAt: new Date().toISOString()
//     } as Course;
//   },
//   invalidateQueries: [/^course-/, 'courses', 'course-list'],
//   retryCount: 2
// });

// /**
//  * Delete course mutation
//  */
// export const deleteCourseMutation = createMutation<{ success: boolean }, { id: string }>({
//   mutationFn: (variables) => {
//     return fetch(`/api/courses/${variables.id}`, {
//       method: 'DELETE'
//     }).then(res => res.json());
//   },
//   onSuccess: (result, variables) => {
//     // Invalidate course-related queries
//     invalidateQueries(`course-${variables.id}`);
//     invalidateQueries('courses');
//     invalidateQueries('course-list');
//     invalidateQueries('course-search');
    
//     console.log('Course deleted successfully');
//   },
//   onError: (error) => {
//     console.error('Failed to delete course:', error);
//   },
//   invalidateQueries: [/^course-/, 'courses', 'course-list', 'course-search'],
//   retryCount: 1
// });

// /**
//  * Enroll in course mutation
//  */
// export const enrollCourseMutation = createPostMutation<
//   { enrollment: any; course: Course },
//   { courseId: string; userId: string }
// >(
//   '/enrollments',
//   {
//     mutationFn: (variables) => {
//       return fetch('/api/enrollments', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify(variables)
//       }).then(res => res.json());
//     },
//     onSuccess: (result, variables) => {
//       // Invalidate enrollment and course queries
//       invalidateQueries(`course-${variables.courseId}`);
//       invalidateQueries(`user-${variables.userId}-enrollments`);
//       invalidateQueries('user-courses');
//       invalidateQueries('enrollments');
      
//       console.log('Enrolled in course successfully');
//     },
//     onError: (error) => {
//       console.error('Failed to enroll in course:', error);
//     },
//     invalidateQueries: [/^course-/, /^user-.*-enrollments$/, 'user-courses', 'enrollments'],
//     retryCount: 2
//   }
// );

// // ===== PRE-CONFIGURED USER MUTATIONS =====

// /**
//  * Create user mutation
//  */
// export const createUserMutation = createPostMutation<User, CreateUserData>(
//   '/users',
//   {
//     onSuccess: (user) => {
//       // Invalidate user-related queries
//       invalidateQueries('users');
//       invalidateQueries('user-list');
//       invalidateQueries('user-search');
      
//       console.log('User created successfully:', user.email);
//     },
//     onError: (error) => {
//       console.error('Failed to create user:', error);
//     },
//     invalidateQueries: ['users', 'user-list', 'user-search'],
//     retryCount: 2
//   }
// );

// /**
//  * Update user mutation
//  */
// export const updateUserMutation = createMutation<User, UpdateUserData & { id: string }>({
//   mutationFn: (data) => {
//     const { id, ...updateData } = data;
//     return fetch(`/api/users/${id}`, {
//       method: 'PUT',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(updateData)
//     }).then(res => res.json());
//   },
//   onSuccess: (user, variables) => {
//     // Invalidate specific user and related queries
//     invalidateQueries(`user-${variables.id}`);
//     invalidateQueries('users');
//     invalidateQueries('user-list');
    
//     console.log('User updated successfully:', user.email);
//   },
//   onError: (error) => {
//     console.error('Failed to update user:', error);
//   },
//   optimisticUpdates: true,
//   optimisticUpdate: (variables) => {
//     // Return optimistic user data
//     return {
//       id: variables.id,
//       ...variables,
//       updatedAt: new Date().toISOString()
//     } as User;
//   },
//   invalidateQueries: [/^user-/, 'users', 'user-list'],
//   retryCount: 2
// });

// /**
//  * Update user profile mutation
//  */
// export const updateUserProfileMutation = createMutation<User, Partial<User['profile']> & { userId: string }>({
//   mutationFn: (data) => {
//     const { userId, ...profileData } = data;
//     return fetch(`/api/users/${userId}/profile`, {
//       method: 'PATCH',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(profileData)
//     }).then(res => res.json());
//   },
//   onSuccess: (user, variables) => {
//     // Invalidate user profile queries
//     invalidateQueries(`user-${variables.userId}`);
//     invalidateQueries(`user-${variables.userId}-profile`);
//     invalidateQueries('current-user');
    
//     console.log('User profile updated successfully');
//   },
//   onError: (error) => {
//     console.error('Failed to update user profile:', error);
//   },
//   optimisticUpdates: true,
//   optimisticUpdate: (variables) => {
//     // Return optimistic user data with updated profile
//     return {
//       id: variables.userId,
//       profile: variables,
//       updatedAt: new Date().toISOString()
//     } as User;
//   },
//   invalidateQueries: [/^user-.*-profile$/, 'current-user'],
//   retryCount: 2
// });

// /**
//  * Delete user mutation
//  */
// export const deleteUserMutation = createMutation<{ success: boolean }, { id: string }>({
//   mutationFn: (variables) => {
//     return fetch(`/api/users/${variables.id}`, {
//       method: 'DELETE'
//     }).then(res => res.json());
//   },
//   onSuccess: (result, variables) => {
//     // Invalidate user-related queries
//     invalidateQueries(`user-${variables.id}`);
//     invalidateQueries('users');
//     invalidateQueries('user-list');
//     invalidateQueries('user-search');
    
//     console.log('User deleted successfully');
//   },
//   onError: (error) => {
//     console.error('Failed to delete user:', error);
//   },
//   invalidateQueries: [/^user-/, 'users', 'user-list', 'user-search'],
//   retryCount: 1
// });

// // ===== AUTHENTICATION MUTATIONS =====

// /**
//  * Login mutation
//  */
// export const loginMutation = createPostMutation<
//   { user: User; token: string },
//   { email: string; password: string }
// >(
//   '/auth/login',
//   {
//     onSuccess: (result) => {
//       // Store token and invalidate auth queries
//       localStorage.setItem('auth_token', result.token);
//       invalidateQueries('current-user');
//       invalidateQueries('auth-status');
      
//       console.log('Login successful:', result.user.email);
//     },
//     onError: (error) => {
//       console.error('Login failed:', error);
//     },
//     invalidateQueries: ['current-user', 'auth-status'],
//     retryCount: 1
//   }
// );

// /**
//  * Logout mutation
//  */
// export const logoutMutation = createPostMutation<{ success: boolean }, void>(
//   '/auth/logout',
//   {
//     onSuccess: () => {
//       // Clear token and invalidate all queries
//       localStorage.removeItem('auth_token');
//       invalidateQueries(/.*/);
      
//       console.log('Logout successful');
//     },
//     onError: (error) => {
//       console.error('Logout failed:', error);
//     },
//     invalidateQueries: [/.*/],
//     retryCount: 1
//   }
// );

// /**
//  * Register mutation
//  */
// export const registerMutation = createPostMutation<
//   { user: User; token: string },
//   { email: string; password: string; firstName: string; lastName: string }
// >(
//   '/auth/register',
//   {
//     onSuccess: (result) => {
//       // Store token and invalidate auth queries
//       localStorage.setItem('auth_token', result.token);
//       invalidateQueries('current-user');
//       invalidateQueries('auth-status');
//       invalidateQueries('users');
      
//       console.log('Registration successful:', result.user.email);
//     },
//     onError: (error) => {
//       console.error('Registration failed:', error);
//     },
//     invalidateQueries: ['current-user', 'auth-status', 'users'],
//     retryCount: 1
//   }
// );

// // ===== UTILITY FUNCTIONS =====

// /**
//  * Invalidate all course-related queries
//  */
// export const invalidateCourseQueries = (): void => {
//   invalidateQueries('courses');
//   invalidateQueries('course-list');
//   invalidateQueries('course-search');
//   invalidateQueries(/^course-/);
// };

// /**
//  * Invalidate all user-related queries
//  */
// export const invalidateUserQueries = (): void => {
//   invalidateQueries('users');
//   invalidateQueries('user-list');
//   invalidateQueries('user-search');
//   invalidateQueries(/^user-/);
// };

// /**
//  * Invalidate all authentication-related queries
//  */
// export const invalidateAuthQueries = (): void => {
//   invalidateQueries('current-user');
//   invalidateQueries('auth-status');
//   invalidateQueries(/^auth-/);
// };

// /**
//  * Invalidate all queries (nuclear option)
//  */
// export const invalidateAllQueries = (): void => {
//   invalidateQueries(/.*/); // Matches all query keys
// };

// /**
//  * Mutation collections for easy access
//  */
// export const courseMutations = {
//   create: createCourseMutation,
//   update: updateCourseMutation,
//   delete: deleteCourseMutation,
//   enroll: enrollCourseMutation
// } as const;

// export const userMutations = {
//   create: createUserMutation,
//   update: updateUserMutation,
//   updateProfile: updateUserProfileMutation,
//   delete: deleteUserMutation
// } as const;

// export const authMutations = {
//   login: loginMutation,
//   logout: logoutMutation,
//   register: registerMutation
// } as const;

// /**
//  * All mutations grouped by domain
//  */
// export const mutations = {
//   course: courseMutations,
//   user: userMutations,
//   auth: authMutations
// } as const;

// /**
//  * Mutation metadata for debugging and monitoring
//  */
// export const mutationMetadata = {
//   course: {
//     create: { endpoint: '/courses', method: 'POST' },
//     update: { endpoint: '/courses/:id', method: 'PUT' },
//     delete: { endpoint: '/courses/:id', method: 'DELETE' },
//     enroll: { endpoint: '/enrollments', method: 'POST' }
//   },
//   user: {
//     create: { endpoint: '/users', method: 'POST' },
//     update: { endpoint: '/users/:id', method: 'PUT' },
//     updateProfile: { endpoint: '/users/:id/profile', method: 'PATCH' },
//     delete: { endpoint: '/users/:id', method: 'DELETE' }
//   },
//   auth: {
//     login: { endpoint: '/auth/login', method: 'POST' },
//     logout: { endpoint: '/auth/logout', method: 'POST' },
//     register: { endpoint: '/auth/register', method: 'POST' }
//   }
// } as const;

/**
 * Development Notes:
 * 
 * 1. Export Strategy:
 *    - Re-export all base mutation utilities
 *    - Provide pre-configured mutations for common operations
 *    - Group mutations by domain (course, user, auth)
 *    - Include utility functions for cache invalidation
 * 
 * 2. Pre-configured Mutations:
 *    - Course CRUD operations
 *    - User management
 *    - Authentication flows
 *    - Enrollment operations
 *    - Profile updates
 * 
 * 3. Cache Invalidation:
 *    - Automatic query invalidation on success
 *    - Pattern-based invalidation
 *    - Domain-specific invalidation utilities
 *    - Nuclear option for complete cache clear
 * 
 * 4. Optimistic Updates:
 *    - Enabled for update operations
 *    - Proper rollback mechanisms
 *    - Type-safe optimistic data
 *    - User experience improvements
 * 
 * 5. Error Handling:
 *    - Consistent error logging
 *    - Retry strategies
 *    - User-friendly error messages
 *    - Development debugging
 * 
 * 6. Type Safety:
 *    - Full TypeScript support
 *    - Proper generic constraints
 *    - Interface compliance
 *    - Runtime type checking
 * 
 * Usage Examples:
 * ```tsx
 * // Use pre-configured mutations
 * import { createCourseMutation, updateUserMutation } from '@/services/mutation';
 * 
 * const CreateCourseForm = () => {
 *   const { mutate, loading, error } = createCourseMutation.use();
 *   
 *   const handleSubmit = (data: CreateCourseData) => {
 *     mutate(data);
 *   };
 *   
 *   return (
 *     <form onSubmit={handleSubmit}>
 *       <button disabled={loading}>Create Course</button>
 *       {error && <div>Error: {error.message}</div>}
 *     </form>
 *   );
 * };
 * 
 * // Use mutation collections
 * import { mutations } from '@/services/mutation';
 * 
 * const CourseManager = () => {
 *   const createCourse = mutations.course.create.use();
 *   const updateCourse = mutations.course.update.use();
 *   const deleteCourse = mutations.course.delete.use();
 *   
 *   // Use mutations...
 * };
 * 
 * // Custom mutations
 * import { createMutation } from '@/services/mutation';
 * 
 * const customMutation = createMutation({
 *   mutationFn: (data) => apiClient.post('/custom', data),
 *   onSuccess: () => {
 *     invalidateQueries('custom');
 *   }
 * });
 * ```
 */