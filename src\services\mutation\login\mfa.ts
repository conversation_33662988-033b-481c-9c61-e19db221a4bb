import {
  ICheckMfaRequirementRequest,
  IEnrollMfaRequest,
  IVerifyMfaSetupRequest,
  IVerifyMfaRequest,
  ISendOtpRequest,
  IVerifyOtpRequest,
} from "@/interfaces/user/mfa";
import {
  apiCheckMfaRequirement,
  apiEnrollMfa,
  apiVerifyMfaSetup,
  apiVerifyMfa,
  apiSendOtp,
  apiVerifyOtp,
} from "@/services/api/login/mfa";
import { useMutation } from "@tanstack/react-query";

// MFA: Check Requirement
export const useCheckMfaRequirementMutation = () => {
  return useMutation({
    mutationFn: async (body: ICheckMfaRequirementRequest) => {
      return await apiCheckMfaRequirement(body);
    },
  });
};

// MFA: Enroll
export const useEnrollMfaMutation = () => {
  return useMutation({
    mutationFn: async (body: IEnrollMfaRequest) => {
      return await apiEnrollMfa(body);
    },
  });
};

// MFA: Verify Setup
export const useVerifyMfaSetupMutation = () => {
  return useMutation({
    mutationFn: async (body: IVerifyMfaSetupRequest) => {
      return await apiVerifyMfaSetup(body);
    },
  });
};

// MFA: Verify
export const useVerifyMfaMutation = () => {
  return useMutation({
    mutationFn: async (body: IVerifyMfaRequest) => {
      return await apiVerifyMfa(body);
    },
  });
};

// OTP: Send
export const useSendOtpMutation = () => {
  return useMutation({
    mutationFn: async (body: ISendOtpRequest) => {
      return await apiSendOtp(body);
    },
  });
};

// OTP: Verify
export const useVerifyOtpMutation = () => {
  return useMutation({
    mutationFn: async (body: IVerifyOtpRequest) => {
      return await apiVerifyOtp(body);
    },
  });
};
