import {
  ILoginUserRequest,
  IUpdatePasswordRequest,
} from "@/interfaces/user/login";
import {
  apiLoginUser,
  apiLogoutUser,
  apiUpdatePassword,
  apiVerifyUser,
} from "@/services/api/login/user";
import { useMutation } from "@tanstack/react-query";

// Verify User
export const useVerifyUserMutation = () => {
  return useMutation({
    mutationFn: async (body: { username: string }) => {
      return await apiVerifyUser(body);
    },
  });
};

// Update Password
export const useUpdatePasswordMutation = () => {
  return useMutation({
    mutationFn: async ({
      body,
      userId,
    }: {
      body: IUpdatePasswordRequest;
      userId: number;
    }) => {
      return await apiUpdatePassword(body, userId);
    },
  });
};

// Login User
export const useLoginUserMutation = () => {
  return useMutation({
    mutationFn: async (body: ILoginUserRequest) => {
      return await apiLoginUser(body);
    },
  });
};

export const useLogoutUserMutation = () => {
  return useMutation({
    mutationFn: async () => {
      return await apiLogoutUser();
    },
  });
};
