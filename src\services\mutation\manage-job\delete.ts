import {
  IUpdateJobPositionBody,
  IUpdateJobPositionParams,
} from "@/interfaces/admin/manage-job/update";
import { useMutation } from "@tanstack/react-query";
import { apiUpdateJobPosition } from "@/services/api/manage-job/update";

export const useDeleteJobPositionMutation = () => {
  return useMutation({
    mutationKey: ["delete-job-position"],
    mutationFn: async ({ params }: { params: IUpdateJobPositionParams }) => {
      const data: IUpdateJobPositionBody = {
        is_deleted: true,
      };

      return await apiUpdateJobPosition(params, data);
    },
  });
};
