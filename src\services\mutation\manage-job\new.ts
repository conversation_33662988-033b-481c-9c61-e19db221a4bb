import { IGetJobPositionDepartmentListResponse } from "@/interfaces/admin/manage-job/department";
import { IGetJobPositionFunctionListResponse } from "@/interfaces/admin/manage-job/function";
import {
  ICreateJobPositionBody,
  ICreateJobPositionForm,
} from "@/interfaces/admin/manage-job/new";
import { IGetJobPositionStartingLevelListResponse } from "@/interfaces/admin/manage-job/starting-level";
import { apiCreateJobPosition } from "@/services/api/manage-job/new";
import { useMutation } from "@tanstack/react-query";

export const useCreateNewJobPositionMutation = () => {
  return useMutation({
    mutationKey: ["new-job-position"],
    mutationFn: async ({
      form,
      departments,
      job_functions,
      levels,
    }: {
      form: ICreateJobPositionForm;
      departments: IGetJobPositionDepartmentListResponse[];
      job_functions: IGetJobPositionFunctionListResponse[];
      levels: IGetJobPositionStartingLevelListResponse[];
    }) => {
      const data: ICreateJobPositionBody = {
        job_id: form.id,
        department_id: form.department_id?.toString(),
        job_name: form.name,
        entity_id: form.entity_id,
        is_active: true,
        is_need_neop: form.is_neop,
        is_need_welcoming_kit: form.welcoming_kit,
        job_function_id: form.job_function_id,
        level_id: form.starting_level_id,
        department_name: form.department_id
          ? departments.find(
              (item) => item.id === form.department_id?.toString()
            )?.department_name ?? undefined
          : undefined,
        job_function: form.job_function_id
          ? job_functions.find((item) => item.id === form.job_function_id)
              ?.function_name ?? undefined
          : undefined,
        job_position_type: form.position_type,
        starter_module_priority: form.starter_module_priority?.toString(),
        level: form.starting_level_id
          ? levels.find((item) => item.id === form.starting_level_id)?.level ??
            undefined
          : undefined,
        level_name: form.starting_level_id
          ? levels.find((item) => item.id === form.starting_level_id)?.name ??
            undefined
          : undefined,
      };

      console.log(data);

      return await apiCreateJobPosition(data);
    },
  });
};
