import {
  IUpdateJobPositionBody,
  IUpdateJobPositionParams,
} from "@/interfaces/admin/manage-job/update";
import { useMutation } from "@tanstack/react-query";
import { apiUpdateJobPosition } from "@/services/api/manage-job/update";

export const useSetActiveJobPositionMutation = () => {
  return useMutation({
    mutationKey: ["set-active-job-position"],
    mutationFn: async ({
      params,
      is_active,
    }: {
      params: IUpdateJobPositionParams;
      is_active: boolean;
    }) => {
      const data: IUpdateJobPositionBody = {
        is_active,
      };

      return await apiUpdateJobPosition(params, data);
    },
  });
};
