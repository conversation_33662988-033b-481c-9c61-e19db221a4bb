import { IUpdateSliderBody } from "@/interfaces/admin/manage-slider/update";
import { apiUpdateSlider } from "@/services/api/manage-slider/update";
import { useMutation } from "@tanstack/react-query";

export const useDeleteSliderMutation = () => {
  return useMutation({
    mutationKey: ["delete-slider"],
    mutationFn: async ({
      id,
      slider_name,
    }: {
      id: number;
      slider_name: string;
    }) => {
      const data: IUpdateSliderBody = {
        is_deleted: true,
        id,
        slider_name,
      };

      return await apiUpdateSlider(data);
    },
  });
};
