import {
  ICreateSliderBody,
  ICreateSliderForm,
} from "@/interfaces/admin/manage-slider/new";
import { apiCreateSlider } from "@/services/api/manage-slider/new";
import { useMutation } from "@tanstack/react-query";

export const useCreateSliderMutation = () => {
  return useMutation({
    mutationKey: ["create-slider"],
    mutationFn: async (body: ICreateSliderForm) => {
      const data: ICreateSliderBody = {
        link: body.link,
        slider_desktop: body.slider_desktop as File,
        slider_mobile: body.slider_mobile as File,
        slider_name: body.slider_name,
      };

      return await apiCreateSlider(data);
    },
  });
};
