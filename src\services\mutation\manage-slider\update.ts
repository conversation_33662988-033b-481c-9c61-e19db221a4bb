import { ICreateSliderForm } from "@/interfaces/admin/manage-slider/new";
import { IUpdateSliderBody } from "@/interfaces/admin/manage-slider/update";
import { apiUpdateSlider } from "@/services/api/manage-slider/update";
import { useMutation } from "@tanstack/react-query";

export const useUpdateSliderMutation = () => {
  return useMutation({
    mutationKey: ["update-slider"],
    mutationFn: async ({
      id,
      body,
    }: {
      id: number;
      body: ICreateSliderForm;
    }) => {
      const data: IUpdateSliderBody = {
        link: body.link,
        slider_desktop: body.slider_desktop as File,
        slider_mobile: body.slider_mobile as File,
        slider_name: body.slider_name,
        id,
      };

      return await apiUpdateSlider(data);
    },
  });
};
