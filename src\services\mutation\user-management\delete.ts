import { IUpdateUserBody } from "@/interfaces/admin/user-management/update";
import { apiUpdateUser } from "@/services/api/user-management/update";
import { useMutation } from "@tanstack/react-query";

export const useDeleteUserMutation = () => {
  return useMutation({
    mutationKey: ["delete-user"],
    mutationFn: async ({ id }: { id: number }) => {
      const data: IUpdateUserBody = {
        is_deleted: true,
      };

      return await apiUpdateUser(id, null, data);
    },
  });
};
