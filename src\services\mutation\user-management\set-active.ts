import { IUpdateUserBody } from "@/interfaces/admin/user-management/update";
import { apiUpdateUser } from "@/services/api/user-management/update";
import { useMutation } from "@tanstack/react-query";

export const useSetUserActiveMutation = () => {
  return useMutation({
    mutationKey: ["update-user"],
    mutationFn: async ({
      id,
      value: boolean,
    }: {
      id: number;
      value: boolean;
    }) => {
      const data: IUpdateUserBody = {
        is_active: boolean,
      };

      return await apiUpdateUser(id, null, data);
    },
  });
};
