import { IGetForumTitleResponse } from "@/interfaces/admin/user-management/forum-title";
import { IGetJobPositionResponse } from "@/interfaces/admin/user-management/job-position";
import { ICreateUserForm } from "@/interfaces/admin/user-management/new";
import { IUpdateUserBody } from "@/interfaces/admin/user-management/update";
import { IGetMasterUserListResponse } from "@/interfaces/admin/user-management/user-list";
import { apiUpdateUser } from "@/services/api/user-management/update";
import { useMutation } from "@tanstack/react-query";

export const useUpdateUserMutation = () => {
  return useMutation({
    mutationKey: ["update-user"],
    mutationFn: async ({
      id,
      form,
      forumTitles,
      jobs,
      userList,
    }: {
      id: number;
      form: ICreateUserForm;
      forumTitles: IGetForumTitleResponse[];
      jobs: IGetJobPositionResponse[];
      userList: IGetMasterUserListResponse[];
    }) => {
      const file = form.user_signature as File;
      const data: IUpdateUserBody = {
        npk: form.npk,
        email: form.email,
        second_email: form.second_email,
        point: null,
        forum_title:
          forumTitles.find(
            (forumTitle) => forumTitle.forum_id === form.forum_title_id
          )?.forum_name ?? "",
        name: form.name,
        job_name:
          jobs.find((job) => job.job_id === form.job_name_id)?.job_name ?? "",
        password: "",
        job_name_id: form.job_name_id,
        phone_number: form.phone_number,
        user_type_id: form.user_type_id,
        user_role_id: form.user_role_id,
        supervisor_id: form.supervisor_id,
        supervisor_name:
          userList.find((user) => user.user_id === form.supervisor_id)
            ?.user_name ?? "",
        supervisor_npk: "",
        is_active: form.is_active,
        is_need_neop: form.is_need_neop,
        is_new_user: true,
        is_deleted: false,
        created_by: "",
        updated_by: "",
        forum_title_id: form.forum_title_id,
        entity_id: form.entity_id,
        is_need_welcoming_kit: form.is_need_welcoming_kit,
      };
      console.log(data);
      return await apiUpdateUser(id, file, data);
    },
  });
};
