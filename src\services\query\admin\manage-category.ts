import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import {
  apiGetListCategory,
  apiGetListSubCategory,
} from "@/services/api/admin/manage-category";
import { useQuery } from "@tanstack/react-query";

export const useGetListCategoryQuery = (
  params?: IGetListCategoryQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["category", params],
    queryFn: async () => {
      return await apiGetListCategory(params);
    },
    enabled,
  });
};

export const useGetListSubCategoryQuery = (
  params?: IGetListSubCategoryQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["subcategory", params],
    queryFn: async () => {
      return await apiGetListSubCategory(params);
    },
    enabled,
  });
};
