import { useQuery } from "@tanstack/react-query";
import {
  IGetListFaqParams,
  IGetListTagParams,
} from "@/interfaces/admin/manage-faq/list";
import {
  apiDetailFaq,
  apiGetListFaq,
  apiGetListTag,
} from "@/services/api/admin/manage-faq";

export const faqQueryKeys = {
  list: (params?: IGetListFaqParams) => ["faq", "list", params],
  detail: (id: string) => ["faq", "detail", id],
};

export const tagQueryKeys = {
  list: (params: IGetListTagParams) => ["tag", "list", params],
};

export const useGetListFaqQuery = (params?: IGetListFaqParams) => {
  return useQuery({
    queryKey: faqQueryKeys.list(params),
    queryFn: async () => {
      return await apiGetListFaq(params);
    },
  });
};

export const useGetListTagQuery = (params: IGetListTagParams) => {
  return useQuery({
    queryKey: tagQueryKeys.list(params),
    queryFn: async () => {
      return await apiGetListTag(params);
    },
  });
};

export const useDetailFaqQuery = (id: string) => {
  return useQuery({
    queryKey: faqQueryKeys.detail(id),
    queryFn: async () => {
      return await apiDetailFaq(id);
    },
    enabled: !!id,
  });
};
