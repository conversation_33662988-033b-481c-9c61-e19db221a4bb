import { IGetListUserLogLoginQuery } from "@/interfaces/admin/user-log/list";
import { apiGetListUserLogLogin } from "@/services/api/admin/user-log-login";
import { useQuery } from "@tanstack/react-query";

export const useGetListUserLogLoginQuery = (
  params?: IGetListUserLogLoginQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["user-log-login", params],
    queryFn: async () => {
      return await apiGetListUserLogLogin(params);
    },
    enabled,
  });
};
