/**
 * Base Fetcher
 *
 * This file provides a foundational data fetching utility that wraps the API client
 * with additional features like caching, deduplication, and error handling.
 * It serves as the base for more specific fetchers and query hooks.
 *
 * Key Concepts:
 * - Request deduplication
 * - Response caching
 * - Error handling and retries
 * - Loading state management
 * - Optimistic updates
 * - Background refetching
 *
 * Usage Examples:
 * ```tsx
 * // Import base fetcher
 * import { createFetcher, FetcherOptions } from '@/services/fetcher/base';
 *
 * // Create a course fetcher
 * const courseFetcher = createFetcher<Course[]>('courses', {
 *   endpoint: '/courses',
 *   cacheTime: 5 * 60 * 1000, // 5 minutes
 *   staleTime: 1 * 60 * 1000   // 1 minute
 * });
 *
 * // Use in component
 * const { data, loading, error, ref=nulletch } = courseFetcher.use();
 * ```
 */

// import { apiClient, type ApiResponse, type ApiError } from '../config/api';
// import { isDevelopment } from '../config/env';

// // ===== FETCHER TYPES =====

// /**
//  * Fetcher state
//  */
// export interface FetcherState<T> {
//   /** Response data */
//   data: T | null;

//   /** Loading state */
//   loading: boolean;

//   /** Error state */
//   error: ApiError | null;

//   /** Last fetch timestamp */
//   lastFetch: number | null;

//   /** Cache timestamp */
//   cacheTime: number | null;

//   /** Is data stale */
//   isStale: boolean;

//   /** Is data cached */
//   isCached: boolean;

//   /** Fetch count */
//   fetchCount: number;

//   /** Retry count */
//   retryCount: number;
// }

// /**
//  * Fetcher options
//  */
// export interface FetcherOptions<T> {
//   /** API endpoint */
//   endpoint: string;

//   /** HTTP method */
//   method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

//   /** Request parameters */
//   params?: Record<string, any>;

//   /** Request body data */
//   data?: any;

//   /** Cache time in milliseconds */
//   cacheTime?: number;

//   /** Stale time in milliseconds */
//   staleTime?: number;

//   /** Enable request deduplication */
//   dedupe?: boolean;

//   /** Enable background refetching */
//   refetchOnWindowFocus?: boolean;

//   /** Enable refetch on reconnect */
//   refetchOnReconnect?: boolean;

//   /** Retry count */
//   retryCount?: number;

//   /** Retry delay in milliseconds */
//   retryDelay?: number;

//   /** Enable optimistic updates */
//   optimisticUpdates?: boolean;

//   /** Data transformer */
//   transform?: (data: any) => T;

//   /** Error transformer */
//   transformError?: (error: ApiError) => ApiError;

//   /** Success callback */
//   onSuccess?: (data: T) => void;

//   /** Error callback */
//   onError?: (error: ApiError) => void;

//   /** Loading callback */
//   onLoading?: (loading: boolean) => void;

//   /** Cache key generator */
//   getCacheKey?: (options: FetcherOptions<T>) => string;

//   /** Should refetch predicate */
//   shouldRefetch?: (state: FetcherState<T>) => boolean;

//   /** Initial data */
//   initialData?: T;

//   /** Enable polling */
//   polling?: {
//     interval: number;
//     enabled: boolean;
//   };
// }

// /**
//  * Fetcher result
//  */
// export interface FetcherResult<T> {
//   /** Response data */
//   data: T | null;

//   /** Loading state */
//   loading: boolean;

//   /** Error state */
//   error: ApiError | null;

//   /** Is data stale */
//   isStale: boolean;

//   /** Is data cached */
//   isCached: boolean;

//   /** Refetch function */
//   refetch: () => Promise<T | null>;

//   /** Mutate function for optimistic updates */
//   mutate: (data: T | ((prev: T | null) => T)) => void;

//   /** Invalidate cache */
//   invalidate: () => void;

//   /** Reset state */
//   reset: () => void;

//   /** Cancel ongoing request */
//   cancel: () => void;
// }

// /**
//  * Fetcher instance
//  */
// export interface Fetcher<T> {
//   /** Fetch data */
//   fetch: () => Promise<T | null>;

//   /** Get current state */
//   getState: () => FetcherState<T>;

//   /** Subscribe to state changes */
//   subscribe: (callback: (state: FetcherState<T>) => void) => () => void;

//   /** Use hook for React components */
//   use: () => FetcherResult<T>;

//   /** Invalidate cache */
//   invalidate: () => void;

//   /** Reset state */
//   reset: () => void;

//   /** Update options */
//   updateOptions: (options: Partial<FetcherOptions<T>>) => void;
// }

// // ===== CACHE MANAGEMENT =====

// /**
//  * Global cache store
//  */
// const cache = new Map<string, {
//   data: any;
//   timestamp: number;
//   cacheTime: number;
//   staleTime: number;
// }>();

// /**
//  * Pending requests store for deduplication
//  */
// const pendingRequests = new Map<string, Promise<any>>();

// /**
//  * Subscribers store
//  */
// const subscribers = new Map<string, Set<(state: any) => void>>();

// /**
//  * Generate cache key
//  */
// const generateCacheKey = <T>(options: FetcherOptions<T>): string => {
//   if (options.getCacheKey) {
//     return options.getCacheKey(options);
//   }

//   const { endpoint, method = 'GET', params, data } = options;
//   const paramsStr = params ? JSON.stringify(params) : '';
//   const dataStr = data ? JSON.stringify(data) : '';

//   return `${method}:${endpoint}:${paramsStr}:${dataStr}`;
// };

// /**
//  * Check if cache is valid
//  */
// const isCacheValid = (cacheKey: string): boolean => {
//   const cached = cache.get(cacheKey);
//   if (!cached) return false;

//   const now = Date.now();
//   return now - cached.timestamp < cached.cacheTime;
// };

// /**
//  * Check if data is stale
//  */
// const isDataStale = (cacheKey: string): boolean => {
//   const cached = cache.get(cacheKey);
//   if (!cached) return true;

//   const now = Date.now();
//   return now - cached.timestamp > cached.staleTime;
// };

// /**
//  * Get cached data
//  */
// const getCachedData = <T>(cacheKey: string): T | null => {
//   const cached = cache.get(cacheKey);
//   return cached ? cached.data : null;
// };

// /**
//  * Set cached data
//  */
// const setCachedData = <T>(
//   cacheKey: string,
//   data: T,
//   cacheTime: number,
//   staleTime: number
// ): void => {
//   cache.set(cacheKey, {
//     data,
//     timestamp: Date.now(),
//     cacheTime,
//     staleTime
//   });
// };

// /**
//  * Invalidate cache
//  */
// const invalidateCache = (cacheKey: string): void => {
//   cache.delete(cacheKey);
//   pendingRequests.delete(cacheKey);
// };

// /**
//  * Clear all cache
//  */
// export const clearCache = (): void => {
//   cache.clear();
//   pendingRequests.clear();
// };

// /**
//  * Get cache stats
//  */
// export const getCacheStats = () => {
//   return {
//     cacheSize: cache.size,
//     pendingRequests: pendingRequests.size,
//     subscribers: Array.from(subscribers.values()).reduce((acc, set) => acc + set.size, 0)
//   };
// };

// // ===== SUBSCRIPTION MANAGEMENT =====

// /**
//  * Subscribe to state changes
//  */
// const subscribe = <T>(
//   cacheKey: string,
//   callback: (state: FetcherState<T>) => void
// ): (() => void) => {
//   if (!subscribers.has(cacheKey)) {
//     subscribers.set(cacheKey, new Set());
//   }

//   const keySubscribers = subscribers.get(cacheKey)!;
//   keySubscribers.add(callback);

//   return () => {
//     keySubscribers.delete(callback);
//     if (keySubscribers.size === 0) {
//       subscribers.delete(cacheKey);
//     }
//   };
// };

// /**
//  * Notify subscribers
//  */
// const notifySubscribers = <T>(cacheKey: string, state: FetcherState<T>): void => {
//   const keySubscribers = subscribers.get(cacheKey);
//   if (keySubscribers) {
//     keySubscribers.forEach(callback => {
//       try {
//         callback(state);
//       } catch (error) {
//         if (isDevelopment) {
//           console.error('Error in fetcher subscriber:', error);
//         }
//       }
//     });
//   }
// };

// // ===== FETCHER IMPLEMENTATION =====

// /**
//  * Create fetcher instance
//  */
// export const createFetcher = <T>(
//   key: string,
//   options: FetcherOptions<T>
// ): Fetcher<T> => {
//   const cacheKey = `${key}:${generateCacheKey(options)}`;

//   // Default options
//   const defaultOptions: Required<Omit<FetcherOptions<T>, 'transform' | 'transformError' | 'onSuccess' | 'onError' | 'onLoading' | 'getCacheKey' | 'shouldRefetch' | 'initialData' | 'polling'>> = {
//     endpoint: options.endpoint,
//     method: 'GET',
//     params: {},
//     data: undefined,
//     cacheTime: 5 * 60 * 1000, // 5 minutes
//     staleTime: 1 * 60 * 1000, // 1 minute
//     dedupe: true,
//     refetchOnWindowFocus: true,
//     refetchOnReconnect: true,
//     retryCount: 3,
//     retryDelay: 1000,
//     optimisticUpdates: false
//   };

//   const finalOptions = { ...defaultOptions, ...options };

//   // State management
//   let state: FetcherState<T> = {
//     data: options.initialData || getCachedData<T>(cacheKey),
//     loading: false,
//     error: null,
//     lastFetch: null,
//     cacheTime: null,
//     isStale: isDataStale(cacheKey),
//     isCached: isCacheValid(cacheKey),
//     fetchCount: 0,
//     retryCount: 0
//   };

//   // Abort controller for cancellation
//   let abortController: AbortController | null = null;

//   // Polling timer
//   let pollingTimer: NodeJS.Timeout | null = null;

//   /**
//    * Update state and notify subscribers
//    */
//   const updateState = (updates: Partial<FetcherState<T>>): void => {
//     state = { ...state, ...updates };
//     notifySubscribers(cacheKey, state);

//     // Call callbacks
//     if (updates.loading !== undefined && finalOptions.onLoading) {
//       finalOptions.onLoading(updates.loading);
//     }
//   };

//   /**
//    * Execute fetch request
//    */
//   const executeFetch = async (): Promise<T | null> => {
//     try {
//       // Check for pending request (deduplication)
//       if (finalOptions.dedupe && pendingRequests.has(cacheKey)) {
//         return await pendingRequests.get(cacheKey);
//       }

//       // Create abort controller
//       abortController = new AbortController();

//       // Update loading state
//       updateState({ loading: true, error: null });

//       // Create request promise
//       const requestPromise = (async (): Promise<T | null> => {
//         const { endpoint, method, params, data } = finalOptions;

//         let response: ApiResponse<any>;

//         switch (method) {
//           case 'GET':
//             response = await apiClient.get(endpoint, {
//               params,
//               signal: abortController!.signal
//             });
//             break;
//           case 'POST':
//             response = await apiClient.post(endpoint, data, {
//               params,
//               signal: abortController!.signal
//             });
//             break;
//           case 'PUT':
//             response = await apiClient.put(endpoint, data, {
//               params,
//               signal: abortController!.signal
//             });
//             break;
//           case 'PATCH':
//             response = await apiClient.patch(endpoint, data, {
//               params,
//               signal: abortController!.signal
//             });
//             break;
//           case 'DELETE':
//             response = await apiClient.delete(endpoint, {
//               params,
//               signal: abortController!.signal
//             });
//             break;
//           default:
//             throw new Error(`Unsupported HTTP method: ${method}`);
//         }

//         // Transform response data
//         let responseData = response.data;
//         if (finalOptions.transform) {
//           responseData = finalOptions.transform(responseData);
//         }

//         // Cache the response
//         setCachedData(
//           cacheKey,
//           responseData,
//           finalOptions.cacheTime,
//           finalOptions.staleTime
//         );

//         // Update state
//         updateState({
//           data: responseData,
//           loading: false,
//           error: null,
//           lastFetch: Date.now(),
//           cacheTime: Date.now(),
//           isStale: false,
//           isCached: true,
//           fetchCount: state.fetchCount + 1,
//           retryCount: 0
//         });

//         // Call success callback
//         if (finalOptions.onSuccess) {
//           finalOptions.onSuccess(responseData);
//         }

//         return responseData;
//       })();

//       // Store pending request for deduplication
//       if (finalOptions.dedupe) {
//         pendingRequests.set(cacheKey, requestPromise);
//       }

//       const result = await requestPromise;

//       // Clean up pending request
//       pendingRequests.delete(cacheKey);

//       return result;

//     } catch (error) {
//       // Clean up pending request
//       pendingRequests.delete(cacheKey);

//       // Handle abort
//       if (error instanceof Error && error.name === 'AbortError') {
//         return null;
//       }

//       // Transform error
//       let apiError = error as ApiError;
//       if (finalOptions.transformError) {
//         apiError = finalOptions.transformError(apiError);
//       }

//       // Update state
//       updateState({
//         loading: false,
//         error: apiError,
//         retryCount: state.retryCount + 1
//       });

//       // Call error callback
//       if (finalOptions.onError) {
//         finalOptions.onError(apiError);
//       }

//       // Retry logic
//       if (state.retryCount < finalOptions.retryCount) {
//         await new Promise(resolve => setTimeout(resolve, finalOptions.retryDelay));
//         return executeFetch();
//       }

//       throw apiError;
//     }
//   };

//   /**
//    * Start polling
//    */
//   const startPolling = (): void => {
//     if (finalOptions.polling?.enabled && finalOptions.polling.interval > 0) {
//       pollingTimer = setInterval(() => {
//         if (!state.loading) {
//           executeFetch().catch(() => {
//             // Ignore polling errors
//           });
//         }
//       }, finalOptions.polling.interval);
//     }
//   };

//   /**
//    * Stop polling
//    */
//   const stopPolling = (): void => {
//     if (pollingTimer) {
//       clearInterval(pollingTimer);
//       pollingTimer = null;
//     }
//   };

//   // Fetcher interface implementation
//   const fetcher: Fetcher<T> = {
//     async fetch(): Promise<T | null> {
//       // Check if we should refetch
//       if (finalOptions.shouldRefetch && !finalOptions.shouldRefetch(state)) {
//         return state.data;
//       }

//       // Return cached data if valid
//       if (isCacheValid(cacheKey) && !isDataStale(cacheKey)) {
//         return getCachedData<T>(cacheKey);
//       }

//       return executeFetch();
//     },

//     getState(): FetcherState<T> {
//       return { ...state };
//     },

//     subscribe(callback: (state: FetcherState<T>) => void): () => void {
//       return subscribe(cacheKey, callback);
//     },

//     use(): FetcherResult<T> {
//       // This would be implemented with React hooks in a real implementation
//       // For now, return a basic implementation
//       return {
//         data: state.data,
//         loading: state.loading,
//         error: state.error,
//         isStale: state.isStale,
//         isCached: state.isCached,
//         refetch: () => executeFetch(),
//         mutate: (data: T | ((prev: T | null) => T)) => {
//           const newData = typeof data === 'function' ? data(state.data) : data;
//           updateState({ data: newData });
//           setCachedData(cacheKey, newData, finalOptions.cacheTime, finalOptions.staleTime);
//         },
//         invalidate: () => {
//           invalidateCache(cacheKey);
//           updateState({ isStale: true, isCached: false });
//         },
//         reset: () => {
//           invalidateCache(cacheKey);
//           updateState({
//             data: null,
//             loading: false,
//             error: null,
//             lastFetch: null,
//             cacheTime: null,
//             isStale: true,
//             isCached: false,
//             fetchCount: 0,
//             retryCount: 0
//           });
//         },
//         cancel: () => {
//           if (abortController) {
//             abortController.abort();
//           }
//         }
//       };
//     },

//     invalidate(): void {
//       invalidateCache(cacheKey);
//       updateState({ isStale: true, isCached: false });
//     },

//     reset(): void {
//       stopPolling();
//       invalidateCache(cacheKey);
//       updateState({
//         data: null,
//         loading: false,
//         error: null,
//         lastFetch: null,
//         cacheTime: null,
//         isStale: true,
//         isCached: false,
//         fetchCount: 0,
//         retryCount: 0
//       });
//     },

//     updateOptions(newOptions: Partial<FetcherOptions<T>>): void {
//       Object.assign(finalOptions, newOptions);

//       // Restart polling if options changed
//       if (newOptions.polling) {
//         stopPolling();
//         startPolling();
//       }
//     }
//   };

//   // Start polling if enabled
//   startPolling();

//   return fetcher;
// };

// // ===== UTILITY FUNCTIONS =====

// /**
//  * Create a simple GET fetcher
//  */
// export const createGetFetcher = <T>(
//   key: string,
//   endpoint: string,
//   options?: Partial<FetcherOptions<T>>
// ): Fetcher<T> => {
//   return createFetcher(key, {
//     endpoint,
//     method: 'GET',
//     ...options
//   });
// };

// /**
//  * Create a POST fetcher
//  */
// export const createPostFetcher = <T, D = any>(
//   key: string,
//   endpoint: string,
//   data: D,
//   options?: Partial<FetcherOptions<T>>
// ): Fetcher<T> => {
//   return createFetcher(key, {
//     endpoint,
//     method: 'POST',
//     data,
//     ...options
//   });
// };

// /**
//  * Prefetch data
//  */
// export const prefetch = async <T>(
//   key: string,
//   options: FetcherOptions<T>
// ): Promise<T | null> => {
//   const fetcher = createFetcher(key, options);
//   return fetcher.fetch();
// };

// /**
//  * Invalidate multiple cache keys
//  */
// export const invalidateQueries = (pattern: string | RegExp): void => {
//   const keysToInvalidate: string[] = [];

//   for (const key of cache.keys()) {
//     if (typeof pattern === 'string') {
//       if (key.includes(pattern)) {
//         keysToInvalidate.push(key);
//       }
//     } else {
//       if (pattern.test(key)) {
//         keysToInvalidate.push(key);
//       }
//     }
//   }

//   keysToInvalidate.forEach(key => {
//     invalidateCache(key);
//   });
// };

// // ===== EXPORTS =====

// export type {
//   FetcherState,
//   FetcherOptions,
//   FetcherResult,
//   Fetcher
// };

/**
 * Development Notes:
 *
 * 1. Caching Strategy:
 *    - In-memory cache with TTL
 *    - Stale-while-revalidate pattern
 *    - Cache invalidation and cleanup
 *    - Request deduplication
 *
 * 2. State Management:
 *    - Reactive state updates
 *    - Subscriber pattern for notifications
 *    - Loading and error states
 *    - Optimistic updates support
 *
 * 3. Error Handling:
 *    - Automatic retry logic
 *    - Error transformation
 *    - Graceful degradation
 *    - Abort signal support
 *
 * 4. Performance:
 *    - Request deduplication
 *    - Background refetching
 *    - Polling support
 *    - Memory management
 *
 * 5. Developer Experience:
 *    - TypeScript support
 *    - Flexible configuration
 *    - Debug utilities
 *    - Easy integration
 *
 * 6. React Integration:
 *    - Hook-based API (use method)
 *    - Automatic re-renders
 *    - Suspense support (can be added)
 *    - SSR compatibility
 *
 * Usage Examples:
 * ```tsx
 * // Basic usage
 * const courseFetcher = createGetFetcher<Course[]>('courses', '/courses');
 *
 * // With options
 * const userFetcher = createFetcher<User>('user', {
 *   endpoint: '/user/profile',
 *   cacheTime: 10 * 60 * 1000, // 10 minutes
 *   staleTime: 5 * 60 * 1000,  // 5 minutes
 *   refetchOnWindowFocus: false
 * });
 *
 * // Prefetch data
 * await prefetch('courses', { endpoint: '/courses' });
 *
 * // Invalidate cache
 * invalidateQueries('courses');
 *
 * // Use in React component (conceptual)
 * const MyComponent = () => {
 *   const { data, loading, error } = courseFetcher.use();
 *
 *   if (loading) return <div>Loading...</div>;
 *   if (error) return <div>Error: {error.message}</div>;
 *
 *   return <div>{data?.map(course => course.title)}</div>;
 * };
 * ```
 */
