import { IGetJobPositionDepartmentListQuery } from "@/interfaces/admin/manage-job/department";
import { apiGetJobPositionDepartmentList } from "@/services/api/manage-job/department";
import { useQuery } from "@tanstack/react-query";

export const useGetJobPositionDepartmentListQuery = (
  query: IGetJobPositionDepartmentListQuery
) => {
  return useQuery({
    queryKey: ["manage-job", "department", query],
    queryFn: async () => {
      return await apiGetJobPositionDepartmentList(query);
    },
  });
};
