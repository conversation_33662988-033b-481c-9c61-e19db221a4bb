import { IGetJobPositionDetailParams } from "@/interfaces/admin/manage-job/detail";
import { apiGetJobPositionDetail } from "@/services/api/manage-job/detail";
import { useQuery } from "@tanstack/react-query";

export const useGetJobPositionDetailQuery = (
  params: IGetJobPositionDetailParams
) => {
  return useQuery({
    queryKey: ["manage-job", "detail", params],
    enabled: !!params.id,
    queryFn: async () => {
      return await apiGetJobPositionDetail(params);
    },
  });
};
