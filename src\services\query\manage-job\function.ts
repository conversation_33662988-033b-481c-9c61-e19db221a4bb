import { IGetJobPositionFunctionListQuery } from "@/interfaces/admin/manage-job/function";
import { apiGetJobPositionFunctionList } from "@/services/api/manage-job/function";
import { useQuery } from "@tanstack/react-query";

export const useGetJobPositionFunctionListQuery = (
  query: IGetJobPositionFunctionListQuery
) => {
  return useQuery({
    queryKey: ["manage-job", "function", query],
    queryFn: async () => {
      return await apiGetJobPositionFunctionList(query);
    },
  });
};
