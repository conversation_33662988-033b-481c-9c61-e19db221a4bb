import { IGetJobPositionListQuery } from "@/interfaces/admin/manage-job/list";
import { apiGetJobPositionList } from "@/services/api/manage-job/list";
import { useQuery } from "@tanstack/react-query";

export const useGetJobPositionListQuery = (query: IGetJobPositionListQuery) => {
  return useQuery({
    queryKey: ["manage-job", "list", query],
    queryFn: async () => {
      return await apiGetJobPositionList(query);
    },
  });
};
