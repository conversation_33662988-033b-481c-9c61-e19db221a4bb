import { IGetJobPositionStartingLevelListQuery } from "@/interfaces/admin/manage-job/starting-level";
import { apiGetJobPositionStartingLevelList } from "@/services/api/manage-job/starting-level";
import { useQuery } from "@tanstack/react-query";

export const useGetJobPositionStartingLevelListQuery = (
  query: IGetJobPositionStartingLevelListQuery
) => {
  return useQuery({
    queryKey: ["manage-job", "starting-level", query],
    queryFn: async () => {
      return await apiGetJobPositionStartingLevelList(query);
    },
  });
};
