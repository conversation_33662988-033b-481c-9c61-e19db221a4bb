import { IGetSliderDetailParams } from "@/interfaces/admin/manage-slider/detail";
import { apiGetSliderDetail } from "@/services/api/manage-slider/detail";
import { useQuery } from "@tanstack/react-query";

export const useGetSliderDetailQuery = (params: IGetSliderDetailParams) => {
  return useQuery({
    queryKey: ["slider", "detail", params],
    enabled: !!params.id,
    queryFn: async () => {
      return await apiGetSliderDetail(params);
    },
  });
};
