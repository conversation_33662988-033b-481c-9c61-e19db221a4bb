import { IGetUserDetailParams } from "@/interfaces/admin/user-management/detail";
import { apiGetUserDetail } from "@/services/api/user-management/detail";
import { useQuery } from "@tanstack/react-query";

export const useGetUserDetailQuery = (params: IGetUserDetailParams) => {
  return useQuery({
    queryKey: ["users", "detail", params],
    enabled: !!params.id,
    queryFn: async () => {
      return await apiGetUserDetail(params);
    },
  });
};
