import { IGetForumTitleQuery } from "@/interfaces/admin/user-management/forum-title";
import { apiGetMasterForumTitle } from "@/services/api/user-management/forum-title";
import { useQuery } from "@tanstack/react-query";

export const useGetMasterForumTitle = (query: IGetForumTitleQuery) => {
  return useQuery({
    queryKey: ["users", "master", "forum-title", query],
    queryFn: async () => {
      return await apiGetMasterForumTitle(query);
    },
  });
};
