import { IGetJobPositionQuery } from "@/interfaces/admin/user-management/job-position";
import { apiGetMasterJobPosition } from "@/services/api/user-management/job-position";
import { useQuery } from "@tanstack/react-query";

export const useGetMasterJobPosition = (query: IGetJobPositionQuery) => {
  return useQuery({
    queryKey: ["users", "master", "job-position", query],
    queryFn: async () => {
      return await apiGetMasterJobPosition(query);
    },
  });
};
