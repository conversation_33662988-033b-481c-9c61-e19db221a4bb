import { IGetUserEntityQuery } from "@/interfaces/admin/user-management/user-entity";
import { apiGetUserMasterEntity } from "@/services/api/user-management/user-entity";
import { useQuery } from "@tanstack/react-query";

export const useGetUserMasterEntity = (query: IGetUserEntityQuery) => {
  return useQuery({
    queryKey: ["users", "master", "entity", query],
    queryFn: async () => {
      return await apiGetUserMasterEntity(query);
    },
  });
};
