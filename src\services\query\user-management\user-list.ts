import { IGetMasterUserListQuery } from "@/interfaces/admin/user-management/user-list";
import { apiGetMasterUserList } from "@/services/api/user-management/user-list";
import { useQuery } from "@tanstack/react-query";

export const useGetMasterUserList = (query: IGetMasterUserListQuery) => {
  return useQuery({
    queryKey: ["users", "master", "user-list", query],
    queryFn: async () => {
      return await apiGetMasterUserList(query);
    },
  });
};
