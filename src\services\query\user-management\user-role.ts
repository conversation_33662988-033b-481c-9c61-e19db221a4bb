import { IGetUserRoleQuery } from "@/interfaces/admin/user-management/user-role";
import { apiGetUserMasterRole } from "@/services/api/user-management/user-role";
import { useQuery } from "@tanstack/react-query";

export const useGetUserMasterRole = (query: IGetUserRoleQuery) => {
  return useQuery({
    queryKey: ["users", "master", "role", query],
    queryFn: async () => {
      return await apiGetUserMasterRole(query);
    },
  });
};
