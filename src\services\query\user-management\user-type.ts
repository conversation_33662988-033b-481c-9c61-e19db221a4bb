import { IGetUserTypeQuery } from "@/interfaces/admin/user-management/user-type";
import { apiGetUserMasterType } from "@/services/api/user-management/user-type";
import { useQuery } from "@tanstack/react-query";

export const useGetUserMasterType = (query: IGetUserTypeQuery) => {
  return useQuery({
    queryKey: ["users", "master", "type", query],
    queryFn: async () => {
      return await apiGetUserMasterType(query);
    },
  });
};
