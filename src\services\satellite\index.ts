import axios from "axios";
import { env } from "../config/env";
import { clearSession, getSession, setSession } from "../session/session";
import { apiRefreshToken } from "../api/login/user";

export const api = axios.create({
  baseURL: env.API.URL,
  withCredentials: true,
  headers: {
    apikey: env.API.KEY,
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
    "X-Frame-Options": "SAMEORIGIN",
  },
});

api.interceptors.request.use(
  async function (config) {
    const session = await getSession();
    if (session) {
      config.headers["Authorization"] = `Bearer ${session.accessToken}`;
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
  //   { synchronous: true, runWhen: () => /* This function returns true */}
);

api.interceptors.response.use(
  function onFulfilled(response) {
    return response;
  },
  async function onRejected(error) {
    if (error.response.status === 401) {
      try {
        const res = await apiRefreshToken();
        if (res.status === true) {
          setSession(res.data.token);
          // retry the request
          return api.request(error.config);
        }
      } catch {
        // refresh token failed
        clearSession();
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);
