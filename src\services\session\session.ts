"use server";

import { cookies } from "next/headers";
import { ITokenResponse } from "@/interfaces/user/login";

export const setSession = async (token: ITokenResponse) => {
  const cookieStore = await cookies();
  cookieStore.set("access_token", token.accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    path: "/",
    maxAge: 7 * 24 * 60 * 60,
  });
  cookieStore.set("refresh_token", token.refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    path: "/",
    maxAge: 7 * 24 * 60 * 60,
  });
};

export const getSession = async () => {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("access_token");
  const refreshToken = cookieStore.get("refresh_token");
  return {
    accessToken: accessToken?.value,
    refreshToken: refreshToken?.value,
  };
};

export const clearSession = async () => {
  const cookieStore = await cookies();
  cookieStore.delete("access_token");
  cookieStore.delete("refresh_token");
};

export const setNeedUpdatePassword = async () => {
  const cookieStore = await cookies();
  cookieStore.set("need_update_password", "true", {
    sameSite: "lax",
    path: "/",
    maxAge: 7 * 24 * 60 * 60,
  });
};

export const getNeedUpdatePassword = async () => {
  const cookieStore = await cookies();
  const needUpdatePassword = cookieStore.get("need_update_password");
  return needUpdatePassword?.value === "true";
};

export const clearNeedUpdatePassword = async () => {
  const cookieStore = await cookies();
  cookieStore.delete("need_update_password");
};
