/**
 * Authentication Actions
 *
 * This file defines Redux actions for authentication state management.
 * It provides action creators for login, logout, registration, token refresh,
 * and user session management with comprehensive type safety.
 *
 * Key Concepts:
 * - Redux action creators
 * - Authentication flow management
 * - Type-safe action definitions
 * - Async action handling
 * - Error state management
 * - Session persistence
 *
 * Usage Examples:
 * ```tsx
 * // In components
 * import { useDispatch } from 'react-redux';
 * import { loginUser, logoutUser, ref=nullreshToken } from '@/store/actions/auth';
 *
 * const LoginForm = () => {
 *   const dispatch = useDispatch();
 *
 *   const handleLogin = async (credentials) => {
 *     dispatch(loginUser.pending());
 *     try {
 *       const result = await authService.login(credentials);
 *       dispatch(loginUser.fulfilled(result));
 *     } catch (error) {
 *       dispatch(loginUser.rejected(error));
 *     }
 *   };
 * };
 *
 * // In middleware/thunks
 * import { createAsyncThunk } from '@reduxjs/toolkit';
 * import { loginUserAsync } from '@/store/actions/auth';
 *
 * const handleAsyncLogin = createAsyncThunk(
 *   'auth/loginAsync',
 *   async (credentials, { dispatch }) => {
 *     return dispatch(loginUserAsync(credentials));
 *   }
 * );
 * ```
 */

import type {
  User,
  UserCredentials,
  UserRegistration,
} from "@/interfaces/user";

// ===== ACTION TYPES =====

/**
 * Authentication action types
 */
export const AUTH_ACTION_TYPES = {
  // Login actions
  LOGIN_PENDING: "auth/login/pending",
  LOGIN_FULFILLED: "auth/login/fulfilled",
  LOGIN_REJECTED: "auth/login/rejected",

  // Logout actions
  LOGOUT_PENDING: "auth/logout/pending",
  LOGOUT_FULFILLED: "auth/logout/fulfilled",
  LOGOUT_REJECTED: "auth/logout/rejected",

  // Registration actions
  REGISTER_PENDING: "auth/register/pending",
  REGISTER_FULFILLED: "auth/register/fulfilled",
  REGISTER_REJECTED: "auth/register/rejected",

  // Token refresh actions
  REFRESH_TOKEN_PENDING: "auth/refreshToken/pending",
  REFRESH_TOKEN_FULFILLED: "auth/refreshToken/fulfilled",
  REFRESH_TOKEN_REJECTED: "auth/refreshToken/rejected",

  // Session actions
  RESTORE_SESSION: "auth/restoreSession",
  CLEAR_SESSION: "auth/clearSession",
  UPDATE_USER: "auth/updateUser",

  // UI state actions
  SET_LOADING: "auth/setLoading",
  CLEAR_ERROR: "auth/clearError",
  SET_REDIRECT_URL: "auth/setRedirectUrl",
  CLEAR_REDIRECT_URL: "auth/clearRedirectUrl",
} as const;

// ===== ACTION PAYLOAD TYPES =====

/**
 * Login success payload
 */
export interface LoginSuccessPayload {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  permissions: string[];
}

/**
 * Registration success payload
 */
export interface RegisterSuccessPayload {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  emailVerificationRequired: boolean;
}

/**
 * Token refresh success payload
 */
export interface RefreshTokenSuccessPayload {
  accessToken: string;
  expiresAt: number;
}

/**
 * Authentication error payload
 */
export interface AuthErrorPayload {
  message: string;
  code: string;
  field?: string;
  details?: Record<string, any>;
}

/**
 * Session restore payload
 */
export interface SessionRestorePayload {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  permissions: string[];
}

// ===== ACTION INTERFACES =====

/**
 * Base action interface
 */
interface BaseAction<T extends string> {
  type: T;
  timestamp: number;
  requestId?: string;
}

/**
 * Action with payload
 */
interface ActionWithPayload<T extends string, P> extends BaseAction<T> {
  payload: P;
}

/**
 * Action with error
 */
interface ActionWithError<T extends string> extends BaseAction<T> {
  error: AuthErrorPayload;
}

// ===== LOGIN ACTIONS =====

/**
 * Login pending action
 */
export interface LoginPendingAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.LOGIN_PENDING> {
  meta: {
    credentials: Omit<UserCredentials, "password">;
  };
}

/**
 * Login fulfilled action
 */
export interface LoginFulfilledAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.LOGIN_FULFILLED,
    LoginSuccessPayload
  > {}

/**
 * Login rejected action
 */
export interface LoginRejectedAction
  extends ActionWithError<typeof AUTH_ACTION_TYPES.LOGIN_REJECTED> {}

// ===== LOGOUT ACTIONS =====

/**
 * Logout pending action
 */
export interface LogoutPendingAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.LOGOUT_PENDING> {}

/**
 * Logout fulfilled action
 */
export interface LogoutFulfilledAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.LOGOUT_FULFILLED> {
  payload: {
    reason: "user_initiated" | "token_expired" | "security" | "admin_action";
  };
}

/**
 * Logout rejected action
 */
export interface LogoutRejectedAction
  extends ActionWithError<typeof AUTH_ACTION_TYPES.LOGOUT_REJECTED> {}

// ===== REGISTRATION ACTIONS =====

/**
 * Register pending action
 */
export interface RegisterPendingAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.REGISTER_PENDING> {
  meta: {
    email: string;
  };
}

/**
 * Register fulfilled action
 */
export interface RegisterFulfilledAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.REGISTER_FULFILLED,
    RegisterSuccessPayload
  > {}

/**
 * Register rejected action
 */
export interface RegisterRejectedAction
  extends ActionWithError<typeof AUTH_ACTION_TYPES.REGISTER_REJECTED> {}

// ===== TOKEN REFRESH ACTIONS =====

/**
 * Refresh token pending action
 */
export interface RefreshTokenPendingAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.REFRESH_TOKEN_PENDING> {}

/**
 * Refresh token fulfilled action
 */
export interface RefreshTokenFulfilledAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.REFRESH_TOKEN_FULFILLED,
    RefreshTokenSuccessPayload
  > {}

/**
 * Refresh token rejected action
 */
export interface RefreshTokenRejectedAction
  extends ActionWithError<typeof AUTH_ACTION_TYPES.REFRESH_TOKEN_REJECTED> {}

// ===== SESSION ACTIONS =====

/**
 * Restore session action
 */
export interface RestoreSessionAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.RESTORE_SESSION,
    SessionRestorePayload
  > {}

/**
 * Clear session action
 */
export interface ClearSessionAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.CLEAR_SESSION> {
  payload: {
    reason: "logout" | "expired" | "security" | "error";
  };
}

/**
 * Update user action
 */
export interface UpdateUserAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.UPDATE_USER,
    Partial<User>
  > {}

// ===== UI STATE ACTIONS =====

/**
 * Set loading action
 */
export interface SetLoadingAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.SET_LOADING,
    {
      operation: "login" | "logout" | "register" | "refresh" | "restore";
      loading: boolean;
    }
  > {}

/**
 * Clear error action
 */
export interface ClearErrorAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.CLEAR_ERROR> {}

/**
 * Set redirect URL action
 */
export interface SetRedirectUrlAction
  extends ActionWithPayload<
    typeof AUTH_ACTION_TYPES.SET_REDIRECT_URL,
    string
  > {}

/**
 * Clear redirect URL action
 */
export interface ClearRedirectUrlAction
  extends BaseAction<typeof AUTH_ACTION_TYPES.CLEAR_REDIRECT_URL> {}

// ===== UNION TYPES =====

/**
 * All authentication actions
 */
export type AuthAction =
  | LoginPendingAction
  | LoginFulfilledAction
  | LoginRejectedAction
  | LogoutPendingAction
  | LogoutFulfilledAction
  | LogoutRejectedAction
  | RegisterPendingAction
  | RegisterFulfilledAction
  | RegisterRejectedAction
  | RefreshTokenPendingAction
  | RefreshTokenFulfilledAction
  | RefreshTokenRejectedAction
  | RestoreSessionAction
  | ClearSessionAction
  | UpdateUserAction
  | SetLoadingAction
  | ClearErrorAction
  | SetRedirectUrlAction
  | ClearRedirectUrlAction;

// ===== ACTION CREATORS =====

/**
 * Generate unique request ID
 */
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Create base action
 */
const createBaseAction = <T extends string>(
  type: T,
  requestId?: string
): BaseAction<T> => ({
  type,
  timestamp: Date.now(),
  requestId: requestId || generateRequestId(),
});

/**
 * Login action creators
 */
export const loginUser = {
  pending: (
    credentials: UserCredentials,
    requestId?: string
  ): LoginPendingAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.LOGIN_PENDING, requestId),
    meta: {
      credentials: {
        email: credentials.email,
        rememberMe: credentials.rememberMe,
      },
    },
  }),

  fulfilled: (
    payload: LoginSuccessPayload,
    requestId?: string
  ): LoginFulfilledAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.LOGIN_FULFILLED, requestId),
    payload,
  }),

  rejected: (
    error: AuthErrorPayload,
    requestId?: string
  ): LoginRejectedAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.LOGIN_REJECTED, requestId),
    error,
  }),
};

/**
 * Logout action creators
 */
export const logoutUser = {
  pending: (requestId?: string): LogoutPendingAction =>
    createBaseAction(AUTH_ACTION_TYPES.LOGOUT_PENDING, requestId),

  fulfilled: (
    reason:
      | "user_initiated"
      | "token_expired"
      | "security"
      | "admin_action" = "user_initiated",
    requestId?: string
  ): LogoutFulfilledAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.LOGOUT_FULFILLED, requestId),
    payload: { reason },
  }),

  rejected: (
    error: AuthErrorPayload,
    requestId?: string
  ): LogoutRejectedAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.LOGOUT_REJECTED, requestId),
    error,
  }),
};

/**
 * Registration action creators
 */
export const registerUser = {
  pending: (email: string, requestId?: string): RegisterPendingAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.REGISTER_PENDING, requestId),
    meta: { email },
  }),

  fulfilled: (
    payload: RegisterSuccessPayload,
    requestId?: string
  ): RegisterFulfilledAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.REGISTER_FULFILLED, requestId),
    payload,
  }),

  rejected: (
    error: AuthErrorPayload,
    requestId?: string
  ): RegisterRejectedAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.REGISTER_REJECTED, requestId),
    error,
  }),
};

/**
 * Token refresh action creators
 */
export const refreshToken = {
  pending: (requestId?: string): RefreshTokenPendingAction =>
    createBaseAction(AUTH_ACTION_TYPES.REFRESH_TOKEN_PENDING, requestId),

  fulfilled: (
    payload: RefreshTokenSuccessPayload,
    requestId?: string
  ): RefreshTokenFulfilledAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.REFRESH_TOKEN_FULFILLED, requestId),
    payload,
  }),

  rejected: (
    error: AuthErrorPayload,
    requestId?: string
  ): RefreshTokenRejectedAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.REFRESH_TOKEN_REJECTED, requestId),
    error,
  }),
};

/**
 * Session action creators
 */
export const sessionActions = {
  restore: (payload: SessionRestorePayload): RestoreSessionAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.RESTORE_SESSION),
    payload,
  }),

  clear: (
    reason: "logout" | "expired" | "security" | "error" = "logout"
  ): ClearSessionAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.CLEAR_SESSION),
    payload: { reason },
  }),

  updateUser: (payload: Partial<User>): UpdateUserAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.UPDATE_USER),
    payload,
  }),
};

/**
 * UI state action creators
 */
export const uiActions = {
  setLoading: (
    operation: "login" | "logout" | "register" | "refresh" | "restore",
    loading: boolean
  ): SetLoadingAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.SET_LOADING),
    payload: { operation, loading },
  }),

  clearError: (): ClearErrorAction =>
    createBaseAction(AUTH_ACTION_TYPES.CLEAR_ERROR),

  setRedirectUrl: (url: string): SetRedirectUrlAction => ({
    ...createBaseAction(AUTH_ACTION_TYPES.SET_REDIRECT_URL),
    payload: url,
  }),

  clearRedirectUrl: (): ClearRedirectUrlAction =>
    createBaseAction(AUTH_ACTION_TYPES.CLEAR_REDIRECT_URL),
};

/**
 * Combined action creators
 */
export const authActions = {
  login: loginUser,
  logout: logoutUser,
  register: registerUser,
  refreshToken,
  session: sessionActions,
  ui: uiActions,
};

/**
 * Action type guards
 */
export const isAuthAction = (action: any): action is AuthAction => {
  return (
    typeof action === "object" &&
    action !== null &&
    typeof action.type === "string" &&
    action.type.startsWith("auth/")
  );
};

export const isLoginAction = (
  action: AuthAction
): action is
  | LoginPendingAction
  | LoginFulfilledAction
  | LoginRejectedAction => {
  return action.type.startsWith("auth/login/");
};

export const isLogoutAction = (
  action: AuthAction
): action is
  | LogoutPendingAction
  | LogoutFulfilledAction
  | LogoutRejectedAction => {
  return action.type.startsWith("auth/logout/");
};

export const isRegisterAction = (
  action: AuthAction
): action is
  | RegisterPendingAction
  | RegisterFulfilledAction
  | RegisterRejectedAction => {
  return action.type.startsWith("auth/register/");
};

export const isRefreshTokenAction = (
  action: AuthAction
): action is
  | RefreshTokenPendingAction
  | RefreshTokenFulfilledAction
  | RefreshTokenRejectedAction => {
  return action.type.startsWith("auth/refreshToken/");
};

/**
 * Development Notes:
 *
 * 1. Action Design:
 *    - Follow Redux best practices with type-safe actions
 *    - Include metadata for debugging and analytics
 *    - Provide comprehensive error handling
 *    - Support async operation tracking
 *
 * 2. Type Safety:
 *    - Strict TypeScript interfaces for all actions
 *    - Type guards for runtime type checking
 *    - Payload validation and sanitization
 *    - Error type standardization
 *
 * 3. Authentication Flow:
 *    - Support multiple authentication methods
 *    - Handle token refresh automatically
 *    - Manage session persistence
 *    - Provide logout with different reasons
 *
 * 4. Error Handling:
 *    - Standardized error payload structure
 *    - Field-specific validation errors
 *    - Detailed error codes and messages
 *    - Error recovery mechanisms
 *
 * 5. Performance:
 *    - Minimal action payload sizes
 *    - Efficient action creator functions
 *    - Request ID tracking for deduplication
 *    - Optimistic update support
 *
 * 6. Security:
 *    - Never include sensitive data in actions
 *    - Sanitize user input in action payloads
 *    - Implement proper session management
 *    - Support security-related logout scenarios
 *
 * Usage Examples:
 * ```tsx
 * // Basic login flow
 * const handleLogin = async (credentials: UserCredentials) => {
 *   dispatch(authActions.login.pending(credentials));
 *
 *   try {
 *     const response = await authService.login(credentials);
 *     dispatch(authActions.login.fulfilled(response));
 *   } catch (error) {
 *     dispatch(authActions.login.rejected({
 *       message: error.message,
 *       code: error.code,
 *       field: error.field
 *     }));
 *   }
 * };
 *
 * // Session restoration
 * const restoreSession = () => {
 *   const savedSession = localStorage.getItem('session');
 *   if (savedSession) {
 *     const session = JSON.parse(savedSession);
 *     dispatch(authActions.session.restore(session));
 *   }
 * };
 *
 * // Automatic token refresh
 * const setupTokenRefresh = () => {
 *   setInterval(async () => {
 *     if (shouldRefreshToken()) {
 *       dispatch(authActions.refreshToken.pending());
 *
 *       try {
 *         const newToken = await authService.refreshToken();
 *         dispatch(authActions.refreshToken.fulfilled(newToken));
 *       } catch (error) {
 *         dispatch(authActions.refreshToken.rejected(error));
 *         dispatch(authActions.logout.fulfilled('token_expired'));
 *       }
 *     }
 *   }, 60000); // Check every minute
 * };
 *
 * // Error handling
 * const handleAuthError = (error: AuthErrorPayload) => {
 *   if (error.code === 'INVALID_CREDENTIALS') {
 *     // Show specific error message
 *   } else if (error.code === 'ACCOUNT_LOCKED') {
 *     // Redirect to account recovery
 *   } else {
 *     // Show generic error
 *   }
 * };
 * ```
 */
