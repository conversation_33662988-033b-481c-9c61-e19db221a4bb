import { IGetListUserLogLoginQuery } from "@/interfaces/admin/user-log/list";
import { create } from "zustand";

interface IUserLogLoginFilter {
  userLogLoginQuery: IGetListUserLogLoginQuery;
  setUserLogLoginQuery: (query: Partial<IGetListUserLogLoginQuery>) => void;
}

export const useUserLogLoginFilterStore = create<IUserLogLoginFilter>()(
  (set, get) => ({
    userLogLoginQuery: { page: 1, limit: 10, search_by: "npk" },
    setUserLogLoginQuery: (query) =>
      set({
        userLogLoginQuery: { ...get().userLogLoginQuery, ...query },
      }),
  })
);
