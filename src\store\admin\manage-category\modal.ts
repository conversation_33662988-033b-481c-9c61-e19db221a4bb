import {
  ICategory,
  ISubCategory,
} from "@/interfaces/admin/manage-category/list";
import { create } from "zustand";

interface IManageCategoryModal {
  openedCategory: ICategory | null;
  setOpenedCategory: (data: ICategory | null) => void;
  openedSubCategory: ISubCategory | null;
  setOpenedSubCategory: (data: ISubCategory | null) => void;
  openAddModal: boolean;
  setOpenAddModal: (open: boolean) => void;
  openEditModal: boolean;
  setOpenEditModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useManageCategoryModal = create<IManageCategoryModal>()((set) => ({
  openedCategory: null,
  setOpenedCategory: (data: ICategory | null) => set({ openedCategory: data }),
  openedSubCategory: null,
  setOpenedSubCategory: (data: ISubCategory | null) =>
    set({ openedSubCategory: data }),
  openAddModal: false,
  setOpenAddModal: (open: boolean) => set({ openAddModal: open }),
  openEditModal: false,
  setOpenEditModal: (open: boolean) => set({ openEditModal: open }),
  openDeleteModal: false,
  setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
}));
