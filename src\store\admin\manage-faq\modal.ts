import { create } from "zustand";

interface IManageFaqModal {
  openedFaqId: string;
  setOpenedFaqId: (id: string) => void;
  openAddFaq: boolean;
  setOpenAddFaq: (open: boolean) => void;
  openEditFaq: boolean;
  setOpenEditFaq: (open: boolean) => void;
  openDeleteFaq: boolean;
  setOpenDeleteFaq: (open: boolean) => void;
}

export const useManageFaqModal = create<IManageFaqModal>()((set) => ({
  openedFaqId: "",
  setOpenedFaqId: (id: string) => set({ openedFaqId: id }),
  openAddFaq: false,
  setOpenAddFaq: (open: boolean) => set({ openAddFaq: open }),
  openEditFaq: false,
  setOpenEditFaq: (open: boolean) => set({ openEditFaq: open }),
  openDeleteFaq: false,
  setOpenDeleteFaq: (open: boolean) => set({ openDeleteFaq: open }),
}));
