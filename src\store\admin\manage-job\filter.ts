import { IGetJobPositionListQuery } from "@/interfaces/admin/manage-job/list";
import { create } from "zustand";

interface IManageJobFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  query: IGetJobPositionListQuery;
  setQuery: (query: IGetJobPositionListQuery) => void;
}

export const useManageJobFilterStore = create<IManageJobFilter>()((set) => ({
  openFilter: false,
  setOpenFilter: (open: boolean) => set({ openFilter: open }),
  query: {
    is_new_user: false,
  },
  setQuery: (query: IGetJobPositionListQuery) => set({ query }),
}));
