import { create } from "zustand";

interface IManageJobModal {
  openAddJobPosition: boolean;
  setOpenAddJobPosition: (open: boolean) => void;
  openVerifyJobPositionConfirmation: boolean;
  setOpenVerifyJobPositionConfirmation: (open: boolean) => void;
  openUploadHistory: boolean;
  setOpenUploadHistory: (open: boolean) => void;
  openUploadExcel: boolean;
  setOpenUploadExcel: (open: boolean) => void;
  openUploadExcelConfirmation: boolean;
  setOpenUploadExcelConfirmation: (open: boolean) => void;
  openDeleteJobPosition: boolean;
  setOpenDeleteJobPosition: (open: boolean) => void;
  currentData: number | null;
  setCurrentData: (data: number | null) => void;
}

export const useManageJobModalStore = create<IManageJobModal>((set) => ({
  openAddJobPosition: false,
  setOpenAddJobPosition: (open: boolean) => set({ openAddJobPosition: open }),
  openVerifyJobPositionConfirmation: false,
  setOpenVerifyJobPositionConfirmation: (open: boolean) =>
    set({ openVerifyJobPositionConfirmation: open }),
  openUploadHistory: false,
  setOpenUploadHistory: (open: boolean) => set({ openUploadHistory: open }),
  openUploadExcel: false,
  setOpenUploadExcel: (open: boolean) => set({ openUploadExcel: open }),
  openUploadExcelConfirmation: false,
  setOpenUploadExcelConfirmation: (open: boolean) =>
    set({ openUploadExcelConfirmation: open }),
  openDeleteJobPosition: false,
  setOpenDeleteJobPosition: (open: boolean) =>
    set({ openDeleteJobPosition: open }),
  currentData: null,
  setCurrentData: (data: number | null) => set({ currentData: data }),
}));
