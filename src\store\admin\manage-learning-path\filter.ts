import { create } from "zustand";

interface IManageLearningPathFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  // query: IGetLearningPathPositionListQuery;
  // setQuery: (query: IGetLearningPathPositionListQuery) => void;
}

export const useManageLearningPathFilterStore =
  create<IManageLearningPathFilter>()((set) => ({
    openFilter: false,
    setOpenFilter: (open: boolean) => set({ openFilter: open }),
    // query: {
    //   is_new_user: false,
    // },
    // setQuery: (query: IGetLearningPathPositionListQuery) => set({ query }),
  }));
