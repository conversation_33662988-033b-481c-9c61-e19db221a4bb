import { create } from "zustand";

interface IManageLearningPathModal {
  openAddLearningLevelModal: boolean;
  setOpenAddLearningLevelModal: (open: boolean) => void;
  openEditModal: boolean;
  setOpenEditModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useManageLearningPathModal = create<IManageLearningPathModal>()(
  (set) => ({
    openedManageLearningPath: null,
    openAddLearningLevelModal: false,
    setOpenAddLearningLevelModal: (open: boolean) =>
      set({ openAddLearningLevelModal: open }),
    openEditModal: false,
    setOpenEditModal: (open: boolean) => set({ openEditModal: open }),
    openDeleteModal: false,
    setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
  })
);
