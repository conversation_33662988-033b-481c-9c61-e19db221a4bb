import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import { create } from "zustand";

interface IManageCategoryQuery {
  categoryQuery: IGetListCategoryQuery;
  setCategoryQuery: (query: Partial<IGetListCategoryQuery>) => void;
  subCategoryQuery: IGetListSubCategoryQuery;
  setSubCategoryQuery: (query: Partial<IGetListSubCategoryQuery>) => void;
}

export const useManageCategoryQueryStore = create<IManageCategoryQuery>()(
  (set, get) => ({
    categoryQuery: { page: 1, limit: 10, search_by: "category_name" },
    setCategoryQuery: (query) =>
      set({ categoryQuery: { ...(get().categoryQuery as any), ...query } }),
    subCategoryQuery: { page: 1, search_by: "sub_category_name" },
    setSubCategoryQuery: (query) =>
      set({
        subCategoryQuery: { ...(get().subCategoryQuery as any), ...query },
      }),
  })
);
