import { IMaterialType } from "@/interfaces/admin/manage-material/common";
import { IMaterial } from "@/interfaces/admin/manage-material/list";
import { create } from "zustand";

interface IManageMaterialModal {
  materialType: IMaterialType;
  setMaterialType: (type: IMaterialType) => void;
  openedMaterial: IMaterial | null;
  setOpenedMaterial: (data: IMaterial | null) => void;
  openAddModal: boolean;
  setOpenAddModal: (open: boolean) => void;
  openEditModal: boolean;
  setOpenEditModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useManageMaterialModal = create<IManageMaterialModal>()((set) => ({
  materialType: "video",
  setMaterialType: (type: IMaterialType) => set({ materialType: type }),
  openedMaterial: null,
  setOpenedMaterial: (data: IMaterial | null) => set({ openedMaterial: data }),
  openAddModal: false,
  setOpenAddModal: (open: boolean) => set({ openAddModal: open }),
  openEditModal: false,
  setOpenEditModal: (open: boolean) => set({ openEditModal: open }),
  openDeleteModal: false,
  setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
}));
