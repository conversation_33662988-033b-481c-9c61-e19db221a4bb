import { create } from "zustand";

interface IManageSliderModal {
  openAddSlider: boolean;
  setOpenAddSlider: (open: boolean) => void;
  openEditSlider: boolean;
  setOpenEditSlider: (open: boolean) => void;
  openDeleteSliderConfirmation: boolean;
  setOpenDeleteSliderConfirmation: (open: boolean) => void;
  currentData: number | null;
  setCurrentData: (data: number | null) => void;
}

export const useManageSliderModal = create<IManageSliderModal>()((set) => ({
  openAddSlider: false,
  setOpenAddSlider: (open: boolean) => set({ openAddSlider: open }),
  openEditSlider: false,
  setOpenEditSlider: (open: boolean) => set({ openEditSlider: open }),
  openDeleteSliderConfirmation: false,
  setOpenDeleteSliderConfirmation: (open: boolean) =>
    set({ openDeleteSliderConfirmation: open }),
  currentData: null,
  setCurrentData: (data: number | null) => set({ currentData: data }),
}));
