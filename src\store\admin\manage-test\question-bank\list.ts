import { IQuestionBank } from "@/interfaces/admin/manage-test/question-bank/list";
import { create } from "zustand";

interface IQuestionBankTableList {
  selectedQuestionBanksFinal: IQuestionBank[];
  setSelectedQuestionBanksFinal: VoidFunction;
  selectedQuestionBanks: IQuestionBank[];
  setSelectedQuestionBanks: (questions: IQuestionBank[]) => void;
  deleteFinalQuestionBank: (id: number | string) => void;
}

export const useQuestionBankTableListStore = create<IQuestionBankTableList>()(
  (set, get) => ({
    selectedQuestionBanksFinal: [],
    selectedQuestionBanks: [],
    setSelectedQuestionBanks: (question) => {
      if (!question.length) {
        set({ selectedQuestionBanks: [] });
        return;
      }

      if (question.length === 1) {
        const currentData = get().selectedQuestionBanks;
        const alreadySelected = currentData.find(
          (it) => it.questionId === question[0].questionId
        );

        if (alreadySelected) {
          set({
            selectedQuestionBanks: currentData.filter(
              (it) => it.questionId === question[0].questionId
            ),
          });
        } else {
          set({ selectedQuestionBanks: [...currentData, ...question] });
        }
        return;
      }

      // save all question
      set({ selectedQuestionBanks: question });
    },
    setSelectedQuestionBanksFinal: () => {
      set({ selectedQuestionBanksFinal: get().selectedQuestionBanks });
    },
    deleteFinalQuestionBank: (id) => {
      const currentData = get().selectedQuestionBanksFinal;

      set({
        selectedQuestionBanksFinal: currentData.filter(
          (it) => it.questionId !== id
        ),
      });
    },
  })
);
