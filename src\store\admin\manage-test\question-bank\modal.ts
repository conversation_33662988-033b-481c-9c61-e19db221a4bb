import { IQuestionBank } from "@/interfaces/admin/manage-test/question-bank/list";
import { create } from "zustand";

interface IQuestionBankModal {
  openedQuestionBank: IQuestionBank | null;
  setOpenedQuestionBank: (data: IQuestionBank | null) => void;
  openAddModal: boolean;
  setOpenAddModal: (open: boolean) => void;
  openDeleteModal: boolean;
  setOpenDeleteModal: (open: boolean) => void;
}

export const useQuestionBankModal = create<IQuestionBankModal>()((set) => ({
  openedQuestionBank: null,
  setOpenedQuestionBank: (data: IQuestionBank | null) =>
    set({ openedQuestionBank: data }),
  openAddModal: false,
  setOpenAddModal: (open: boolean) => set({ openAddModal: open }),
  openDeleteModal: false,
  setOpenDeleteModal: (open: boolean) => set({ openDeleteModal: open }),
}));
