import { create } from "zustand";

interface IQuestionTemplateMutationModal {
  openListQuestionBankModal: boolean;
  setOpenListQuestionBankModal: (open: boolean) => void;
}

export const useQuestionTemplateMutationModal =
  create<IQuestionTemplateMutationModal>()((set) => ({
    openListQuestionBankModal: false,
    setOpenListQuestionBankModal: (open: boolean) =>
      set({ openListQuestionBankModal: open }),
  }));
