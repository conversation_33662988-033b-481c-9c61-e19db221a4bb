import { IGetUserListQuery } from "@/interfaces/admin/user-management/list";
import { create } from "zustand";

interface IUserManagementFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  query: IGetUserListQuery;
  setQuery: (query: IGetUserListQuery) => void;
}

export const useUserManagementFilterStore = create<IUserManagementFilter>()(
  (set) => ({
    openFilter: false,
    setOpenFilter: (open: boolean) => set({ openFilter: open }),
    query: {
      is_new_user: false,
    },
    setQuery: (query: IGetUserListQuery) => set({ query }),
  })
);
