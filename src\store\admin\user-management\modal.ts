import { create } from "zustand";

interface IUserManagementModal {
  openAddUser: boolean;
  setOpenAddUser: (open: boolean) => void;
  currentData: number | null;
  setCurrentData: (data: number | null) => void;
  openVerifyUserConfirmation: boolean;
  setOpenVerifyUserConfirmation: (open: boolean) => void;
  openResetPassword: boolean;
  setOpenResetPassword: (open: boolean) => void;
  openUploadHistory: boolean;
  setOpenUploadHistory: (open: boolean) => void;
  openUploadExcel: boolean;
  setOpenUploadExcel: (open: boolean) => void;
  openUploadExcelConfirmation: boolean;
  setOpenUploadExcelConfirmation: (open: boolean) => void;
  setOpenDeleteUser: (open: boolean) => void;
  openDeleteUser: boolean;
}

export const useUserManagementModalStore = create<IUserManagementModal>(
  (set) => ({
    openAddUser: false,
    setOpenAddUser: (open: boolean) => set({ openAddUser: open }),
    currentData: null,
    setCurrentData: (data: number | null) => set({ currentData: data }),
    openVerifyUserConfirmation: false,
    setOpenVerifyUserConfirmation: (open: boolean) =>
      set({ openVerifyUserConfirmation: open }),
    openResetPassword: false,
    setOpenResetPassword: (open: boolean) => set({ openResetPassword: open }),
    openUploadHistory: false,
    setOpenUploadHistory: (open: boolean) => set({ openUploadHistory: open }),
    openUploadExcel: false,
    setOpenUploadExcel: (open: boolean) => set({ openUploadExcel: open }),
    openUploadExcelConfirmation: false,
    setOpenUploadExcelConfirmation: (open: boolean) =>
      set({ openUploadExcelConfirmation: open }),
    openDeleteUser: false,
    setOpenDeleteUser: (open: boolean) => set({ openDeleteUser: open }),
  })
);
