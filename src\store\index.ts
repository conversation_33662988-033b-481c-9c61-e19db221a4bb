/**
 * Store Index
 * 
 * This file serves as the main entry point for the Redux store configuration.
 * It provides a unified interface for accessing the store, actions, reducers,
 * selectors, and storage utilities.
 * 
 * Key Features:
 * - Redux store configuration
 * - Redux Toolkit integration
 * - Persistence configuration
 * - Middleware setup
 * - Development tools
 * - Type safety
 * 
 * Usage Examples:
 * ```tsx
 * // In main.tsx or App.tsx
 * import { store, persistor } from '@/store';
 * import { Provider } from 'react-redux';
 * import { PersistGate } from 'redux-persist/integration/react';
 * 
 * function App() {
 *   return (
 *     <Provider store={store}>
 *       <PersistGate loading={<div>Loading...</div>} persistor={persistor}>
 *         <YourApp />
 *       </PersistGate>
 *     </Provider>
 *   );
 * }
 * 
 * // In components
 * import { useAppSelector, useAppDispatch } from '@/store';
 * import { authActions, courseActions } from '@/store';
 * 
 * function MyComponent() {
 *   const dispatch = useAppDispatch();
 *   const user = useAppSelector(state => state.auth.user);
 *   
 *   const handleLogin = () => {
 *     dispatch(authActions.loginRequest({ email, password }));
 *   };
 * }
 * ```
 */

// import { configureStore, combineReducers } from '@reduxjs/toolkit';
// import { 
//   persistStore, 
//   persistReducer,
//   FLUSH,
//   REHYDRATE,
//   PAUSE,
//   PERSIST,
//   PURGE,
//   REGISTER
// } from 'redux-persist';
// import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
// // @ts-ignore
// import { createLogger } from 'redux-logger';

// // ===== IMPORTS =====

// // Reducers
// import {
//   rootReducer,
//   initialRootState,
//   type RootState,
//   // type AppDispatch,
//   authSelectors,
//   courseSelectors,
//   // globalSelectors,
//   createTypedSelector,
//   devUtils as reducerDevUtils
// } from './reducers';

// // Actions
// import {
//   authActions,
//   courseActions,
//   type AppAction,
//   type AsyncActionState,
//   actionUtils,
//   devUtils as actionDevUtils
// } from './actions';

// // Storage
// import {
//   persistConfig,
//   storageUtils,
//   createStorageMiddleware,
//   getCurrentStorageConfig,
//   type StorageConfig
// } from './storage';

// // ===== MIDDLEWARE CONFIGURATION =====

// /**
//  * Development middleware configuration
//  */
// const getDevMiddleware = () => {
//   const middleware: any[] = [];
  
//   if (process.env.NODE_ENV === 'development') {
//     // Redux logger
//     const logger = createLogger({
//       collapsed: true,
//       duration: true,
//       timestamp: true,
//       logErrors: true,
//       diff: true,
//       predicate: (getState, action) => {
//         // Don't log certain actions to reduce noise
//         const ignoredActions = [
//           'persist/PERSIST',
//           'persist/REHYDRATE',
//           'persist/FLUSH',
//           'persist/PAUSE',
//           'persist/PURGE',
//           'persist/REGISTER'
//         ];
//         return !ignoredActions.includes(action.type);
//       }
//     });
    
//     middleware.push(logger);
//   }
  
//   return middleware;
// };

// /**
//  * Storage middleware configuration
//  */
// const storageMiddleware = createStorageMiddleware({
//   keys: ['auth', 'course'],
//   debounceMs: 2000
// });

// /**
//  * Custom middleware for action tracking
//  */
// const actionTrackingMiddleware = (store: any) => (next: any) => (action: any) => {
//   // Track action performance in development
//   if (process.env.NODE_ENV === 'development') {
//     const start = performance.now();
//     const result = next(action);
//     const end = performance.now();
    
//     if (end - start > 10) { // Log slow actions
//       console.warn(`Slow action detected: ${action.type} took ${end - start}ms`);
//     }
    
//     return result;
//   }
  
//   return next(action);
// };

// /**
//  * Error handling middleware
//  */
// const errorHandlingMiddleware = (store: any) => (next: any) => (action: any) => {
//   try {
//     return next(action);
//   } catch (error) {
//     console.error('Redux action error:', {
//       action,
//       error,
//       state: store.getState()
//     });
    
//     // Dispatch error action
//     store.dispatch({
//       type: 'GLOBAL_ERROR',
//       payload: {
//         message: error instanceof Error ? error.message : 'Unknown error',
//         action: action.type,
//         timestamp: Date.now()
//       }
//     });
    
//     throw error;
//   }
// };

// // ===== STORE CONFIGURATION =====

// /**
//  * Create persisted reducer
//  */
// const persistedReducer = persistReducer(persistConfig, rootReducer);

// /**
//  * Configure the Redux store
//  */
// export const store = configureStore({
//   reducer: persistedReducer,
//   middleware: (getDefaultMiddleware) =>
//     getDefaultMiddleware({
//       serializableCheck: {
//         ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
//         ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
//         ignoredPaths: ['items.dates']
//       },
//       immutableCheck: {
//         warnAfter: 128
//       }
//     })
//     .concat([
//       errorHandlingMiddleware,
//       actionTrackingMiddleware,
//       storageMiddleware,
//       ...getDevMiddleware()
//     ]),
//   devTools: process.env.NODE_ENV === 'development' && {
//     name: 'Lemon App Store',
//     trace: true,
//     traceLimit: 25,
//     actionSanitizer: (action: any) => ({
//       ...action,
//       // Sanitize sensitive data
//       payload: action.payload && typeof action.payload === 'object'
//         ? Object.keys(action.payload).reduce((acc, key) => {
//             if (key.toLowerCase().includes('password') || 
//                 key.toLowerCase().includes('token') ||
//                 key.toLowerCase().includes('secret')) {
//               acc[key] = '[REDACTED]';
//             } else {
//               acc[key] = action.payload[key];
//             }
//             return acc;
//           }, {} as any)
//         : action.payload
//     }),
//     stateSanitizer: (state: any) => ({
//       ...state,
//       auth: {
//         ...state.auth,
//         // Sanitize sensitive auth data
//         token: state.auth?.token ? '[REDACTED]' : null,
//         refreshToken: state.auth?.refreshToken ? '[REDACTED]' : null
//       }
//     })
//   },
//   preloadedState: undefined // Will be hydrated from persistence
// });

// /**
//  * Create persistor
//  */
// export const persistor = persistStore(store);

// // ===== TYPE DEFINITIONS =====

// /**
//  * Infer the `RootState` and `AppDispatch` types from the store itself
//  */
// export type { RootState } from './reducers';
// export type AppDispatch = typeof store.dispatch;
// export type AppStore = typeof store;
// export type AppGetState = typeof store.getState;

// // ===== TYPED HOOKS =====

// /**
//  * Use throughout your app instead of plain `useDispatch` and `useSelector`
//  */
// export const useAppDispatch = () => useDispatch<AppDispatch>();
// export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// /**
//  * Custom hook for dispatching async actions with loading state
//  */
// export const useAsyncDispatch = () => {
//   const dispatch = useAppDispatch();
  
//   return <T extends (...args: any[]) => any>(
//     asyncAction: T,
//     options?: {
//       onSuccess?: (result: any) => void;
//       onError?: (error: any) => void;
//       onFinally?: () => void;
//     }
//   ) => {
//     return async (...args: Parameters<T>) => {
//       try {
//         const result = await dispatch(asyncAction(...args));
//         options?.onSuccess?.(result);
//         return result;
//       } catch (error) {
//         options?.onError?.(error);
//         throw error;
//       } finally {
//         options?.onFinally?.();
//       }
//     };
//   };
// };

// /**
//  * Custom hook for selecting state with memoization
//  */
// export const useMemoizedSelector = <T>(
//   selector: (state: RootState) => T,
//   deps?: React.DependencyList
// ) => {
//   const memoizedSelector = React.useMemo(() => selector, deps || []);
//   return useAppSelector(memoizedSelector);
// };

// // ===== STORE UTILITIES =====

// /**
//  * Store utilities for external use
//  */
// export const storeUtils = {
//   /**
//    * Get current state
//    */
//   getState: () => store.getState(),
  
//   /**
//    * Dispatch action
//    */
//   dispatch: (action: AppAction) => store.dispatch(action),
  
//   /**
//    * Subscribe to store changes
//    */
//   subscribe: (listener: () => void) => store.subscribe(listener),
  
//   /**
//    * Reset store to initial state
//    */
//   reset: () => {
//     store.dispatch({ type: 'RESET_STORE' });
//   },
  
//   /**
//    * Get store statistics
//    */
//   getStats: () => {
//     const state = store.getState();
//     return {
//       stateSize: JSON.stringify(state).length,
//       authState: {
//         isAuthenticated: !!state.auth.user,
//         hasToken: !!state.auth.token,
//         sessionExpiry: state.auth.sessionExpiry
//       },
//       courseState: {
//         totalCourses: state.course.courses.length,
//         enrolledCourses: state.course.enrolledCourses.length,
//         isLoading: state.course.loading.courses
//       },
//       performance: {
//         lastUpdate: Date.now(),
//         storageUsage: storageUtils.getUsage()
//       }
//     };
//   },
  
//   /**
//    * Export state for debugging
//    */
//   exportState: () => {
//     const state = store.getState();
//     return {
//       ...state,
//       // Remove sensitive data
//       auth: {
//         ...state.auth,
//         token: state.auth.token ? '[REDACTED]' : null,
//         refreshToken: state.auth.refreshToken ? '[REDACTED]' : null
//       }
//     };
//   },
  
//   /**
//    * Import state (for testing/debugging)
//    */
//   importState: (state: Partial<RootState>) => {
//     store.dispatch({
//       type: 'IMPORT_STATE',
//       payload: state
//     });
//   }
// };

// // ===== DEVELOPMENT UTILITIES =====

// /**
//  * Development utilities (only available in development)
//  */
// export const devUtils = process.env.NODE_ENV === 'development' ? {
//   /**
//    * Store utilities
//    */
//   store: storeUtils,
  
//   /**
//    * Action utilities
//    */
//   actions: actionDevUtils,
  
//   /**
//    * Reducer utilities
//    */
//   reducers: reducerDevUtils,
  
//   /**
//    * Storage utilities
//    */
//   storage: storageUtils.dev,
  
//   /**
//    * Performance monitoring
//    */
//   performance: {
//     measureAction: (actionType: string, fn: () => void) => {
//       const start = performance.now();
//       fn();
//       const end = performance.now();
//       console.log(`Action ${actionType} took ${end - start}ms`);
//     },
    
//     measureSelector: <T>(name: string, selector: (state: RootState) => T) => {
//       return (state: RootState) => {
//         const start = performance.now();
//         const result = selector(state);
//         const end = performance.now();
        
//         if (end - start > 5) {
//           console.warn(`Slow selector ${name} took ${end - start}ms`);
//         }
        
//         return result;
//       };
//     }
//   },
  
//   /**
//    * Debug helpers
//    */
//   debug: {
//     logState: () => console.log('Current state:', storeUtils.exportState()),
//     logStats: () => console.log('Store stats:', storeUtils.getStats()),
//     logStorageUsage: () => console.log('Storage usage:', storageUtils.getUsage()),
    
//     // Time travel debugging
//     timeTravel: {
//       saveSnapshot: () => {
//         const snapshot = storeUtils.exportState();
//         localStorage.setItem('lemon_debug_snapshot', JSON.stringify(snapshot));
//         console.log('State snapshot saved');
//       },
      
//       loadSnapshot: () => {
//         const snapshot = localStorage.getItem('lemon_debug_snapshot');
//         if (snapshot) {
//           storeUtils.importState(JSON.parse(snapshot));
//           console.log('State snapshot loaded');
//         } else {
//           console.warn('No snapshot found');
//         }
//       }
//     }
//   }
// } : undefined;

// // ===== STORE INITIALIZATION =====

// /**
//  * Initialize store with default data
//  */
// export const initializeStore = async () => {
//   try {
//     // Load user preferences
//     const preferences = await storageUtils.loadUserPreferences();
//     if (preferences.success && preferences.data) {
//       // Apply user preferences to store
//       store.dispatch({
//         type: 'APPLY_USER_PREFERENCES',
//         payload: preferences.data
//       });
//     }
    
//     // Load cached data
//     const cacheData = await storageUtils.loadCacheData();
//     if (cacheData.success && cacheData.data) {
//       // Apply cached data to store
//       store.dispatch({
//         type: 'APPLY_CACHE_DATA',
//         payload: cacheData.data
//       });
//     }
    
//     // Initialize app settings
//     const appSettings = await storageUtils.loadAppSettings();
//     if (appSettings.success && appSettings.data) {
//       store.dispatch({
//         type: 'APPLY_APP_SETTINGS',
//         payload: appSettings.data
//       });
//     }
    
//     console.log('Store initialized successfully');
//   } catch (error) {
//     console.error('Failed to initialize store:', error);
//   }
// };

// // ===== EXPORTS =====

// // Re-export actions
// export {
//   authActions,
//   courseActions,
//   actionUtils,
//   type AppAction,
//   type AsyncActionState
// };

// // Re-export selectors
// export {
//   authSelectors,
//   courseSelectors,
//   globalSelectors,
//   createTypedSelector
// };

// // Re-export storage utilities
// export {
//   storageUtils,
//   getCurrentStorageConfig,
//   type StorageConfig
// };

// // Export store configuration
// export {
//   store,
//   persistor,
//   storeUtils,
//   initializeStore,
//   devUtils
// };

// // Export types
// export type {
//   RootState,
//   AppDispatch,
//   AppStore,
//   AppGetState
// };

/**
 * Development Notes:
 * 
 * 1. Store Configuration:
 *    - Redux Toolkit for modern Redux patterns
 *    - Redux Persist for state persistence
 *    - Comprehensive middleware setup
 *    - Development tools integration
 * 
 * 2. Type Safety:
 *    - Fully typed store, actions, and selectors
 *    - Custom hooks with proper typing
 *    - Type-safe state access
 *    - Action payload validation
 * 
 * 3. Performance:
 *    - Memoized selectors
 *    - Debounced storage operations
 *    - Action performance monitoring
 *    - Efficient state updates
 * 
 * 4. Development Experience:
 *    - Comprehensive debugging utilities
 *    - Time travel debugging
 *    - Performance monitoring
 *    - State export/import
 * 
 * 5. Security:
 *    - Sensitive data sanitization
 *    - Secure storage practices
 *    - Action payload filtering
 *    - Development-only utilities
 * 
 * Usage Examples:
 * ```tsx
 * // Basic store usage
 * import { store, useAppSelector, useAppDispatch } from '@/store';
 * import { authActions, courseActions } from '@/store';
 * 
 * function LoginComponent() {
 *   const dispatch = useAppDispatch();
 *   const { user, loading, error } = useAppSelector(state => state.auth);
 *   
 *   const handleLogin = async (credentials) => {
 *     try {
 *       await dispatch(authActions.loginRequest(credentials));
 *       // Handle success
 *     } catch (error) {
 *       // Handle error
 *     }
 *   };
 * }
 * 
 * // Advanced selector usage
 * import { useAppSelector, globalSelectors } from '@/store';
 * 
 * function DashboardComponent() {
 *   const dashboardData = useAppSelector(globalSelectors.getDashboardData);
 *   const isLoading = useAppSelector(globalSelectors.getGlobalLoading);
 *   
 *   // Component logic
 * }
 * 
 * // Development debugging
 * import { devUtils } from '@/store';
 * 
 * // In development console
 * devUtils?.debug.logState();
 * devUtils?.debug.timeTravel.saveSnapshot();
 * devUtils?.performance.measureAction('LOGIN', () => {
 *   store.dispatch(authActions.loginRequest(credentials));
 * });
 * ```
 */