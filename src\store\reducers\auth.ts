/**
 * Authentication Reducer
 * 
 * This file defines the Redux reducer for authentication state management.
 * It handles all authentication-related actions and maintains the auth state
 * with comprehensive error handling, loading states, and session management.
 * 
 * Key Concepts:
 * - Immutable state updates
 * - Authentication flow management
 * - Session persistence
 * - Error state handling
 * - Loading state tracking
 * - Security considerations
 * 
 * Usage Examples:
 * ```tsx
 * // In store configuration
 * import { authReducer } from '@/store/reducers/auth';
 * 
 * const store = configureStore({
 *   reducer: {
 *     auth: authReducer
 *   }
 * });
 * 
 * // In components
 * import { useSelector } from 'react-redux';
 * 
 * const LoginStatus = () => {
 *   const { user, isAuthenticated, loading } = useSelector(state => state.auth);
 *   
 *   if (loading.login) return <div>Logging in...</div>;
 *   if (isAuthenticated) return <div>Welcome, {user.name}!</div>;
 *   return <div>Please log in</div>;
 * };
 * ```
 */

import type { User } from '@/interfaces/user';
import type {
  AuthAction,
  LoginSuccessPayload,
  RegisterSuccessPayload,
  RefreshTokenSuccessPayload,
  AuthErrorPayload,
  SessionRestorePayload
} from '@/store/actions/auth';
import { AUTH_ACTION_TYPES } from '@/store/actions/auth';

// ===== STATE INTERFACE =====

/**
 * Authentication state interface
 */
export interface AuthState {
  // User data
  user: User | null;
  
  // Authentication status
  isAuthenticated: boolean;
  isEmailVerified: boolean;
  
  // Tokens
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiresAt: number | null;
  
  // Permissions
  permissions: string[];
  
  // Loading states
  loading: {
    login: boolean;
    logout: boolean;
    register: boolean;
    refresh: boolean;
    restore: boolean;
  };
  
  // Error states
  error: {
    login: AuthErrorPayload | null;
    logout: AuthErrorPayload | null;
    register: AuthErrorPayload | null;
    refresh: AuthErrorPayload | null;
    general: AuthErrorPayload | null;
  };
  
  // Session data
  session: {
    id: string | null;
    createdAt: number | null;
    lastActivity: number | null;
    expiresAt: number | null;
    deviceInfo: {
      userAgent: string | null;
      ipAddress: string | null;
      location: string | null;
    };
  };
  
  // UI state
  ui: {
    redirectUrl: string | null;
    rememberMe: boolean;
    showWelcomeMessage: boolean;
    lastLoginMethod: 'email' | 'social' | 'sso' | null;
  };
  
  // Metadata
  meta: {
    lastUpdated: number;
    version: string;
    migrationVersion: number;
  };
}

// ===== INITIAL STATE =====

/**
 * Initial authentication state
 */
export const initialAuthState: AuthState = {
  // User data
  user: null,
  
  // Authentication status
  isAuthenticated: false,
  isEmailVerified: false,
  
  // Tokens
  accessToken: null,
  refreshToken: null,
  tokenExpiresAt: null,
  
  // Permissions
  permissions: [],
  
  // Loading states
  loading: {
    login: false,
    logout: false,
    register: false,
    refresh: false,
    restore: false
  },
  
  // Error states
  error: {
    login: null,
    logout: null,
    register: null,
    refresh: null,
    general: null
  },
  
  // Session data
  session: {
    id: null,
    createdAt: null,
    lastActivity: null,
    expiresAt: null,
    deviceInfo: {
      userAgent: null,
      ipAddress: null,
      location: null
    }
  },
  
  // UI state
  ui: {
    redirectUrl: null,
    rememberMe: false,
    showWelcomeMessage: false,
    lastLoginMethod: null
  },
  
  // Metadata
  meta: {
    lastUpdated: Date.now(),
    version: '1.0.0',
    migrationVersion: 1
  }
};

// ===== HELPER FUNCTIONS =====

/**
 * Update metadata timestamp
 */
const updateMetadata = (state: AuthState): AuthState => ({
  ...state,
  meta: {
    ...state.meta,
    lastUpdated: Date.now()
  }
});

/**
 * Clear all errors
 */
const clearAllErrors = (state: AuthState): AuthState => ({
  ...state,
  error: {
    login: null,
    logout: null,
    register: null,
    refresh: null,
    general: null
  }
});

/**
 * Set loading state for specific operation
 */
const setLoadingState = (
  state: AuthState, 
  operation: keyof AuthState['loading'], 
  loading: boolean
): AuthState => ({
  ...state,
  loading: {
    ...state.loading,
    [operation]: loading
  }
});

/**
 * Set error state for specific operation
 */
const setErrorState = (
  state: AuthState, 
  operation: keyof AuthState['error'], 
  error: AuthErrorPayload | null
): AuthState => ({
  ...state,
  error: {
    ...state.error,
    [operation]: error
  }
});

/**
 * Set authenticated state with user data
 */
const setAuthenticatedState = (
  state: AuthState,
  payload: LoginSuccessPayload | RegisterSuccessPayload | SessionRestorePayload
): AuthState => ({
  ...state,
  user: payload.user,
  isAuthenticated: true,
  isEmailVerified: payload.user.isEmailVerified || false,
  accessToken: payload.accessToken,
  refreshToken: payload.refreshToken,
  tokenExpiresAt: payload.expiresAt,
  permissions: 'permissions' in payload ? payload.permissions : [],
  session: {
    ...state.session,
    id: payload.user.id,
    createdAt: Date.now(),
    lastActivity: Date.now(),
    expiresAt: payload.expiresAt
  },
  ui: {
    ...state.ui,
    showWelcomeMessage: true
  }
});

/**
 * Clear authenticated state
 */
const clearAuthenticatedState = (state: AuthState): AuthState => ({
  ...state,
  user: null,
  isAuthenticated: false,
  isEmailVerified: false,
  accessToken: null,
  refreshToken: null,
  tokenExpiresAt: null,
  permissions: [],
  session: {
    id: null,
    createdAt: null,
    lastActivity: null,
    expiresAt: null,
    deviceInfo: {
      userAgent: null,
      ipAddress: null,
      location: null
    }
  },
  ui: {
    ...state.ui,
    showWelcomeMessage: false,
    redirectUrl: null
  }
});

/**
 * Update session activity
 */
const updateSessionActivity = (state: AuthState): AuthState => ({
  ...state,
  session: {
    ...state.session,
    lastActivity: Date.now()
  }
});

// ===== REDUCER FUNCTION =====

/**
 * Authentication reducer
 */
export const authReducer = (state: AuthState = initialAuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    // ===== LOGIN ACTIONS =====
    
    case AUTH_ACTION_TYPES.LOGIN_PENDING:
      return updateMetadata(
        setLoadingState(
          clearAllErrors(state),
          'login',
          true
        )
      );
    
    case AUTH_ACTION_TYPES.LOGIN_FULFILLED:
      return updateMetadata(
        setLoadingState(
          setAuthenticatedState(
            clearAllErrors(state),
            action.payload
          ),
          'login',
          false
        )
      );
    
    case AUTH_ACTION_TYPES.LOGIN_REJECTED:
      return updateMetadata(
        setLoadingState(
          setErrorState(state, 'login', action.error),
          'login',
          false
        )
      );
    
    // ===== LOGOUT ACTIONS =====
    
    case AUTH_ACTION_TYPES.LOGOUT_PENDING:
      return updateMetadata(
        setLoadingState(state, 'logout', true)
      );
    
    case AUTH_ACTION_TYPES.LOGOUT_FULFILLED:
      return updateMetadata(
        setLoadingState(
          clearAuthenticatedState(
            clearAllErrors(state)
          ),
          'logout',
          false
        )
      );
    
    case AUTH_ACTION_TYPES.LOGOUT_REJECTED:
      return updateMetadata(
        setLoadingState(
          setErrorState(state, 'logout', action.error),
          'logout',
          false
        )
      );
    
    // ===== REGISTRATION ACTIONS =====
    
    case AUTH_ACTION_TYPES.REGISTER_PENDING:
      return updateMetadata(
        setLoadingState(
          clearAllErrors(state),
          'register',
          true
        )
      );
    
    case AUTH_ACTION_TYPES.REGISTER_FULFILLED:
      return updateMetadata(
        setLoadingState(
          setAuthenticatedState(
            clearAllErrors(state),
            action.payload
          ),
          'register',
          false
        )
      );
    
    case AUTH_ACTION_TYPES.REGISTER_REJECTED:
      return updateMetadata(
        setLoadingState(
          setErrorState(state, 'register', action.error),
          'register',
          false
        )
      );
    
    // ===== TOKEN REFRESH ACTIONS =====
    
    case AUTH_ACTION_TYPES.REFRESH_TOKEN_PENDING:
      return updateMetadata(
        setLoadingState(state, 'refresh', true)
      );
    
    case AUTH_ACTION_TYPES.REFRESH_TOKEN_FULFILLED:
      return updateMetadata(
        setLoadingState(
          updateSessionActivity({
            ...state,
            accessToken: action.payload.accessToken,
            tokenExpiresAt: action.payload.expiresAt
          }),
          'refresh',
          false
        )
      );
    
    case AUTH_ACTION_TYPES.REFRESH_TOKEN_REJECTED:
      return updateMetadata(
        setLoadingState(
          setErrorState(
            clearAuthenticatedState(state),
            'refresh',
            action.error
          ),
          'refresh',
          false
        )
      );
    
    // ===== SESSION ACTIONS =====
    
    case AUTH_ACTION_TYPES.RESTORE_SESSION:
      return updateMetadata(
        setAuthenticatedState(
          clearAllErrors(state),
          action.payload
        )
      );
    
    case AUTH_ACTION_TYPES.CLEAR_SESSION:
      return updateMetadata(
        clearAuthenticatedState(
          clearAllErrors(state)
        )
      );
    
    case AUTH_ACTION_TYPES.UPDATE_USER:
      if (!state.user) return state;
      
      return updateMetadata(
        updateSessionActivity({
          ...state,
          user: {
            ...state.user,
            ...action.payload
          }
        })
      );
    
    // ===== UI STATE ACTIONS =====
    
    case AUTH_ACTION_TYPES.SET_LOADING:
      return updateMetadata(
        setLoadingState(state, action.payload.operation, action.payload.loading)
      );
    
    case AUTH_ACTION_TYPES.CLEAR_ERROR:
      return updateMetadata(
        clearAllErrors(state)
      );
    
    case AUTH_ACTION_TYPES.SET_REDIRECT_URL:
      return updateMetadata({
        ...state,
        ui: {
          ...state.ui,
          redirectUrl: action.payload
        }
      });
    
    case AUTH_ACTION_TYPES.CLEAR_REDIRECT_URL:
      return updateMetadata({
        ...state,
        ui: {
          ...state.ui,
          redirectUrl: null
        }
      });
    
    // ===== DEFAULT =====
    
    default:
      return state;
  }
};

// ===== SELECTOR HELPERS =====

/**
 * Authentication selectors
 */
export const authSelectors = {
  // Basic selectors
  getUser: (state: { auth: AuthState }) => state.auth.user,
  getIsAuthenticated: (state: { auth: AuthState }) => state.auth.isAuthenticated,
  getIsEmailVerified: (state: { auth: AuthState }) => state.auth.isEmailVerified,
  getAccessToken: (state: { auth: AuthState }) => state.auth.accessToken,
  getPermissions: (state: { auth: AuthState }) => state.auth.permissions,
  
  // Loading selectors
  getIsLoading: (state: { auth: AuthState }) => 
    Object.values(state.auth.loading).some(loading => loading),
  getLoginLoading: (state: { auth: AuthState }) => state.auth.loading.login,
  getLogoutLoading: (state: { auth: AuthState }) => state.auth.loading.logout,
  getRegisterLoading: (state: { auth: AuthState }) => state.auth.loading.register,
  getRefreshLoading: (state: { auth: AuthState }) => state.auth.loading.refresh,
  
  // Error selectors
  getHasError: (state: { auth: AuthState }) => 
    Object.values(state.auth.error).some(error => error !== null),
  getLoginError: (state: { auth: AuthState }) => state.auth.error.login,
  getLogoutError: (state: { auth: AuthState }) => state.auth.error.logout,
  getRegisterError: (state: { auth: AuthState }) => state.auth.error.register,
  getRefreshError: (state: { auth: AuthState }) => state.auth.error.refresh,
  getGeneralError: (state: { auth: AuthState }) => state.auth.error.general,
  
  // Session selectors
  getSession: (state: { auth: AuthState }) => state.auth.session,
  getSessionId: (state: { auth: AuthState }) => state.auth.session.id,
  getLastActivity: (state: { auth: AuthState }) => state.auth.session.lastActivity,
  getTokenExpiresAt: (state: { auth: AuthState }) => state.auth.tokenExpiresAt,
  
  // UI selectors
  getRedirectUrl: (state: { auth: AuthState }) => state.auth.ui.redirectUrl,
  getRememberMe: (state: { auth: AuthState }) => state.auth.ui.rememberMe,
  getShowWelcomeMessage: (state: { auth: AuthState }) => state.auth.ui.showWelcomeMessage,
  getLastLoginMethod: (state: { auth: AuthState }) => state.auth.ui.lastLoginMethod,
  
  // Computed selectors
  getIsTokenExpired: (state: { auth: AuthState }) => {
    const expiresAt = state.auth.tokenExpiresAt;
    return expiresAt ? Date.now() >= expiresAt : false;
  },
  
  getIsTokenExpiringSoon: (state: { auth: AuthState }, threshold: number = 300000) => {
    const expiresAt = state.auth.tokenExpiresAt;
    return expiresAt ? Date.now() >= (expiresAt - threshold) : false;
  },
  
  getHasPermission: (state: { auth: AuthState }, permission: string) => 
    state.auth.permissions.includes(permission),
  
  getHasAnyPermission: (state: { auth: AuthState }, permissions: string[]) => 
    permissions.some(permission => state.auth.permissions.includes(permission)),
  
  getHasAllPermissions: (state: { auth: AuthState }, permissions: string[]) => 
    permissions.every(permission => state.auth.permissions.includes(permission)),
  
  getUserRole: (state: { auth: AuthState }) => 
    state.auth.user?.role || null,
  
  getUserDisplayName: (state: { auth: AuthState }) => {
    const user = state.auth.user;
    if (!user) return null;
    return user.firstName || user.email;
  },
  
  getSessionTimeRemaining: (state: { auth: AuthState }) => {
    const expiresAt = state.auth.session.expiresAt;
    return expiresAt ? Math.max(0, expiresAt - Date.now()) : 0;
  },
  
  getIsSessionActive: (state: { auth: AuthState }) => {
    const lastActivity = state.auth.session.lastActivity;
    const sessionTimeout = 30 * 60 * 1000; // 30 minutes
    return lastActivity ? Date.now() - lastActivity < sessionTimeout : false;
  }
};

/**
 * Development Notes:
 * 
 * 1. State Design:
 *    - Comprehensive authentication state structure
 *    - Separate loading states for different operations
 *    - Detailed error handling with operation-specific errors
 *    - Session management with activity tracking
 * 
 * 2. Immutability:
 *    - All state updates are immutable
 *    - Helper functions for common state transformations
 *    - Proper spreading of nested objects
 *    - No direct state mutations
 * 
 * 3. Security:
 *    - Token expiration tracking
 *    - Session timeout management
 *    - Permission-based access control
 *    - Secure state clearing on logout
 * 
 * 4. Performance:
 *    - Efficient state updates
 *    - Memoized selector functions
 *    - Minimal state changes
 *    - Optimized re-renders
 * 
 * 5. Error Handling:
 *    - Operation-specific error states
 *    - Detailed error information
 *    - Error clearing mechanisms
 *    - Graceful error recovery
 * 
 * 6. User Experience:
 *    - Loading states for UI feedback
 *    - Welcome message management
 *    - Redirect URL handling
 *    - Remember me functionality
 * 
 * Usage Examples:
 * ```tsx
 * // In components
 * import { useSelector } from 'react-redux';
 * import { authSelectors } from '@/store/reducers/auth';
 * 
 * const UserProfile = () => {
 *   const user = useSelector(authSelectors.getUser);
 *   const isLoading = useSelector(authSelectors.getIsLoading);
 *   const displayName = useSelector(authSelectors.getUserDisplayName);
 *   
 *   if (isLoading) return <div>Loading...</div>;
 *   if (!user) return <div>Not authenticated</div>;
 *   
 *   return <div>Welcome, {displayName}!</div>;
 * };
 * 
 * // Permission checking
 * const AdminPanel = () => {
 *   const hasAdminPermission = useSelector(state => 
 *     authSelectors.getHasPermission(state, 'admin')
 *   );
 *   
 *   if (!hasAdminPermission) {
 *     return <div>Access denied</div>;
 *   }
 *   
 *   return <AdminDashboard />;
 * };
 * 
 * // Token expiration checking
 * const TokenStatus = () => {
 *   const isExpired = useSelector(authSelectors.getIsTokenExpired);
 *   const isExpiringSoon = useSelector(state => 
 *     authSelectors.getIsTokenExpiringSoon(state, 600000) // 10 minutes
 *   );
 *   
 *   if (isExpired) return <div>Session expired</div>;
 *   if (isExpiringSoon) return <div>Session expiring soon</div>;
 *   return <div>Session active</div>;
 * };
 * ```
 */