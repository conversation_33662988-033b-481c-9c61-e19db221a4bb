/**
 * Store Reducers Index
 * 
 * This file serves as a central export hub for all Redux reducers.
 * It provides a unified interface for accessing reducers, combined state types,
 * and root reducer configuration with comprehensive type safety.
 * 
 * Key Features:
 * - Centralized reducer exports
 * - Combined state type definitions
 * - Root reducer configuration
 * - Selector type helpers
 * - Development utilities
 * - Performance optimizations
 * 
 * Usage Examples:
 * ```tsx
 * // In store configuration
 * import { rootReducer, RootState } from '@/store/reducers';
 * 
 * const store = configureStore({
 *   reducer: rootReducer,
 *   middleware: (getDefaultMiddleware) => 
 *     getDefaultMiddleware({
 *       serializableCheck: {
 *         ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER]
 *       }
 *     })
 * });
 * 
 * // In components with typed selectors
 * import { useSelector } from 'react-redux';
 * import { RootState } from '@/store/reducers';
 * 
 * const MyComponent = () => {
 *   const user = useSelector((state: RootState) => state.auth.user);
 *   const courses = useSelector((state: RootState) => state.course.courses);
 *   
 *   return <div>{user?.name} has {courses.length} courses</div>;
 * };
 * ```
 */

// import { combineReducers } from '@reduxjs/toolkit';
// import type { Action, AnyAction } from '@reduxjs/toolkit';

// // ===== REDUCER IMPORTS =====

// import { 
//   authReducer, 
//   type AuthState, 
//   initialAuthState,
//   authSelectors 
// } from './auth';
// import { 
//   courseReducer, 
//   type CourseState, 
//   initialCourseState,
//   courseSelectors 
// } from './course';

// // ===== STATE TYPE DEFINITIONS =====

// /**
//  * Root state interface combining all reducer states
//  */
// export interface RootState {
//   auth: AuthState;
//   course: CourseState;
// }

// /**
//  * Initial root state
//  */
// export const initialRootState: RootState = {
//   auth: initialAuthState,
//   course: initialCourseState
// };

// // ===== ROOT REDUCER =====

// /**
//  * Combined root reducer
//  */
// export const rootReducer = combineReducers({
//   auth: authReducer,
//   course: courseReducer
// });

// /**
//  * Root reducer with reset capability
//  * Allows resetting the entire state to initial values
//  */
// export const rootReducerWithReset = (
//   state: RootState | undefined, 
//   action: AnyAction
// ): RootState => {
//   // Reset state on logout or explicit reset action
//   if (action.type === 'RESET_STORE' || action.type === 'auth/logout/fulfilled') {
//     // Preserve certain state that should survive logout
//     const preservedState: Partial<RootState> = {
//       course: {
//         ...initialCourseState,
//         // Preserve UI preferences
//         ui: state?.course.ui || initialCourseState.ui,
//         // Preserve cache settings
//         cache: state?.course.cache || initialCourseState.cache
//       }
//     };
    
//     return rootReducer(preservedState as RootState, action as AuthAction | CourseAction);
//   }
  
//   return rootReducer(state, action);
// };

// // ===== SELECTOR EXPORTS =====

// /**
//  * Combined selectors from all reducers
//  */
// export const selectors = {
//   auth: authSelectors,
//   course: courseSelectors
// };

// // ===== TYPE HELPERS =====

// /**
//  * Type helper for useSelector hook
//  */
// export type RootStateSelector<T> = (state: RootState) => T;

// /**
//  * Type helper for action creators
//  */
// export type AppAction = Action<string>;

// /**
//  * Type helper for thunk actions
//  */
// export type AppThunk<ReturnType = void> = (
//   dispatch: any,
//   getState: () => RootState
// ) => ReturnType;

// /**
//  * Type helper for async thunk actions
//  */
// export type AppAsyncThunk<ReturnType = void> = (
//   dispatch: any,
//   getState: () => RootState
// ) => Promise<ReturnType>;

// // ===== UTILITY FUNCTIONS =====

// /**
//  * Get initial state for a specific reducer
//  */
// export const getInitialState = <K extends keyof RootState>(reducerKey: K): RootState[K] => {
//   return initialRootState[reducerKey];
// };

// /**
//  * Check if state is in initial state
//  */
// export const isInitialState = (state: RootState): boolean => {
//   return JSON.stringify(state) === JSON.stringify(initialRootState);
// };

// /**
//  * Get state slice
//  */
// export const getStateSlice = <K extends keyof RootState>(
//   state: RootState, 
//   sliceKey: K
// ): RootState[K] => {
//   return state[sliceKey];
// };

// /**
//  * Create typed selector
//  */
// export const createTypedSelector = <T>(
//   selector: RootStateSelector<T>
// ): RootStateSelector<T> => {
//   return selector;
// };

// /**
//  * Combine multiple selectors
//  */
// export const combineSelectors = <T extends Record<string, any>>(
//   selectors: { [K in keyof T]: RootStateSelector<T[K]> }
// ): RootStateSelector<T> => {
//   return (state: RootState) => {
//     const result = {} as T;
//     for (const key in selectors) {
//       result[key] = selectors[key](state);
//     }
//     return result;
//   };
// };

// /**
//  * Create memoized selector
//  */
// export const createMemoizedSelector = <T>(
//   selector: RootStateSelector<T>,
//   equalityFn?: (a: T, b: T) => boolean
// ): RootStateSelector<T> => {
//   let lastResult: T;
//   let lastState: RootState;
  
//   return (state: RootState) => {
//     if (state !== lastState) {
//       const newResult = selector(state);
      
//       if (equalityFn) {
//         if (!equalityFn(lastResult, newResult)) {
//           lastResult = newResult;
//         }
//       } else {
//         lastResult = newResult;
//       }
      
//       lastState = state;
//     }
    
//     return lastResult;
//   };
// };

// // ===== COMMON SELECTORS =====

// /**
//  * Common cross-reducer selectors
//  */
// export const commonSelectors = {
//   /**
//    * Get loading state across all reducers
//    */
//   getGlobalLoading: createTypedSelector((state: RootState) => {
//     const authLoading = Object.values(state.auth.loading).some(loading => loading);
//     const courseLoading = Object.values(state.course.loading).some(loading => loading);
    
//     return authLoading || courseLoading;
//   }),
  
//   /**
//    * Get error state across all reducers
//    */
//   getGlobalError: createTypedSelector((state: RootState) => {
//     const authErrors = Object.values(state.auth.error).filter(error => error !== null);
//     const courseErrors = Object.values(state.course.error).filter(error => error !== null);
    
//     return [...authErrors, ...courseErrors];
//   }),
  
//   /**
//    * Get user's enrolled courses with progress
//    */
//   getEnrolledCoursesWithProgress: createTypedSelector((state: RootState) => {
//     const enrolledCourseIds = state.course.enrolledCourseIds;
//     const courses = state.course.courses;
//     const progress = state.course.progress;
    
//     return enrolledCourseIds.map(courseId => {
//       const course = courses.find(c => c.id === courseId);
//       const courseProgress = progress[courseId];
      
//       return {
//         course,
//         progress: courseProgress
//       };
//     }).filter(item => item.course !== undefined);
//   }),
  
//   /**
//    * Get user dashboard data
//    */
//   getDashboardData: createTypedSelector((state: RootState) => {
//     const user = state.auth.user;
//     const enrolledCourses = state.course.enrolledCourseIds.length;
//     const completedCourses = Object.values(state.course.progress).filter(
//       p => p.completionPercentage === 100
//     ).length;
//     const inProgressCourses = Object.values(state.course.progress).filter(
//       p => p.completionPercentage > 0 && p.completionPercentage < 100
//     ).length;
    
//     return {
//       user,
//       stats: {
//         enrolledCourses,
//         completedCourses,
//         inProgressCourses,
//         completionRate: enrolledCourses > 0 ? (completedCourses / enrolledCourses) * 100 : 0
//       }
//     };
//   }),
  
//   /**
//    * Get app initialization status
//    */
//   getAppInitialized: createTypedSelector((state: RootState) => {
//     const authInitialized = state.auth.user !== null || !state.auth.loading.restore;
//     const courseDataLoaded = state.course.courses.length > 0 || !state.course.loading.fetchCourses;
    
//     return authInitialized && courseDataLoaded;
//   }),
  
//   /**
//    * Get app health status
//    */
//   getAppHealth: createTypedSelector((state: RootState) => {
//     const hasAuthErrors = Object.values(state.auth.error).some(error => error !== null);
//     const hasCourseErrors = Object.values(state.course.error).some(error => error !== null);
//     const isLoading = commonSelectors.getGlobalLoading(state);
    
//     return {
//       healthy: !hasAuthErrors && !hasCourseErrors,
//       loading: isLoading,
//       errors: {
//         auth: hasAuthErrors,
//         course: hasCourseErrors
//       }
//     };
//   }),
  
//   /**
//    * Get user permissions for course actions
//    */
//   getCoursePermissions: createTypedSelector((state: RootState) => {
//     const permissions = state.auth.permissions;
//     const userRole = state.auth.user?.role;
    
//     return {
//       canCreateCourse: permissions.includes('course:create') || userRole === 'admin',
//       canUpdateCourse: permissions.includes('course:update') || userRole === 'admin',
//       canDeleteCourse: permissions.includes('course:delete') || userRole === 'admin',
//       canEnrollCourse: permissions.includes('course:enroll') || userRole !== 'guest',
//       canViewCourse: permissions.includes('course:view') || userRole !== 'guest'
//     };
//   }),
  
//   /**
//    * Get recent activity
//    */
//   getRecentActivity: createTypedSelector((state: RootState) => {
//     const lastAuthUpdate = state.auth.meta.lastUpdated;
//     const lastCourseUpdate = state.course.meta.lastUpdated;
//     const lastSessionActivity = state.auth.session.lastActivity;
    
//     return {
//       lastAuthUpdate,
//       lastCourseUpdate,
//       lastSessionActivity,
//       lastActivity: Math.max(lastAuthUpdate, lastCourseUpdate, lastSessionActivity || 0)
//     };
//   })
// };

// // ===== DEVELOPMENT UTILITIES =====

// /**
//  * Development utilities for debugging and testing
//  */
// export const devUtils = {
//   /**
//    * Log current state
//    */
//   logState: (state: RootState, label?: string) => {
//     if (process.env.NODE_ENV === 'development') {
//       console.group(`🔍 State ${label ? `(${label})` : ''}`);
//       console.log('Auth State:', state.auth);
//       console.log('Course State:', state.course);
//       console.groupEnd();
//     }
//   },
  
//   /**
//    * Get state size (approximate)
//    */
//   getStateSize: (state: RootState): number => {
//     return JSON.stringify(state).length;
//   },
  
//   /**
//    * Validate state structure
//    */
//   validateState: (state: RootState): boolean => {
//     try {
//       // Check required properties
//       const hasAuth = state.auth && typeof state.auth === 'object';
//       const hasCourse = state.course && typeof state.course === 'object';
      
//       // Check auth structure
//       const authValid = hasAuth && 
//         typeof state.auth.isAuthenticated === 'boolean' &&
//         typeof state.auth.loading === 'object' &&
//         typeof state.auth.error === 'object';
      
//       // Check course structure
//       const courseValid = hasCourse &&
//         Array.isArray(state.course.courses) &&
//         typeof state.course.loading === 'object' &&
//         typeof state.course.error === 'object';
      
//       return authValid && courseValid;
//     } catch (error) {
//       console.error('State validation error:', error);
//       return false;
//     }
//   },
  
//   /**
//    * Get state diff
//    */
//   getStateDiff: (prevState: RootState, nextState: RootState): Partial<RootState> => {
//     const diff: Partial<RootState> = {};
    
//     // Compare auth state
//     if (JSON.stringify(prevState.auth) !== JSON.stringify(nextState.auth)) {
//       diff.auth = nextState.auth;
//     }
    
//     // Compare course state
//     if (JSON.stringify(prevState.course) !== JSON.stringify(nextState.course)) {
//       diff.course = nextState.course;
//     }
    
//     return diff;
//   },
  
//   /**
//    * Reset specific reducer
//    */
//   resetReducer: <K extends keyof RootState>(reducerKey: K) => {
//     return {
//       type: `${reducerKey}/reset`,
//       payload: getInitialState(reducerKey)
//     };
//   },
  
//   /**
//    * Get performance metrics
//    */
//   getPerformanceMetrics: (state: RootState) => {
//     const stateSize = devUtils.getStateSize(state);
//     const courseCount = state.course.courses.length;
//     const enrollmentCount = state.course.enrollments.length;
//     const progressCount = Object.keys(state.course.progress).length;
    
//     return {
//       stateSize,
//       courseCount,
//       enrollmentCount,
//       progressCount,
//       memoryUsage: {
//         courses: JSON.stringify(state.course.courses).length,
//         enrollments: JSON.stringify(state.course.enrollments).length,
//         progress: JSON.stringify(state.course.progress).length,
//         auth: JSON.stringify(state.auth).length
//       }
//     };
//   }
// };

// // ===== EXPORTS =====

// // Re-export individual reducers and their types
// export {
//   authReducer,
//   courseReducer,
//   type AuthState,
//   type CourseState,
//   initialAuthState,
//   initialCourseState,
//   authSelectors,
//   courseSelectors
// };

// // Export action types for reference
// export type { AppAction, AppThunk, AppAsyncThunk, RootStateSelector };

/**
 * Development Notes:
 * 
 * 1. Architecture:
 *    - Centralized reducer management
 *    - Type-safe state definitions
 *    - Comprehensive selector exports
 *    - Development utilities
 *    - Performance monitoring
 * 
 * 2. Type Safety:
 *    - Strict TypeScript interfaces
 *    - Typed selectors and actions
 *    - Generic helper functions
 *    - Runtime validation utilities
 * 
 * 3. Performance:
 *    - Memoized selectors
 *    - Efficient state updates
 *    - Memory usage monitoring
 *    - State size tracking
 * 
 * 4. Development Experience:
 *    - Debugging utilities
 *    - State validation
 *    - Performance metrics
 *    - Error tracking
 * 
 * 5. Maintainability:
 *    - Modular structure
 *    - Clear separation of concerns
 *    - Comprehensive documentation
 *    - Consistent patterns
 * 
 * Usage Examples:
 * ```tsx
 * // Basic usage
 * import { useSelector } from 'react-redux';
 * import { RootState, selectors } from '@/store/reducers';
 * 
 * const MyComponent = () => {
 *   const user = useSelector(selectors.auth.getUser);
 *   const courses = useSelector(selectors.course.getCourses);
 *   const isLoading = useSelector(commonSelectors.getGlobalLoading);
 *   
 *   return (
 *     <div>
 *       {isLoading ? 'Loading...' : `${user?.name} has ${courses.length} courses`}
 *     </div>
 *   );
 * };
 * 
 * // Advanced usage with combined selectors
 * const dashboardSelector = combineSelectors({
 *   user: selectors.auth.getUser,
 *   enrolledCourses: selectors.course.getEnrolledCourses,
 *   progress: selectors.course.getProgress,
 *   loading: commonSelectors.getGlobalLoading
 * });
 * 
 * const Dashboard = () => {
 *   const { user, enrolledCourses, progress, loading } = useSelector(dashboardSelector);
 *   
 *   if (loading) return <LoadingSpinner />;
 *   
 *   return (
 *     <div>
 *       <h1>Welcome, {user?.name}!</h1>
 *       <p>You have {enrolledCourses.length} enrolled courses</p>
 *     </div>
 *   );
 * };
 * 
 * // Development debugging
 * import { devUtils } from '@/store/reducers';
 * 
 * const DebugComponent = () => {
 *   const state = useSelector((state: RootState) => state);
 *   
 *   useEffect(() => {
 *     devUtils.logState(state, 'Component Mount');
 *     console.log('Performance:', devUtils.getPerformanceMetrics(state));
 *   }, [state]);
 *   
 *   return <div>Debug info logged to console</div>;
 * };
 * ```
 */