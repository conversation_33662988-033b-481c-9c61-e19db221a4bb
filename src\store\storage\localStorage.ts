/**
 * Local Storage Utilities
 * 
 * This file provides utilities for persisting Redux state to localStorage
 * with comprehensive error handling, data validation, and performance optimization.
 * 
 * Key Features:
 * - Type-safe storage operations
 * - Data serialization/deserialization
 * - Error handling and recovery
 * - Storage quota management
 * - Data migration support
 * - Performance optimization
 * - Security considerations
 * 
 * Usage Examples:
 * ```tsx
 * // Basic usage
 * import { localStorageUtils } from '@/store/storage/localStorage';
 * 
 * // Save state
 * localStorageUtils.saveState('auth', authState);
 * 
 * // Load state
 * const savedAuthState = localStorageUtils.loadState('auth');
 * 
 * // In store configuration
 * import { persistConfig } from '@/store/storage/localStorage';
 * 
 * const persistedReducer = persistReducer(persistConfig, rootReducer);
 * ```
 */

// import type { AuthState } from '@/store/reducers/auth';
// import type { CourseState } from '@/store/reducers/course';
// import type { RootState } from '@/store/reducers';

// // ===== TYPE DEFINITIONS =====

// /**
//  * Storage key enum for type safety
//  */
// export enum StorageKeys {
//   AUTH_STATE = 'lemon_auth_state',
//   COURSE_STATE = 'lemon_course_state',
//   USER_PREFERENCES = 'lemon_user_preferences',
//   APP_SETTINGS = 'lemon_app_settings',
//   CACHE_DATA = 'lemon_cache_data',
//   SESSION_DATA = 'lemon_session_data'
// }

// /**
//  * Storage configuration interface
//  */
// export interface StorageConfig {
//   key: string;
//   version: number;
//   migrate?: (persistedState: any, version: number) => any;
//   serialize?: (data: any) => string;
//   deserialize?: (data: string) => any;
//   whitelist?: string[];
//   blacklist?: string[];
//   ttl?: number; // time to live in milliseconds
// }

// /**
//  * Storage result interface
//  */
// export interface StorageResult<T> {
//   success: boolean;
//   data?: T;
//   error?: string;
//   version?: number;
//   timestamp?: number;
// }

// /**
//  * Storage metadata interface
//  */
// export interface StorageMetadata {
//   version: number;
//   timestamp: number;
//   size: number;
//   checksum?: string;
//   ttl?: number;
// }

// /**
//  * Storage item interface
//  */
// export interface StorageItem<T> {
//   data: T;
//   metadata: StorageMetadata;
// }

// // ===== CONSTANTS =====

// /**
//  * Storage configuration constants
//  */
// export const STORAGE_CONFIG = {
//   VERSION: 1,
//   MAX_SIZE: 5 * 1024 * 1024, // 5MB
//   DEFAULT_TTL: 7 * 24 * 60 * 60 * 1000, // 7 days
//   COMPRESSION_THRESHOLD: 1024, // 1KB
//   RETRY_ATTEMPTS: 3,
//   RETRY_DELAY: 1000 // 1 second
// } as const;

// /**
//  * Storage error messages
//  */
// export const STORAGE_ERRORS = {
//   NOT_SUPPORTED: 'localStorage is not supported in this environment',
//   QUOTA_EXCEEDED: 'Storage quota exceeded',
//   INVALID_DATA: 'Invalid data format',
//   SERIALIZATION_ERROR: 'Failed to serialize data',
//   DESERIALIZATION_ERROR: 'Failed to deserialize data',
//   VERSION_MISMATCH: 'Version mismatch detected',
//   EXPIRED_DATA: 'Data has expired',
//   CHECKSUM_MISMATCH: 'Data integrity check failed'
// } as const;

// // ===== UTILITY FUNCTIONS =====

// /**
//  * Check if localStorage is available
//  */
// export const isLocalStorageAvailable = (): boolean => {
//   try {
//     if (typeof window === 'undefined' || !window.localStorage) {
//       return false;
//     }
    
//     const testKey = '__localStorage_test__';
//     window.localStorage.setItem(testKey, 'test');
//     window.localStorage.removeItem(testKey);
//     return true;
//   } catch (error) {
//     return false;
//   }
// };

// /**
//  * Get storage usage information
//  */
// export const getStorageUsage = (): { used: number; available: number; percentage: number } => {
//   if (!isLocalStorageAvailable()) {
//     return { used: 0, available: 0, percentage: 0 };
//   }
  
//   try {
//     let used = 0;
//     for (let key in localStorage) {
//       if (localStorage.hasOwnProperty(key)) {
//         used += localStorage[key].length + key.length;
//       }
//     }
    
//     const available = STORAGE_CONFIG.MAX_SIZE - used;
//     const percentage = (used / STORAGE_CONFIG.MAX_SIZE) * 100;
    
//     return { used, available, percentage };
//   } catch (error) {
//     return { used: 0, available: 0, percentage: 0 };
//   }
// };

// /**
//  * Generate checksum for data integrity
//  */
// export const generateChecksum = (data: string): string => {
//   let hash = 0;
//   for (let i = 0; i < data.length; i++) {
//     const char = data.charCodeAt(i);
//     hash = ((hash << 5) - hash) + char;
//     hash = hash & hash; // Convert to 32-bit integer
//   }
//   return hash.toString(36);
// };

// /**
//  * Compress data using simple compression
//  */
// export const compressData = (data: string): string => {
//   if (data.length < STORAGE_CONFIG.COMPRESSION_THRESHOLD) {
//     return data;
//   }
  
//   try {
//     // Simple compression using JSON.stringify optimization
//     return JSON.stringify(JSON.parse(data));
//   } catch (error) {
//     return data;
//   }
// };

// /**
//  * Decompress data
//  */
// export const decompressData = (data: string): string => {
//   try {
//     // If data is compressed, parse and stringify again
//     const parsed = JSON.parse(data);
//     return typeof parsed === 'string' ? parsed : JSON.stringify(parsed);
//   } catch (error) {
//     return data;
//   }
// };

// /**
//  * Clean expired items from storage
//  */
// export const cleanExpiredItems = (): void => {
//   if (!isLocalStorageAvailable()) return;
  
//   try {
//     const now = Date.now();
//     const keysToRemove: string[] = [];
    
//     for (let i = 0; i < localStorage.length; i++) {
//       const key = localStorage.key(i);
//       if (!key || !key.startsWith('lemon_')) continue;
      
//       try {
//         const item = localStorage.getItem(key);
//         if (!item) continue;
        
//         const parsed: StorageItem<any> = JSON.parse(item);
//         const { metadata } = parsed;
        
//         if (metadata.ttl && (metadata.timestamp + metadata.ttl) < now) {
//           keysToRemove.push(key);
//         }
//       } catch (error) {
//         // Remove corrupted items
//         keysToRemove.push(key);
//       }
//     }
    
//     keysToRemove.forEach(key => localStorage.removeItem(key));
//   } catch (error) {
//     console.warn('Failed to clean expired items:', error);
//   }
// };

// // ===== CORE STORAGE FUNCTIONS =====

// /**
//  * Save data to localStorage with metadata
//  */
// export const saveToStorage = <T>(
//   key: string, 
//   data: T, 
//   config: Partial<StorageConfig> = {}
// ): StorageResult<T> => {
//   if (!isLocalStorageAvailable()) {
//     return {
//       success: false,
//       error: STORAGE_ERRORS.NOT_SUPPORTED
//     };
//   }
  
//   try {
//     // Serialize data
//     const serializedData = config.serialize 
//       ? config.serialize(data) 
//       : JSON.stringify(data);
    
//     // Create metadata
//     const metadata: StorageMetadata = {
//       version: config.version || STORAGE_CONFIG.VERSION,
//       timestamp: Date.now(),
//       size: serializedData.length,
//       checksum: generateChecksum(serializedData),
//       ttl: config.ttl
//     };
    
//     // Create storage item
//     const storageItem: StorageItem<T> = {
//       data,
//       metadata
//     };
    
//     // Compress if needed
//     const finalData = compressData(JSON.stringify(storageItem));
    
//     // Check storage quota
//     const usage = getStorageUsage();
//     if (usage.used + finalData.length > STORAGE_CONFIG.MAX_SIZE) {
//       // Try to clean expired items first
//       cleanExpiredItems();
      
//       const newUsage = getStorageUsage();
//       if (newUsage.used + finalData.length > STORAGE_CONFIG.MAX_SIZE) {
//         return {
//           success: false,
//           error: STORAGE_ERRORS.QUOTA_EXCEEDED
//         };
//       }
//     }
    
//     // Save to localStorage
//     localStorage.setItem(key, finalData);
    
//     return {
//       success: true,
//       data,
//       version: metadata.version,
//       timestamp: metadata.timestamp
//     };
//   } catch (error) {
//     return {
//       success: false,
//       error: error instanceof Error ? error.message : STORAGE_ERRORS.SERIALIZATION_ERROR
//     };
//   }
// };

// /**
//  * Load data from localStorage with validation
//  */
// export const loadFromStorage = <T>(
//   key: string, 
//   config: Partial<StorageConfig> = {}
// ): StorageResult<T> => {
//   if (!isLocalStorageAvailable()) {
//     return {
//       success: false,
//       error: STORAGE_ERRORS.NOT_SUPPORTED
//     };
//   }
  
//   try {
//     const item = localStorage.getItem(key);
//     if (!item) {
//       return {
//         success: false,
//         error: 'Item not found'
//       };
//     }
    
//     // Decompress data
//     const decompressedData = decompressData(item);
    
//     // Parse storage item
//     const storageItem: StorageItem<T> = JSON.parse(decompressedData);
//     const { data, metadata } = storageItem;
    
//     // Validate version
//     const expectedVersion = config.version || STORAGE_CONFIG.VERSION;
//     if (metadata.version !== expectedVersion) {
//       if (config.migrate) {
//         const migratedData = config.migrate(data, metadata.version);
//         // Save migrated data
//         saveToStorage(key, migratedData, config);
//         return {
//           success: true,
//           data: migratedData,
//           version: expectedVersion,
//           timestamp: Date.now()
//         };
//       } else {
//         return {
//           success: false,
//           error: STORAGE_ERRORS.VERSION_MISMATCH
//         };
//       }
//     }
    
//     // Check TTL
//     if (metadata.ttl && (metadata.timestamp + metadata.ttl) < Date.now()) {
//       localStorage.removeItem(key);
//       return {
//         success: false,
//         error: STORAGE_ERRORS.EXPIRED_DATA
//       };
//     }
    
//     // Validate checksum
//     const serializedData = config.serialize 
//       ? config.serialize(data) 
//       : JSON.stringify(data);
//     const currentChecksum = generateChecksum(serializedData);
    
//     if (metadata.checksum && metadata.checksum !== currentChecksum) {
//       return {
//         success: false,
//         error: STORAGE_ERRORS.CHECKSUM_MISMATCH
//       };
//     }
    
//     // Deserialize if needed
//     const finalData = config.deserialize 
//       ? config.deserialize(serializedData) 
//       : data;
    
//     return {
//       success: true,
//       data: finalData,
//       version: metadata.version,
//       timestamp: metadata.timestamp
//     };
//   } catch (error) {
//     return {
//       success: false,
//       error: error instanceof Error ? error.message : STORAGE_ERRORS.DESERIALIZATION_ERROR
//     };
//   }
// };

// /**
//  * Remove data from localStorage
//  */
// export const removeFromStorage = (key: string): StorageResult<void> => {
//   if (!isLocalStorageAvailable()) {
//     return {
//       success: false,
//       error: STORAGE_ERRORS.NOT_SUPPORTED
//     };
//   }
  
//   try {
//     localStorage.removeItem(key);
//     return { success: true };
//   } catch (error) {
//     return {
//       success: false,
//       error: error instanceof Error ? error.message : 'Failed to remove item'
//     };
//   }
// };

// /**
//  * Clear all app-related data from localStorage
//  */
// export const clearAppStorage = (): StorageResult<void> => {
//   if (!isLocalStorageAvailable()) {
//     return {
//       success: false,
//       error: STORAGE_ERRORS.NOT_SUPPORTED
//     };
//   }
  
//   try {
//     const keysToRemove: string[] = [];
    
//     for (let i = 0; i < localStorage.length; i++) {
//       const key = localStorage.key(i);
//       if (key && key.startsWith('lemon_')) {
//         keysToRemove.push(key);
//       }
//     }
    
//     keysToRemove.forEach(key => localStorage.removeItem(key));
    
//     return { success: true };
//   } catch (error) {
//     return {
//       success: false,
//       error: error instanceof Error ? error.message : 'Failed to clear storage'
//     };
//   }
// };

// // ===== SPECIALIZED STORAGE FUNCTIONS =====

// /**
//  * Local storage utilities for specific state slices
//  */
// export const localStorageUtils = {
//   /**
//    * Save auth state
//    */
//   saveAuthState: (state: Partial<AuthState>): StorageResult<Partial<AuthState>> => {
//     // Filter sensitive data
//     const filteredState = {
//       user: state.user,
//       isAuthenticated: state.isAuthenticated,
//       isEmailVerified: state.isEmailVerified,
//       permissions: state.permissions,
//       ui: state.ui,
//       // Don't persist tokens for security
//       accessToken: null,
//       refreshToken: null
//     };
    
//     return saveToStorage(StorageKeys.AUTH_STATE, filteredState, {
//       version: 1,
//       ttl: STORAGE_CONFIG.DEFAULT_TTL
//     });
//   },
  
//   /**
//    * Load auth state
//    */
//   loadAuthState: (): StorageResult<Partial<AuthState>> => {
//     return loadFromStorage<Partial<AuthState>>(StorageKeys.AUTH_STATE, {
//       version: 1,
//       migrate: (persistedState: any, version: number) => {
//         // Handle migration from older versions
//         if (version < 1) {
//           return {
//             ...persistedState,
//             ui: {
//               redirectUrl: null,
//               rememberMe: false,
//               showWelcomeMessage: false,
//               lastLoginMethod: null
//             }
//           };
//         }
//         return persistedState;
//       }
//     });
//   },
  
//   /**
//    * Save course state
//    */
//   saveCourseState: (state: Partial<CourseState>): StorageResult<Partial<CourseState>> => {
//     // Filter large data that shouldn't be persisted
//     const filteredState = {
//       enrolledCourseIds: state.enrolledCourseIds,
//       progress: state.progress,
//       filters: state.filters,
//       ui: state.ui,
//       // Don't persist large course arrays
//       courses: [],
//       courseSummaries: [],
//       searchResults: []
//     };
    
//     return saveToStorage(StorageKeys.COURSE_STATE, filteredState, {
//       version: 1,
//       ttl: STORAGE_CONFIG.DEFAULT_TTL
//     });
//   },
  
//   /**
//    * Load course state
//    */
//   loadCourseState: (): StorageResult<Partial<CourseState>> => {
//     return loadFromStorage<Partial<CourseState>>(StorageKeys.COURSE_STATE, {
//       version: 1,
//       migrate: (persistedState: any, version: number) => {
//         // Handle migration from older versions
//         if (version < 1) {
//           return {
//             ...persistedState,
//             ui: {
//               selectedCourseIds: [],
//               viewMode: 'grid',
//               sortBy: 'createdAt',
//               sortOrder: 'desc',
//               showFilters: false,
//               showEnrolledOnly: false,
//               showCompletedOnly: false
//             }
//           };
//         }
//         return persistedState;
//       }
//     });
//   },
  
//   /**
//    * Save user preferences
//    */
//   saveUserPreferences: (preferences: any): StorageResult<any> => {
//     return saveToStorage(StorageKeys.USER_PREFERENCES, preferences, {
//       version: 1,
//       ttl: 30 * 24 * 60 * 60 * 1000 // 30 days
//     });
//   },
  
//   /**
//    * Load user preferences
//    */
//   loadUserPreferences: (): StorageResult<any> => {
//     return loadFromStorage(StorageKeys.USER_PREFERENCES, {
//       version: 1
//     });
//   },
  
//   /**
//    * Save app settings
//    */
//   saveAppSettings: (settings: any): StorageResult<any> => {
//     return saveToStorage(StorageKeys.APP_SETTINGS, settings, {
//       version: 1
//     });
//   },
  
//   /**
//    * Load app settings
//    */
//   loadAppSettings: (): StorageResult<any> => {
//     return loadFromStorage(StorageKeys.APP_SETTINGS, {
//       version: 1
//     });
//   },
  
//   /**
//    * Save cache data
//    */
//   saveCacheData: (cacheData: any): StorageResult<any> => {
//     return saveToStorage(StorageKeys.CACHE_DATA, cacheData, {
//       version: 1,
//       ttl: 60 * 60 * 1000 // 1 hour
//     });
//   },
  
//   /**
//    * Load cache data
//    */
//   loadCacheData: (): StorageResult<any> => {
//     return loadFromStorage(StorageKeys.CACHE_DATA, {
//       version: 1
//     });
//   },
  
//   /**
//    * Save session data
//    */
//   saveSessionData: (sessionData: any): StorageResult<any> => {
//     return saveToStorage(StorageKeys.SESSION_DATA, sessionData, {
//       version: 1,
//       ttl: 24 * 60 * 60 * 1000 // 24 hours
//     });
//   },
  
//   /**
//    * Load session data
//    */
//   loadSessionData: (): StorageResult<any> => {
//     return loadFromStorage(StorageKeys.SESSION_DATA, {
//       version: 1
//     });
//   }
// };

// // ===== REDUX PERSIST CONFIGURATION =====

// /**
//  * Redux persist configuration for auth state
//  */
// export const authPersistConfig = {
//   key: 'auth',
//   storage: {
//     getItem: (key: string) => {
//       const result = localStorageUtils.loadAuthState();
//       return Promise.resolve(result.success ? JSON.stringify(result.data) : null);
//     },
//     setItem: (key: string, value: string) => {
//       const data = JSON.parse(value);
//       localStorageUtils.saveAuthState(data);
//       return Promise.resolve();
//     },
//     removeItem: (key: string) => {
//       removeFromStorage(StorageKeys.AUTH_STATE);
//       return Promise.resolve();
//     }
//   },
//   whitelist: ['user', 'isAuthenticated', 'isEmailVerified', 'permissions', 'ui'],
//   blacklist: ['loading', 'error', 'accessToken', 'refreshToken', 'session']
// };

// /**
//  * Redux persist configuration for course state
//  */
// export const coursePersistConfig = {
//   key: 'course',
//   storage: {
//     getItem: (key: string) => {
//       const result = localStorageUtils.loadCourseState();
//       return Promise.resolve(result.success ? JSON.stringify(result.data) : null);
//     },
//     setItem: (key: string, value: string) => {
//       const data = JSON.parse(value);
//       localStorageUtils.saveCourseState(data);
//       return Promise.resolve();
//     },
//     removeItem: (key: string) => {
//       removeFromStorage(StorageKeys.COURSE_STATE);
//       return Promise.resolve();
//     }
//   },
//   whitelist: ['enrolledCourseIds', 'progress', 'filters', 'ui'],
//   blacklist: ['courses', 'courseSummaries', 'currentCourse', 'searchResults', 'loading', 'error', 'cache']
// };

// /**
//  * Main persist configuration
//  */
// export const persistConfig = {
//   key: 'root',
//   storage: {
//     getItem: (key: string) => {
//       // Custom storage implementation
//       return Promise.resolve(localStorage.getItem(key));
//     },
//     setItem: (key: string, value: string) => {
//       localStorage.setItem(key, value);
//       return Promise.resolve();
//     },
//     removeItem: (key: string) => {
//       localStorage.removeItem(key);
//       return Promise.resolve();
//     }
//   },
//   whitelist: ['auth', 'course'],
//   transforms: [
//     // Add transforms for data processing if needed
//   ]
// };

// // ===== DEVELOPMENT UTILITIES =====

// /**
//  * Development utilities for storage debugging
//  */
// export const storageDevUtils = {
//   /**
//    * Log storage usage
//    */
//   logStorageUsage: () => {
//     if (process.env.NODE_ENV === 'development') {
//       const usage = getStorageUsage();
//       console.group('📦 Storage Usage');
//       console.log(`Used: ${(usage.used / 1024).toFixed(2)} KB`);
//       console.log(`Available: ${(usage.available / 1024).toFixed(2)} KB`);
//       console.log(`Percentage: ${usage.percentage.toFixed(2)}%`);
//       console.groupEnd();
//     }
//   },
  
//   /**
//    * List all app storage keys
//    */
//   listAppStorageKeys: (): string[] => {
//     const keys: string[] = [];
//     for (let i = 0; i < localStorage.length; i++) {
//       const key = localStorage.key(i);
//       if (key && key.startsWith('lemon_')) {
//         keys.push(key);
//       }
//     }
//     return keys;
//   },
  
//   /**
//    * Get storage item info
//    */
//   getStorageItemInfo: (key: string) => {
//     const item = localStorage.getItem(key);
//     if (!item) return null;
    
//     try {
//       const parsed: StorageItem<any> = JSON.parse(item);
//       return {
//         key,
//         size: item.length,
//         metadata: parsed.metadata,
//         dataType: typeof parsed.data
//       };
//     } catch (error) {
//       return {
//         key,
//         size: item.length,
//         error: 'Failed to parse'
//       };
//     }
//   },
  
//   /**
//    * Export storage data
//    */
//   exportStorageData: () => {
//     const data: Record<string, any> = {};
//     const keys = storageDevUtils.listAppStorageKeys();
    
//     keys.forEach(key => {
//       const item = localStorage.getItem(key);
//       if (item) {
//         try {
//           data[key] = JSON.parse(item);
//         } catch (error) {
//           data[key] = item;
//         }
//       }
//     });
    
//     return data;
//   },
  
//   /**
//    * Import storage data
//    */
//   importStorageData: (data: Record<string, any>) => {
//     Object.entries(data).forEach(([key, value]) => {
//       try {
//         const serialized = typeof value === 'string' ? value : JSON.stringify(value);
//         localStorage.setItem(key, serialized);
//       } catch (error) {
//         console.warn(`Failed to import ${key}:`, error);
//       }
//     });
//   }
// };

/**
 * Development Notes:
 * 
 * 1. Security:
 *    - Sensitive data (tokens) are not persisted
 *    - Data integrity checks with checksums
 *    - TTL for automatic expiration
 *    - Version control for migrations
 * 
 * 2. Performance:
 *    - Compression for large data
 *    - Quota management
 *    - Lazy loading and cleanup
 *    - Efficient serialization
 * 
 * 3. Reliability:
 *    - Error handling and recovery
 *    - Data validation
 *    - Migration support
 *    - Fallback mechanisms
 * 
 * 4. Development:
 *    - Debugging utilities
 *    - Storage monitoring
 *    - Data export/import
 *    - Usage analytics
 * 
 * Usage Examples:
 * ```tsx
 * // Basic usage
 * import { localStorageUtils } from '@/store/storage/localStorage';
 * 
 * // Save auth state
 * const result = localStorageUtils.saveAuthState(authState);
 * if (result.success) {
 *   console.log('Auth state saved successfully');
 * } else {
 *   console.error('Failed to save auth state:', result.error);
 * }
 * 
 * // Load auth state
 * const loadResult = localStorageUtils.loadAuthState();
 * if (loadResult.success && loadResult.data) {
 *   // Use loaded state
 *   dispatch(restoreAuthState(loadResult.data));
 * }
 * 
 * // Development debugging
 * import { storageDevUtils } from '@/store/storage/localStorage';
 * 
 * // Log storage usage
 * storageDevUtils.logStorageUsage();
 * 
 * // Export data for debugging
 * const exportedData = storageDevUtils.exportStorageData();
 * console.log('Exported storage data:', exportedData);
 * ```
 */