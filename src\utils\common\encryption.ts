import CryptoJS from "crypto-js";

const secretKey = process.env.NEXT_PUBLIC_SECRET_KEY || "secret";
const saltString = process.env.NEXT_PUBLIC_SALT_KEY || "salt";

const saltHex = Buffer.from(saltString, "utf8").toString("hex");
const salt = CryptoJS.enc.Hex.parse(saltHex);

const key = CryptoJS.PBKDF2(secretKey, salt, {
  keySize: 256 / 32,
  iterations: 65536,
  hasher: CryptoJS.algo.SHA256,
});

const iv = CryptoJS.lib.WordArray.create([
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
]);

export function encryptAES(strToEncrypt: string) {
  try {
    const encrypted = CryptoJS.AES.encrypt(
      CryptoJS.enc.Utf8.parse(strToEncrypt),
      key,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      }
    );
    return encrypted.toString();
  } catch (error) {
    return `[CRYPTO] - Error encryption: ${error}`;
  }
}

export function decryptAES(strToDecrypt: string) {
  try {
    const decrypted = CryptoJS.AES.decrypt(strToDecrypt, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    return `[CRYPTO] - Error decryption: ${error}`;
  }
}
