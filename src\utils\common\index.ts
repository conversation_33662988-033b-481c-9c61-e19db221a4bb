/**
 * Common Utilities Index
 * 
 * This file serves as a central export hub for all common utility functions.
 * It provides a unified interface for accessing helper functions, formatters,
 * validators, and other utility functions used throughout the application.
 * 
 * Key Features:
 * - CSS class utilities
 * - String manipulation utilities
 * - Number formatting and validation
 * - Date and time utilities
 * - Object and array helpers
 * - URL and path utilities
 * - Validation functions
 * - Type guards and assertions
 * - Performance utilities
 * 
 * Usage Examples:
 * ```tsx
 * import { 
 *   cn,
 *   formatCurrency, 
 *   validateEmail, 
 *   debounce, 
 *   deepClone 
 * } from '@/utils/common';
 * 
 * const classes = cn('base-class', condition && 'conditional-class');
 * const price = formatCurrency(29.99, 'USD');
 * const isValid = validateEmail('<EMAIL>');
 * const debouncedFn = debounce(searchFunction, 300);
 * const clonedObj = deepClone(originalObject);
 * ```
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// ===== CSS UTILITIES =====

/**
 * Combines class names with clsx and merges Tailwind classes with tailwind-merge
 * This utility function is essential for conditional styling and avoiding Tailwind conflicts
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// ===== STRING UTILITIES =====

/**
 * Capitalize the first letter of a string
 */
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Convert string to title case
 */
export const toTitleCase = (str: string): string => {
  if (!str) return '';
  return str
    .toLowerCase()
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
};

/**
 * Convert camelCase to kebab-case
 */
export const camelToKebab = (str: string): string => {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
};

/**
 * Convert kebab-case to camelCase
 */
export const kebabToCamel = (str: string): string => {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * Generate a random string
 */
export const generateRandomString = (
  length: number = 8,
  charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * Truncate string with ellipsis
 */
export const truncate = (
  str: string, 
  maxLength: number, 
  suffix: string = '...'
): string => {
  if (!str || str.length <= maxLength) return str;
  return str.slice(0, maxLength - suffix.length) + suffix;
};

/**
 * Remove HTML tags from string
 */
export const stripHtml = (html: string): string => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '');
};

/**
 * Escape HTML characters
 */
export const escapeHtml = (str: string): string => {
  if (!str) return '';
  const div = document.createElement('div');
  div.textContent = str;
  return div.innerHTML;
};

/**
 * Generate slug from string
 */
export const generateSlug = (str: string): string => {
  if (!str) return '';
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// ===== NUMBER UTILITIES =====

/**
 * Format number as currency
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  } catch (error) {
    return `${currency} ${amount.toFixed(2)}`;
  }
};

/**
 * Format number with thousands separator
 */
export const formatNumber = (
  num: number,
  locale: string = 'en-US'
): string => {
  try {
    return new Intl.NumberFormat(locale).format(num);
  } catch (error) {
    return num.toString();
  }
};

/**
 * Format number as percentage
 */
export const formatPercentage = (
  num: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num / 100);
  } catch (error) {
    return `${num.toFixed(decimals)}%`;
  }
};

/**
 * Clamp number between min and max
 */
export const clamp = (num: number, min: number, max: number): number => {
  return Math.min(Math.max(num, min), max);
};

/**
 * Round number to specified decimal places
 */
export const roundTo = (num: number, decimals: number = 2): number => {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
};

/**
 * Check if number is in range
 */
export const isInRange = (
  num: number, 
  min: number, 
  max: number, 
  inclusive: boolean = true
): boolean => {
  return inclusive 
    ? num >= min && num <= max
    : num > min && num < max;
};

/**
 * Generate random number in range
 */
export const randomInRange = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

/**
 * Generate random integer in range
 */
export const randomIntInRange = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// ===== DATE UTILITIES =====

/**
 * Format date using Intl.DateTimeFormat
 */
export const formatDate = (
  date: Date | string | number,
  options: Intl.DateTimeFormatOptions = {},
  locale: string = 'en-US'
): string => {
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      throw new Error('Invalid date');
    }
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...options
    };
    
    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
  } catch (error) {
    return 'Invalid Date';
  }
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (
  date: Date | string | number,
  locale: string = 'en-US'
): string => {
  try {
    const dateObj = new Date(date);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  } catch (error) {
    return 'Invalid Date';
  }
};

/**
 * Check if date is today
 */
export const isToday = (date: Date | string | number): boolean => {
  try {
    const dateObj = new Date(date);
    const today = new Date();
    return dateObj.toDateString() === today.toDateString();
  } catch (error) {
    return false;
  }
};

/**
 * Check if date is yesterday
 */
export const isYesterday = (date: Date | string | number): boolean => {
  try {
    const dateObj = new Date(date);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return dateObj.toDateString() === yesterday.toDateString();
  } catch (error) {
    return false;
  }
};

/**
 * Get start of day
 */
export const startOfDay = (date: Date | string | number): Date => {
  const dateObj = new Date(date);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
};

/**
 * Get end of day
 */
export const endOfDay = (date: Date | string | number): Date => {
  const dateObj = new Date(date);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
};

/**
 * Add days to date
 */
export const addDays = (date: Date | string | number, days: number): Date => {
  const dateObj = new Date(date);
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
};

/**
 * Get difference in days
 */
export const diffInDays = (
  date1: Date | string | number,
  date2: Date | string | number
): number => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// ===== OBJECT UTILITIES =====

/**
 * Deep clone an object
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
};

/**
 * Deep merge objects
 */
export const deepMerge = <T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T => {
  if (!sources.length) return target;
  const source = sources.shift();
  
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key] as T[Extract<keyof T, string>], source[key] as Partial<T[Extract<keyof T, string>]>);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }
  
  return deepMerge(target, ...sources);
};

/**
 * Check if value is an object
 */
export const isObject = (item: any): item is Record<string, any> => {
  return item && typeof item === 'object' && !Array.isArray(item);
};

/**
 * Get nested object property safely
 */
export const getNestedProperty = (
  obj: any,
  path: string,
  defaultValue: any = undefined
): any => {
  try {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : defaultValue;
    }, obj);
  } catch (error) {
    return defaultValue;
  }
};

/**
 * Set nested object property
 */
export const setNestedProperty = (
  obj: any,
  path: string,
  value: any
): void => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  if (!lastKey) return;
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
};

/**
 * Remove undefined properties from object
 */
export const removeUndefined = <T extends Record<string, any>>(
  obj: T
): Partial<T> => {
  const result: Partial<T> = {};
  
  for (const key in obj) {
    if (obj[key] !== undefined) {
      result[key] = obj[key];
    }
  }
  
  return result;
};

/**
 * Pick properties from object
 */
export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  
  return result;
};

/**
 * Omit properties from object
 */
export const omit = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj } as Omit<T, K>;
  
  for (const key of keys) {
    delete (result as any)[key];
  }
  
  return result;
};

// ===== ARRAY UTILITIES =====

/**
 * Remove duplicates from array
 */
export const unique = <T>(array: T[]): T[] => {
  return Array.from(new Set(array));
};

/**
 * Remove duplicates by property
 */
export const uniqueBy = <T>(
  array: T[],
  keyFn: (item: T) => any
): T[] => {
  const seen = new Set();
  return array.filter(item => {
    const key = keyFn(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

/**
 * Chunk array into smaller arrays
 */
export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

/**
 * Shuffle array
 */
export const shuffle = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Group array by property
 */
export const groupBy = <T>(
  array: T[],
  keyFn: (item: T) => string | number
): Record<string | number, T[]> => {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<string | number, T[]>);
};

/**
 * Sort array by property
 */
export const sortBy = <T>(
  array: T[],
  keyFn: (item: T) => any,
  direction: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...array].sort((a, b) => {
    const aVal = keyFn(a);
    const bVal = keyFn(b);
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

/**
 * Find item by property
 */
export const findBy = <T>(
  array: T[],
  keyFn: (item: T) => any,
  value: any
): T | undefined => {
  return array.find(item => keyFn(item) === value);
};

// ===== VALIDATION UTILITIES =====

/**
 * Validate email address
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number (basic)
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

/**
 * Validate URL
 */
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate password strength
 */
export const validatePassword = (
  password: string,
  options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  } = {}
): { isValid: boolean; errors: string[] } => {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true
  } = options;
  
  const errors: string[] = [];
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// ===== PERFORMANCE UTILITIES =====

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Memoize function
 */
export const memoize = <T extends (...args: any[]) => any>(
  func: T,
  keyFn?: (...args: Parameters<T>) => string
): T => {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>) => {
    const key = keyFn ? keyFn(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  }) as T;
};

/**
 * Sleep function
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry function with exponential backoff
 */
export const retry = async <T>(
  func: () => Promise<T>,
  options: {
    maxAttempts?: number;
    delay?: number;
    backoffFactor?: number;
    shouldRetry?: (error: any) => boolean;
  } = {}
): Promise<T> => {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoffFactor = 2,
    shouldRetry = () => true
  } = options;
  
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await func();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts || !shouldRetry(error)) {
        throw error;
      }
      
      const waitTime = delay * Math.pow(backoffFactor, attempt - 1);
      await sleep(waitTime);
    }
  }
  
  throw lastError;
};

// ===== TYPE GUARDS =====

/**
 * Check if value is defined
 */
export const isDefined = <T>(value: T | undefined | null): value is T => {
  return value !== undefined && value !== null;
};

/**
 * Check if value is string
 */
export const isString = (value: any): value is string => {
  return typeof value === 'string';
};

/**
 * Check if value is number
 */
export const isNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value);
};

/**
 * Check if value is boolean
 */
export const isBoolean = (value: any): value is boolean => {
  return typeof value === 'boolean';
};

/**
 * Check if value is array
 */
export const isArray = <T>(value: any): value is T[] => {
  return Array.isArray(value);
};

/**
 * Check if value is function
 */
export const isFunction = (value: any): value is Function => {
  return typeof value === 'function';
};

/**
 * Check if value is empty
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

// ===== URL UTILITIES =====

/**
 * Parse query string
 */
export const parseQueryString = (queryString: string): Record<string, string> => {
  const params: Record<string, string> = {};
  const searchParams = new URLSearchParams(queryString);
  
  for (const [key, value] of searchParams) {
    params[key] = value;
  }
  
  return params;
};

/**
 * Build query string
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  }
  
  return searchParams.toString();
};

/**
 * Join URL paths
 */
export const joinPaths = (...paths: string[]): string => {
  return paths
    .map(path => path.replace(/^\/+|\/+$/g, ''))
    .filter(Boolean)
    .join('/');
};

/**
 * Get file extension
 */
export const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.slice(lastDot + 1).toLowerCase();
};

/**
 * Get filename without extension
 */
export const getFilenameWithoutExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? filename : filename.slice(0, lastDot);
};

// ===== DEVELOPMENT UTILITIES =====

/**
 * Development logger
 */
export const devLog = {
  info: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[INFO] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[WARN] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(`[ERROR] ${message}`, ...args);
    }
  },
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }
};

/**
 * Performance measurement
 */
export const measurePerformance = <T>(
  name: string,
  func: () => T
): T => {
  if (process.env.NODE_ENV === 'development') {
    const start = performance.now();
    const result = func();
    const end = performance.now();
    console.log(`[PERF] ${name} took ${end - start}ms`);
    return result;
  }
  return func();
};

/**
 * Async performance measurement
 */
export const measureAsyncPerformance = async <T>(
  name: string,
  func: () => Promise<T>
): Promise<T> => {
  if (process.env.NODE_ENV === 'development') {
    const start = performance.now();
    const result = await func();
    const end = performance.now();
    console.log(`[PERF] ${name} took ${end - start}ms`);
    return result;
  }
  return await func();
};

/**
 * Development Notes:
 * 
 * 1. String Utilities:
 *    - Comprehensive text manipulation functions
 *    - Case conversion utilities
 *    - HTML handling and sanitization
 *    - Slug generation for URLs
 * 
 * 2. Number Utilities:
 *    - Internationalization support
 *    - Currency and percentage formatting
 *    - Mathematical operations
 *    - Range validation
 * 
 * 3. Date Utilities:
 *    - Internationalization support
 *    - Relative time formatting
 *    - Date manipulation functions
 *    - Validation utilities
 * 
 * 4. Object/Array Utilities:
 *    - Deep cloning and merging
 *    - Safe property access
 *    - Array manipulation functions
 *    - Functional programming helpers
 * 
 * 5. Validation:
 *    - Common validation patterns
 *    - Password strength checking
 *    - Type validation
 *    - Empty value checking
 * 
 * 6. Performance:
 *    - Function optimization utilities
 *    - Async operation helpers
 *    - Retry mechanisms
 *    - Performance monitoring
 * 
 * Usage Examples:
 * ```tsx
 * // String utilities
 * const title = toTitleCase('hello world'); // 'Hello World'
 * const slug = generateSlug('My Blog Post!'); // 'my-blog-post'
 * 
 * // Number utilities
 * const price = formatCurrency(29.99, 'USD'); // '$29.99'
 * const percent = formatPercentage(75); // '75.0%'
 * 
 * // Date utilities
 * const formatted = formatDate(new Date(), { 
 *   year: 'numeric', 
 *   month: 'long', 
 *   day: 'numeric' 
 * }); // 'January 1, 2024'
 * 
 * const relative = formatRelativeTime(Date.now() - 3600000); // '1 hour ago'
 * 
 * // Object utilities
 * const cloned = deepClone(originalObject);
 * const merged = deepMerge(obj1, obj2, obj3);
 * const value = getNestedProperty(obj, 'user.profile.name', 'Unknown');
 * 
 * // Array utilities
 * const unique = uniqueBy(users, user => user.id);
 * const grouped = groupBy(items, item => item.category);
 * const sorted = sortBy(products, product => product.price, 'desc');
 * 
 * // Validation
 * const isValidEmail = validateEmail('<EMAIL>');
 * const passwordCheck = validatePassword('MyPassword123!');
 * 
 * // Performance
 * const debouncedSearch = debounce(searchFunction, 300);
 * const memoizedCalculation = memoize(expensiveFunction);
 * 
 * // Async utilities
 * const result = await retry(apiCall, {
 *   maxAttempts: 3,
 *   delay: 1000,
 *   shouldRetry: (error) => error.status >= 500
 * });
 * ```
 */