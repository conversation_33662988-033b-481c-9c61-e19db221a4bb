/**
 * Environment Utilities
 * 
 * This file provides utilities for managing environment variables,
 * configuration settings, and environment-specific behavior.
 * 
 * Key Features:
 * - Environment variable validation
 * - Type-safe environment access
 * - Configuration management
 * - Environment detection
 * - Feature flags
 * - Debug utilities
 * 
 * Usage Examples:
 * ```tsx
 * import { env, isProduction, getFeatureFlag } from '@/utils/environment';
 * 
 * const apiUrl = env.VITE_API_URL;
 * const isDev = !isProduction();
 * const isFeatureEnabled = getFeatureFlag('NEW_DASHBOARD');
 * ```
 */

// ===== ENVIRONMENT TYPES =====

/**
 * Environment variable schema
 */

// export interface EnvironmentVariables {
//   // App Configuration
//   VITE_APP_NAME: string;
//   VITE_APP_VERSION: string;
//   VITE_APP_DESCRIPTION: string;
//   VITE_APP_URL: string;
  
//   // API Configuration
//   VITE_API_URL: string;
//   VITE_API_VERSION: string;
//   VITE_API_TIMEOUT: string;
  
//   // Authentication
//   VITE_AUTH_DOMAIN: string;
//   VITE_AUTH_CLIENT_ID: string;
//   VITE_AUTH_REDIRECT_URI: string;
  
//   // Storage
//   VITE_STORAGE_PREFIX: string;
//   VITE_STORAGE_VERSION: string;
  
//   // Analytics
//   VITE_ANALYTICS_ID?: string;
//   VITE_ANALYTICS_ENABLED: string;
  
//   // Feature Flags
//   VITE_FEATURE_NEW_DASHBOARD?: string;
//   VITE_FEATURE_ADVANCED_SEARCH?: string;
//   VITE_FEATURE_REAL_TIME_CHAT?: string;
//   VITE_FEATURE_COURSE_RECOMMENDATIONS?: string;
  
//   // Development
//   VITE_DEBUG_ENABLED?: string;
//   VITE_LOG_LEVEL?: string;
//   VITE_MOCK_API?: string;
  
//   // External Services
//   VITE_STRIPE_PUBLIC_KEY?: string;
//   VITE_GOOGLE_MAPS_API_KEY?: string;
//   VITE_SENTRY_DSN?: string;
  
//   // Build Information
//   VITE_BUILD_TIME?: string;
//   VITE_GIT_COMMIT?: string;
//   VITE_GIT_BRANCH?: string;
// }

// /**
//  * Environment types
//  */
// export type Environment = 'development' | 'production' | 'test' | 'staging';

// /**
//  * Log levels
//  */
// export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'silent';

// /**
//  * Feature flag configuration
//  */
// export interface FeatureFlag {
//   name: string;
//   enabled: boolean;
//   description?: string;
//   environments?: Environment[];
//   rolloutPercentage?: number;
// }

// // ===== ENVIRONMENT VALIDATION =====

// /**
//  * Required environment variables
//  */
// const REQUIRED_ENV_VARS: (keyof EnvironmentVariables)[] = [
//   'VITE_APP_NAME',
//   'VITE_APP_VERSION',
//   'VITE_APP_URL',
//   'VITE_API_URL',
//   'VITE_API_VERSION',
//   'VITE_AUTH_DOMAIN',
//   'VITE_AUTH_CLIENT_ID',
//   'VITE_STORAGE_PREFIX'
// ];

// /**
//  * Validate environment variables
//  */
// const validateEnvironmentVariables = (): {
//   isValid: boolean;
//   missing: string[];
//   errors: string[];
// } => {
//   const missing: string[] = [];
//   const errors: string[] = [];
  
//   // Check required variables
//   for (const varName of REQUIRED_ENV_VARS) {
//     const value = import.meta.env[varName];
//     if (!value) {
//       missing.push(varName);
//     }
//   }
  
//   // Validate specific formats
//   const apiUrl = import.meta.env.VITE_API_URL;
//   if (apiUrl && !isValidUrl(apiUrl)) {
//     errors.push('VITE_API_URL must be a valid URL');
//   }
  
//   const appUrl = import.meta.env.VITE_APP_URL;
//   if (appUrl && !isValidUrl(appUrl)) {
//     errors.push('VITE_APP_URL must be a valid URL');
//   }
  
//   const apiTimeout = import.meta.env.VITE_API_TIMEOUT;
//   if (apiTimeout && isNaN(Number(apiTimeout))) {
//     errors.push('VITE_API_TIMEOUT must be a valid number');
//   }
  
//   return {
//     isValid: missing.length === 0 && errors.length === 0,
//     missing,
//     errors
//   };
// };

// /**
//  * Helper function to validate URLs
//  */
// const isValidUrl = (url: string): boolean => {
//   try {
//     new URL(url);
//     return true;
//   } catch {
//     return false;
//   }
// };

// // ===== ENVIRONMENT ACCESS =====

// /**
//  * Type-safe environment variable access
//  */
// export const env: EnvironmentVariables = new Proxy({} as EnvironmentVariables, {
//   get(target, prop: string) {
//     const value = import.meta.env[prop];
    
//     // Log access in development
//     if (import.meta.env.DEV && import.meta.env.VITE_DEBUG_ENABLED === 'true') {
//       console.debug(`[ENV] Accessing ${prop}: ${value}`);
//     }
    
//     return value;
//   }
// });

// /**
//  * Get environment variable with default value
//  */
// export const getEnvVar = <T = string>(
//   key: keyof EnvironmentVariables,
//   defaultValue?: T,
//   transform?: (value: string) => T
// ): T => {
//   const value = import.meta.env[key];
  
//   if (!value) {
//     if (defaultValue !== undefined) {
//       return defaultValue;
//     }
//     throw new Error(`Environment variable ${key} is not defined`);
//   }
  
//   if (transform) {
//     try {
//       return transform(value);
//     } catch (error) {
//       console.error(`Failed to transform environment variable ${key}:`, error);
//       if (defaultValue !== undefined) {
//         return defaultValue;
//       }
//       throw error;
//     }
//   }
  
//   return value as unknown as T;
// };

// /**
//  * Get boolean environment variable
//  */
// export const getBooleanEnv = (
//   key: keyof EnvironmentVariables,
//   defaultValue: boolean = false
// ): boolean => {
//   return getEnvVar(key, defaultValue, (value) => {
//     const normalized = value.toLowerCase().trim();
//     return normalized === 'true' || normalized === '1' || normalized === 'yes';
//   });
// };

// /**
//  * Get number environment variable
//  */
// export const getNumberEnv = (
//   key: keyof EnvironmentVariables,
//   defaultValue?: number
// ): number => {
//   return getEnvVar(key, defaultValue, (value) => {
//     const num = Number(value);
//     if (isNaN(num)) {
//       throw new Error(`Environment variable ${key} is not a valid number: ${value}`);
//     }
//     return num;
//   });
// };

// /**
//  * Get array environment variable (comma-separated)
//  */
// export const getArrayEnv = (
//   key: keyof EnvironmentVariables,
//   defaultValue: string[] = []
// ): string[] => {
//   return getEnvVar(key, defaultValue, (value) => {
//     return value.split(',').map(item => item.trim()).filter(Boolean);
//   });
// };

// /**
//  * Get JSON environment variable
//  */
// export const getJsonEnv = <T = any>(
//   key: keyof EnvironmentVariables,
//   defaultValue?: T
// ): T => {
//   return getEnvVar(key, defaultValue, (value) => {
//     try {
//       return JSON.parse(value);
//     } catch (error) {
//       throw new Error(`Environment variable ${key} is not valid JSON: ${value}`);
//     }
//   });
// };

// // ===== ENVIRONMENT DETECTION =====

// /**
//  * Get current environment
//  */
// export const getCurrentEnvironment = (): Environment => {
//   const nodeEnv = import.meta.env.MODE;
  
//   switch (nodeEnv) {
//     case 'development':
//       return 'development';
//     case 'production':
//       return 'production';
//     case 'test':
//       return 'test';
//     case 'staging':
//       return 'staging';
//     default:
//       return 'development';
//   }
// };

// /**
//  * Environment detection utilities
//  */
// export const isDevelopment = (): boolean => getCurrentEnvironment() === 'development';
// export const isProduction = (): boolean => getCurrentEnvironment() === 'production';
// export const isTest = (): boolean => getCurrentEnvironment() === 'test';
// export const isStaging = (): boolean => getCurrentEnvironment() === 'staging';

// /**
//  * Check if running in browser
//  */
// export const isBrowser = (): boolean => {
//   return typeof window !== 'undefined';
// };

// /**
//  * Check if running in development server
//  */
// export const isDevServer = (): boolean => {
//   return isDevelopment() && isBrowser() && !!import.meta.hot;
// };

// /**
//  * Check if running in preview mode
//  */
// export const isPreview = (): boolean => {
//   return import.meta.env.MODE === 'preview';
// };

// // ===== FEATURE FLAGS =====

// /**
//  * Feature flag definitions
//  */
// const FEATURE_FLAGS: Record<string, FeatureFlag> = {
//   NEW_DASHBOARD: {
//     name: 'NEW_DASHBOARD',
//     enabled: getBooleanEnv('VITE_FEATURE_NEW_DASHBOARD', false),
//     description: 'Enable new dashboard design',
//     environments: ['development', 'staging']
//   },
//   ADVANCED_SEARCH: {
//     name: 'ADVANCED_SEARCH',
//     enabled: getBooleanEnv('VITE_FEATURE_ADVANCED_SEARCH', true),
//     description: 'Enable advanced search functionality',
//     environments: ['development', 'staging', 'production']
//   },
//   REAL_TIME_CHAT: {
//     name: 'REAL_TIME_CHAT',
//     enabled: getBooleanEnv('VITE_FEATURE_REAL_TIME_CHAT', false),
//     description: 'Enable real-time chat feature',
//     environments: ['development', 'staging']
//   },
//   COURSE_RECOMMENDATIONS: {
//     name: 'COURSE_RECOMMENDATIONS',
//     enabled: getBooleanEnv('VITE_FEATURE_COURSE_RECOMMENDATIONS', true),
//     description: 'Enable AI-powered course recommendations',
//     environments: ['development', 'staging', 'production']
//   }
// };

// /**
//  * Get feature flag status
//  */
// export const getFeatureFlag = (flagName: string): boolean => {
//   const flag = FEATURE_FLAGS[flagName];
  
//   if (!flag) {
//     console.warn(`Feature flag '${flagName}' not found`);
//     return false;
//   }
  
//   // Check if flag is enabled for current environment
//   const currentEnv = getCurrentEnvironment();
//   if (flag.environments && !flag.environments.includes(currentEnv)) {
//     return false;
//   }
  
//   // Check rollout percentage (if specified)
//   if (flag.rolloutPercentage !== undefined) {
//     const userId = getUserId(); // You would implement this based on your auth system
//     const hash = simpleHash(userId || 'anonymous');
//     const percentage = hash % 100;
//     return percentage < flag.rolloutPercentage && flag.enabled;
//   }
  
//   return flag.enabled;
// };

// /**
//  * Get all feature flags
//  */
// export const getAllFeatureFlags = (): Record<string, boolean> => {
//   const result: Record<string, boolean> = {};
  
//   for (const [name, flag] of Object.entries(FEATURE_FLAGS)) {
//     result[name] = getFeatureFlag(name);
//   }
  
//   return result;
// };

// /**
//  * Simple hash function for rollout percentage
//  */
// const simpleHash = (str: string): number => {
//   let hash = 0;
//   for (let i = 0; i < str.length; i++) {
//     const char = str.charCodeAt(i);
//     hash = ((hash << 5) - hash) + char;
//     hash = hash & hash; // Convert to 32-bit integer
//   }
//   return Math.abs(hash);
// };

// /**
//  * Get user ID for feature flag rollout (placeholder)
//  */
// const getUserId = (): string | null => {
//   // This would be implemented based on your authentication system
//   // For now, return null or a stored user ID
//   if (isBrowser()) {
//     return localStorage.getItem('userId') || sessionStorage.getItem('userId');
//   }
//   return null;
// };

// // ===== CONFIGURATION =====

// /**
//  * Application configuration
//  */
// export const config = {
//   // App Information
//   app: {
//     name: getEnvVar('VITE_APP_NAME', 'Lemon App'),
//     version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
//     description: getEnvVar('VITE_APP_DESCRIPTION', 'Learning Management System'),
//     url: getEnvVar('VITE_APP_URL', 'http://localhost:5173')
//   },
  
//   // API Configuration
//   api: {
//     baseUrl: getEnvVar('VITE_API_URL', 'http://localhost:3000/api'),
//     version: getEnvVar('VITE_API_VERSION', 'v1'),
//     timeout: getNumberEnv('VITE_API_TIMEOUT', 10000)
//   },
  
//   // Authentication
//   auth: {
//     domain: getEnvVar('VITE_AUTH_DOMAIN'),
//     clientId: getEnvVar('VITE_AUTH_CLIENT_ID'),
//     redirectUri: getEnvVar('VITE_AUTH_REDIRECT_URI', window?.location?.origin || '')
//   },
  
//   // Storage
//   storage: {
//     prefix: getEnvVar('VITE_STORAGE_PREFIX', 'lemon_'),
//     version: getEnvVar('VITE_STORAGE_VERSION', '1')
//   },
  
//   // Analytics
//   analytics: {
//     id: getEnvVar('VITE_ANALYTICS_ID', ''),
//     enabled: getBooleanEnv('VITE_ANALYTICS_ENABLED', false)
//   },
  
//   // External Services
//   services: {
//     stripe: {
//       publicKey: getEnvVar('VITE_STRIPE_PUBLIC_KEY', '')
//     },
//     googleMaps: {
//       apiKey: getEnvVar('VITE_GOOGLE_MAPS_API_KEY', '')
//     },
//     sentry: {
//       dsn: getEnvVar('VITE_SENTRY_DSN', '')
//     }
//   },
  
//   // Development
//   development: {
//     debugEnabled: getBooleanEnv('VITE_DEBUG_ENABLED', isDevelopment()),
//     logLevel: getEnvVar('VITE_LOG_LEVEL', 'info') as LogLevel,
//     mockApi: getBooleanEnv('VITE_MOCK_API', false)
//   },
  
//   // Build Information
//   build: {
//     time: getEnvVar('VITE_BUILD_TIME', new Date().toISOString()),
//     commit: getEnvVar('VITE_GIT_COMMIT', 'unknown'),
//     branch: getEnvVar('VITE_GIT_BRANCH', 'unknown')
//   }
// };

// // ===== LOGGING CONFIGURATION =====

// /**
//  * Log level hierarchy
//  */
// const LOG_LEVELS: Record<LogLevel, number> = {
//   debug: 0,
//   info: 1,
//   warn: 2,
//   error: 3,
//   silent: 4
// };

// /**
//  * Check if log level is enabled
//  */
// export const isLogLevelEnabled = (level: LogLevel): boolean => {
//   const currentLevel = config.development.logLevel;
//   return LOG_LEVELS[level] >= LOG_LEVELS[currentLevel];
// };

// /**
//  * Environment-aware logger
//  */
// export const logger = {
//   debug: (message: string, ...args: any[]) => {
//     if (isLogLevelEnabled('debug')) {
//       console.debug(`[DEBUG] ${message}`, ...args);
//     }
//   },
//   info: (message: string, ...args: any[]) => {
//     if (isLogLevelEnabled('info')) {
//       console.info(`[INFO] ${message}`, ...args);
//     }
//   },
//   warn: (message: string, ...args: any[]) => {
//     if (isLogLevelEnabled('warn')) {
//       console.warn(`[WARN] ${message}`, ...args);
//     }
//   },
//   error: (message: string, ...args: any[]) => {
//     if (isLogLevelEnabled('error')) {
//       console.error(`[ERROR] ${message}`, ...args);
//     }
//   }
// };

// // ===== ENVIRONMENT UTILITIES =====

// /**
//  * Get environment information
//  */
// export const getEnvironmentInfo = () => {
//   return {
//     environment: getCurrentEnvironment(),
//     isDevelopment: isDevelopment(),
//     isProduction: isProduction(),
//     isTest: isTest(),
//     isStaging: isStaging(),
//     isBrowser: isBrowser(),
//     isDevServer: isDevServer(),
//     isPreview: isPreview(),
//     config: {
//       app: config.app,
//       api: {
//         ...config.api,
//         // Don't expose sensitive information
//         baseUrl: config.api.baseUrl.replace(/\/\/.*@/, '//***@')
//       },
//       build: config.build
//     },
//     featureFlags: getAllFeatureFlags(),
//     validation: validateEnvironmentVariables()
//   };
// };

// /**
//  * Initialize environment
//  */
// export const initializeEnvironment = (): void => {
//   const validation = validateEnvironmentVariables();
  
//   if (!validation.isValid) {
//     const errorMessage = [
//       'Environment validation failed:',
//       ...validation.missing.map(v => `  - Missing: ${v}`),
//       ...validation.errors.map(e => `  - Error: ${e}`)
//     ].join('\n');
    
//     if (isProduction()) {
//       throw new Error(errorMessage);
//     } else {
//       console.error(errorMessage);
//     }
//   }
  
//   // Log environment info in development
//   if (isDevelopment() && config.development.debugEnabled) {
//     console.group('🌍 Environment Information');
//     console.table({
//       Environment: getCurrentEnvironment(),
//       'App Name': config.app.name,
//       'App Version': config.app.version,
//       'API URL': config.api.baseUrl,
//       'Debug Enabled': config.development.debugEnabled,
//       'Log Level': config.development.logLevel
//     });
    
//     const enabledFlags = Object.entries(getAllFeatureFlags())
//       .filter(([, enabled]) => enabled)
//       .map(([name]) => name);
    
//     if (enabledFlags.length > 0) {
//       console.log('🚩 Enabled Feature Flags:', enabledFlags.join(', '));
//     }
    
//     console.groupEnd();
//   }
// };

// // ===== DEVELOPMENT UTILITIES =====

// /**
//  * Development utilities (only available in development)
//  */
// export const devUtils = isDevelopment() ? {
//   /**
//    * Log all environment variables
//    */
//   logEnvironmentVariables: () => {
//     console.group('🔧 Environment Variables');
    
//     const envVars = Object.keys(import.meta.env)
//       .filter(key => key.startsWith('VITE_'))
//       .sort()
//       .reduce((acc, key) => {
//         const value = import.meta.env[key];
//         // Mask sensitive values
//         const maskedValue = key.toLowerCase().includes('key') || 
//                            key.toLowerCase().includes('secret') ||
//                            key.toLowerCase().includes('token')
//           ? '***MASKED***'
//           : value;
//         acc[key] = maskedValue;
//         return acc;
//       }, {} as Record<string, any>);
    
//     console.table(envVars);
//     console.groupEnd();
//   },
  
//   /**
//    * Test feature flag
//    */
//   testFeatureFlag: (flagName: string, enabled: boolean) => {
//     if (FEATURE_FLAGS[flagName]) {
//       FEATURE_FLAGS[flagName].enabled = enabled;
//       console.log(`🚩 Feature flag '${flagName}' set to ${enabled}`);
//     } else {
//       console.warn(`🚩 Feature flag '${flagName}' not found`);
//     }
//   },
  
//   /**
//    * Get environment validation report
//    */
//   getValidationReport: () => {
//     return validateEnvironmentVariables();
//   },
  
//   /**
//    * Export environment configuration
//    */
//   exportConfig: () => {
//     return {
//       environment: getCurrentEnvironment(),
//       config,
//       featureFlags: getAllFeatureFlags(),
//       validation: validateEnvironmentVariables()
//     };
//   }
// } : undefined;

// // ===== INITIALIZATION =====

// // Initialize environment on module load
// if (isBrowser()) {
//   try {
//     initializeEnvironment();
//   } catch (error) {
//     console.error('Failed to initialize environment:', error);
//   }
// }

// // ===== EXPORTS =====

// export {
//   type EnvironmentVariables,
//   type Environment,
//   type LogLevel,
//   type FeatureFlag,
//   validateEnvironmentVariables,
//   getCurrentEnvironment,
//   config,
//   logger,
//   getEnvironmentInfo,
//   initializeEnvironment,
//   devUtils
// };

/**
 * Development Notes:
 * 
 * 1. Environment Variables:
 *    - Type-safe access to environment variables
 *    - Validation and error handling
 *    - Support for different data types
 *    - Default value handling
 * 
 * 2. Feature Flags:
 *    - Environment-specific feature toggles
 *    - Rollout percentage support
 *    - User-based feature targeting
 *    - Development testing utilities
 * 
 * 3. Configuration:
 *    - Centralized configuration management
 *    - Environment-specific settings
 *    - Service configuration
 *    - Build information
 * 
 * 4. Logging:
 *    - Environment-aware logging
 *    - Configurable log levels
 *    - Development debugging
 *    - Production safety
 * 
 * 5. Security:
 *    - Sensitive data masking
 *    - Environment validation
 *    - Production safety checks
 *    - Development-only utilities
 * 
 * Usage Examples:
 * ```tsx
 * // Basic environment access
 * import { env, config, isProduction } from '@/utils/environment';
 * 
 * const apiUrl = env.VITE_API_URL;
 * const appName = config.app.name;
 * const isProd = isProduction();
 * 
 * // Feature flags
 * import { getFeatureFlag } from '@/utils/environment';
 * 
 * const showNewDashboard = getFeatureFlag('NEW_DASHBOARD');
 * 
 * if (showNewDashboard) {
 *   // Render new dashboard
 * }
 * 
 * // Logging
 * import { logger } from '@/utils/environment';
 * 
 * logger.info('User logged in', { userId: user.id });
 * logger.error('API request failed', error);
 * 
 * // Development utilities
 * import { devUtils } from '@/utils/environment';
 * 
 * // In development console
 * devUtils?.logEnvironmentVariables();
 * devUtils?.testFeatureFlag('NEW_DASHBOARD', true);
 * 
 * // Environment information
 * import { getEnvironmentInfo } from '@/utils/environment';
 * 
 * const envInfo = getEnvironmentInfo();
 * console.log('Environment:', envInfo);
 * ```
 */