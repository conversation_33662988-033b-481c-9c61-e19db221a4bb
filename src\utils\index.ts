// /**
//  * Utils Index
//  * 
//  * This file serves as a central export hub for all utility modules.
//  * It provides a unified interface for accessing common utilities,
//  * environment configuration, and helper functions.
//  * 
//  * Key Features:
//  * - Centralized utility exports
//  * - Environment configuration
//  * - Common helper functions
//  * - Development utilities
//  * - Performance monitoring
//  * - Type safety
//  * 
//  * Usage Examples:
//  * ```tsx
//  * // Import specific utilities
//  * import { 
//  *   formatCurrency, 
//  *   validateEmail, 
//  *   debounce,
//  *   env,
//  *   config,
//  *   logger 
//  * } from '@/utils';
//  * 
//  * // Use utilities
//  * const price = formatCurrency(29.99, 'USD');
//  * const isValid = validateEmail('<EMAIL>');
//  * const debouncedFn = debounce(searchFunction, 300);
//  * const apiUrl = env.VITE_API_URL;
//  * ```
//  */

// // ===== COMMON UTILITIES =====

// export {
//   // String utilities
//   capitalize,
//   toTitleCase,
//   camelToKebab,
//   kebabToCamel,
//   generateRandomString,
//   truncate,
//   stripHtml,
//   escapeHtml,
//   generateSlug,
  
//   // Number utilities
//   formatCurrency,
//   formatNumber,
//   formatPercentage,
//   clamp,
//   roundTo,
//   isInRange,
//   randomInRange,
//   randomIntInRange,
  
//   // Date utilities
//   formatDate,
//   formatRelativeTime,
//   isToday,
//   isYesterday,
//   startOfDay,
//   endOfDay,
//   addDays,
//   diffInDays,
  
//   // Object utilities
//   deepClone,
//   deepMerge,
//   isObject,
//   getNestedProperty,
//   setNestedProperty,
//   removeUndefined,
//   pick,
//   omit,
  
//   // Array utilities
//   unique,
//   uniqueBy,
//   chunk,
//   shuffle,
//   groupBy,
//   sortBy,
//   findBy,
  
//   // Validation utilities
//   validateEmail,
//   validatePhone,
//   validateUrl,
//   validatePassword,
  
//   // Performance utilities
//   debounce,
//   throttle,
//   memoize,
//   sleep,
//   retry,
  
//   // Type guards
//   isDefined,
//   isString,
//   isNumber,
//   isBoolean,
//   isArray,
//   isFunction,
//   isEmpty,
  
//   // URL utilities
//   parseQueryString,
//   buildQueryString,
//   joinPaths,
//   getFileExtension,
//   getFilenameWithoutExtension,
  
//   // Development utilities
//   devLog,
//   measurePerformance,
//   measureAsyncPerformance
// } from './common';

// // ===== ENVIRONMENT UTILITIES =====

// // export {
// //   // Environment access
// //   env,
// //   getEnvVar,
// //   getBooleanEnv,
// //   getNumberEnv,
// //   getArrayEnv,
// //   getJsonEnv,
  
// //   // Environment detection
// //   getCurrentEnvironment,
// //   isDevelopment,
// //   isProduction,
// //   isTest,
// //   isStaging,
// //   isBrowser,
// //   isDevServer,
// //   isPreview,
  
// //   // Feature flags
// //   getFeatureFlag,
// //   getAllFeatureFlags,
  
// //   // Configuration
// //   config,
  
// //   // Logging
// //   logger,
// //   isLogLevelEnabled,
  
// //   // Environment utilities
// //   getEnvironmentInfo,
// //   initializeEnvironment,
// //   devUtils as envDevUtils,
  
// //   // Types
// //   type EnvironmentVariables,
// //   type Environment,
// //   type LogLevel,
// //   type FeatureFlag
// // } from './environment';

// // ===== ADDITIONAL UTILITIES =====

// /**
//  * Browser detection utilities
//  */
// export const browserUtils = {
//   /**
//    * Get user agent information
//    */
//   getUserAgent: (): string => {
//     return typeof navigator !== 'undefined' ? navigator.userAgent : '';
//   },
  
//   /**
//    * Check if browser supports a feature
//    */
//   supportsFeature: (feature: string): boolean => {
//     if (typeof window === 'undefined') return false;
    
//     switch (feature) {
//       case 'localStorage':
//         try {
//           const test = '__test__';
//           localStorage.setItem(test, test);
//           localStorage.removeItem(test);
//           return true;
//         } catch {
//           return false;
//         }
//       case 'sessionStorage':
//         try {
//           const test = '__test__';
//           sessionStorage.setItem(test, test);
//           sessionStorage.removeItem(test);
//           return true;
//         } catch {
//           return false;
//         }
//       case 'webWorkers':
//         return typeof Worker !== 'undefined';
//       case 'serviceWorkers':
//         return 'serviceWorker' in navigator;
//       case 'pushNotifications':
//         return 'Notification' in window;
//       case 'geolocation':
//         return 'geolocation' in navigator;
//       case 'webRTC':
//         return 'RTCPeerConnection' in window;
//       case 'webGL':
//         try {
//           const canvas = document.createElement('canvas');
//           return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
//         } catch {
//           return false;
//         }
//       default:
//         return false;
//     }
//   },
  
//   /**
//    * Get browser information
//    */
//   getBrowserInfo: () => {
//     const userAgent = browserUtils.getUserAgent();
    
//     const browsers = [
//       { name: 'Chrome', regex: /Chrome\/(\d+)/ },
//       { name: 'Firefox', regex: /Firefox\/(\d+)/ },
//       { name: 'Safari', regex: /Safari\/(\d+)/ },
//       { name: 'Edge', regex: /Edge\/(\d+)/ },
//       { name: 'Opera', regex: /Opera\/(\d+)/ }
//     ];
    
//     for (const browser of browsers) {
//       const match = userAgent.match(browser.regex);
//       if (match) {
//         return {
//           name: browser.name,
//           version: match[1],
//           userAgent
//         };
//       }
//     }
    
//     return {
//       name: 'Unknown',
//       version: 'Unknown',
//       userAgent
//     };
//   },
  
//   /**
//    * Check if mobile device
//    */
//   isMobile: (): boolean => {
//     const userAgent = browserUtils.getUserAgent();
//     return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
//   },
  
//   /**
//    * Check if tablet device
//    */
//   isTablet: (): boolean => {
//     const userAgent = browserUtils.getUserAgent();
//     return /iPad|Android(?!.*Mobile)/i.test(userAgent);
//   },
  
//   /**
//    * Check if desktop device
//    */
//   isDesktop: (): boolean => {
//     return !browserUtils.isMobile() && !browserUtils.isTablet();
//   }
// };

// /**
//  * DOM utilities
//  */
// export const domUtils = {
//   /**
//    * Wait for DOM to be ready
//    */
//   ready: (callback: () => void): void => {
//     if (typeof document === 'undefined') {
//       callback();
//       return;
//     }
    
//     if (document.readyState === 'loading') {
//       document.addEventListener('DOMContentLoaded', callback);
//     } else {
//       callback();
//     }
//   },
  
//   /**
//    * Get element by selector with type safety
//    */
//   querySelector: <T extends Element = Element>(
//     selector: string,
//     parent: Document | Element = document
//   ): T | null => {
//     return parent.querySelector<T>(selector);
//   },
  
//   /**
//    * Get elements by selector with type safety
//    */
//   querySelectorAll: <T extends Element = Element>(
//     selector: string,
//     parent: Document | Element = document
//   ): NodeListOf<T> => {
//     return parent.querySelectorAll<T>(selector);
//   },
  
//   /**
//    * Add event listener with cleanup
//    */
//   addEventListener: <K extends keyof WindowEventMap>(
//     element: Element | Window,
//     type: K,
//     listener: (event: WindowEventMap[K]) => void,
//     options?: boolean | AddEventListenerOptions
//   ): (() => void) => {
//     element.addEventListener(type, listener as EventListener, options);
//     return () => element.removeEventListener(type, listener as EventListener, options);
//   },
  
//   /**
//    * Get element dimensions
//    */
//   getDimensions: (element: Element) => {
//     const rect = element.getBoundingClientRect();
//     return {
//       width: rect.width,
//       height: rect.height,
//       top: rect.top,
//       left: rect.left,
//       right: rect.right,
//       bottom: rect.bottom
//     };
//   },
  
//   /**
//    * Check if element is in viewport
//    */
//   isInViewport: (element: Element): boolean => {
//     const rect = element.getBoundingClientRect();
//     return (
//       rect.top >= 0 &&
//       rect.left >= 0 &&
//       rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
//       rect.right <= (window.innerWidth || document.documentElement.clientWidth)
//     );
//   },
  
//   /**
//    * Scroll to element
//    */
//   scrollToElement: (
//     element: Element,
//     options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'start' }
//   ): void => {
//     element.scrollIntoView(options);
//   },
  
//   /**
//    * Copy text to clipboard
//    */
//   copyToClipboard: async (text: string): Promise<boolean> => {
//     try {
//       if (navigator.clipboard && window.isSecureContext) {
//         await navigator.clipboard.writeText(text);
//         return true;
//       } else {
//         // Fallback for older browsers
//         const textArea = document.createElement('textarea');
//         textArea.value = text;
//         textArea.style.position = 'fixed';
//         textArea.style.left = '-999999px';
//         textArea.style.top = '-999999px';
//         document.body.appendChild(textArea);
//         textArea.focus();
//         textArea.select();
//         const result = document.execCommand('copy');
//         document.body.removeChild(textArea);
//         return result;
//       }
//     } catch {
//       return false;
//     }
//   }
// };

// /**
//  * Storage utilities (enhanced)
//  */
// export const storageUtils = {
//   /**
//    * Safe localStorage operations
//    */
//   local: {
//     get: <T = any>(key: string, defaultValue?: T): T | null => {
//       try {
//         if (!browserUtils.supportsFeature('localStorage')) {
//           return defaultValue || null;
//         }
//         const item = localStorage.getItem(key);
//         return item ? JSON.parse(item) : defaultValue || null;
//       } catch {
//         return defaultValue || null;
//       }
//     },
    
//     set: (key: string, value: any): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('localStorage')) {
//           return false;
//         }
//         localStorage.setItem(key, JSON.stringify(value));
//         return true;
//       } catch {
//         return false;
//       }
//     },
    
//     remove: (key: string): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('localStorage')) {
//           return false;
//         }
//         localStorage.removeItem(key);
//         return true;
//       } catch {
//         return false;
//       }
//     },
    
//     clear: (): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('localStorage')) {
//           return false;
//         }
//         localStorage.clear();
//         return true;
//       } catch {
//         return false;
//       }
//     }
//   },
  
//   /**
//    * Safe sessionStorage operations
//    */
//   session: {
//     get: <T = any>(key: string, defaultValue?: T): T | null => {
//       try {
//         if (!browserUtils.supportsFeature('sessionStorage')) {
//           return defaultValue || null;
//         }
//         const item = sessionStorage.getItem(key);
//         return item ? JSON.parse(item) : defaultValue || null;
//       } catch {
//         return defaultValue || null;
//       }
//     },
    
//     set: (key: string, value: any): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('sessionStorage')) {
//           return false;
//         }
//         sessionStorage.setItem(key, JSON.stringify(value));
//         return true;
//       } catch {
//         return false;
//       }
//     },
    
//     remove: (key: string): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('sessionStorage')) {
//           return false;
//         }
//         sessionStorage.removeItem(key);
//         return true;
//       } catch {
//         return false;
//       }
//     },
    
//     clear: (): boolean => {
//       try {
//         if (!browserUtils.supportsFeature('sessionStorage')) {
//           return false;
//         }
//         sessionStorage.clear();
//         return true;
//       } catch {
//         return false;
//       }
//     }
//   }
// };

// /**
//  * Error handling utilities
//  */
// export const errorUtils = {
//   /**
//    * Create error with additional context
//    */
//   createError: (
//     message: string,
//     code?: string,
//     context?: Record<string, any>
//   ): Error & { code?: string; context?: Record<string, any> } => {
//     const error = new Error(message) as Error & { 
//       code?: string; 
//       context?: Record<string, any>; 
//     };
//     if (code) error.code = code;
//     if (context) error.context = context;
//     return error;
//   },
  
//   /**
//    * Handle async errors safely
//    */
//   safeAsync: async <T>(
//     fn: () => Promise<T>,
//     fallback?: T
//   ): Promise<{ data: T | null; error: Error | null }> => {
//     try {
//       const data = await fn();
//       return { data, error: null };
//     } catch (error) {
//       return { 
//         data: fallback || null, 
//         error: error instanceof Error ? error : new Error(String(error)) 
//       };
//     }
//   },
  
//   /**
//    * Handle sync errors safely
//    */
//   safeSync: <T>(
//     fn: () => T,
//     fallback?: T
//   ): { data: T | null; error: Error | null } => {
//     try {
//       const data = fn();
//       return { data, error: null };
//     } catch (error) {
//       return { 
//         data: fallback || null, 
//         error: error instanceof Error ? error : new Error(String(error)) 
//       };
//     }
//   },
  
//   /**
//    * Log error with context
//    */
//   logError: (
//     error: Error,
//     context?: Record<string, any>
//   ): void => {
//     logger.error('Error occurred:', {
//       message: error.message,
//       stack: error.stack,
//       context
//     });
//   }
// };

// /**
//  * Performance utilities
//  */
// export const perfUtils = {
//   /**
//    * Measure function execution time
//    */
//   measure: <T>(
//     name: string,
//     fn: () => T
//   ): { result: T; duration: number } => {
//     const start = performance.now();
//     const result = fn();
//     const duration = performance.now() - start;
    
//     if (isDevelopment()) {
//       logger.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
//     }
    
//     return { result, duration };
//   },
  
//   /**
//    * Measure async function execution time
//    */
//   measureAsync: async <T>(
//     name: string,
//     fn: () => Promise<T>
//   ): Promise<{ result: T; duration: number }> => {
//     const start = performance.now();
//     const result = await fn();
//     const duration = performance.now() - start;
    
//     if (isDevelopment()) {
//       logger.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
//     }
    
//     return { result, duration };
//   },
  
//   /**
//    * Create performance observer
//    */
//   observePerformance: (
//     entryTypes: string[],
//     callback: (entries: PerformanceEntry[]) => void
//   ): (() => void) | null => {
//     if (typeof PerformanceObserver === 'undefined') {
//       return null;
//     }
    
//     const observer = new PerformanceObserver((list) => {
//       callback(list.getEntries());
//     });
    
//     try {
//       observer.observe({ entryTypes });
//       return () => observer.disconnect();
//     } catch {
//       return null;
//     }
//   }
// };

// // ===== DEVELOPMENT UTILITIES =====

// /**
//  * Combined development utilities
//  */
// export const devUtils = isDevelopment() ? {
//   /**
//    * Environment utilities
//    */
//   env: envDevUtils,
  
//   /**
//    * Log all available utilities
//    */
//   logUtilities: () => {
//     console.group('🛠️ Available Utilities');
//     console.log('Common:', Object.keys({
//       capitalize, formatCurrency, formatDate, deepClone, unique,
//       validateEmail, debounce, isDefined, parseQueryString
//     }));
//     console.log('Environment:', Object.keys({
//       env, config, logger, getFeatureFlag, isDevelopment
//     }));
//     console.log('Browser:', Object.keys(browserUtils));
//     console.log('DOM:', Object.keys(domUtils));
//     console.log('Storage:', Object.keys(storageUtils));
//     console.log('Error:', Object.keys(errorUtils));
//     console.log('Performance:', Object.keys(perfUtils));
//     console.groupEnd();
//   },
  
//   /**
//    * Test all utilities
//    */
//   testUtilities: () => {
//     console.group('🧪 Testing Utilities');
    
//     // Test common utilities
//     console.log('String:', capitalize('hello world'));
//     console.log('Number:', formatCurrency(29.99));
//     console.log('Date:', formatDate(new Date()));
//     console.log('Validation:', validateEmail('<EMAIL>'));
    
//     // Test environment
//     console.log('Environment:', getCurrentEnvironment());
//     console.log('Feature Flags:', getAllFeatureFlags());
    
//     // Test browser
//     console.log('Browser:', browserUtils.getBrowserInfo());
//     console.log('Mobile:', browserUtils.isMobile());
    
//     console.groupEnd();
//   },
  
//   /**
//    * Performance benchmark
//    */
//   benchmark: () => {
//     console.group('⚡ Performance Benchmark');
    
//     // Test common operations
//     const iterations = 10000;
    
//     const { duration: cloneTime } = perfUtils.measure('Deep Clone', () => {
//       for (let i = 0; i < iterations; i++) {
//         deepClone({ a: 1, b: { c: 2 } });
//       }
//     });
    
//     const { duration: formatTime } = perfUtils.measure('Format Currency', () => {
//       for (let i = 0; i < iterations; i++) {
//         formatCurrency(Math.random() * 1000);
//       }
//     });
    
//     const { duration: validateTime } = perfUtils.measure('Validate Email', () => {
//       for (let i = 0; i < iterations; i++) {
//         validateEmail('<EMAIL>');
//       }
//     });
    
//     console.table({
//       'Deep Clone': `${cloneTime.toFixed(2)}ms`,
//       'Format Currency': `${formatTime.toFixed(2)}ms`,
//       'Validate Email': `${validateTime.toFixed(2)}ms`
//     });
    
//     console.groupEnd();
//   }
// } : undefined;

// // ===== EXPORTS =====

// export {
//   browserUtils,
//   domUtils,
//   storageUtils,
//   errorUtils,
//   perfUtils,
//   devUtils
// };

// /**
//  * Development Notes:
//  * 
//  * 1. Utility Organization:
//  *    - Common utilities for everyday operations
//  *    - Environment utilities for configuration
//  *    - Browser utilities for feature detection
//  *    - DOM utilities for safe DOM manipulation
//  *    - Storage utilities for data persistence
//  *    - Error utilities for error handling
//  *    - Performance utilities for optimization
//  * 
//  * 2. Type Safety:
//  *    - Full TypeScript support
//  *    - Generic functions where appropriate
//  *    - Type guards for runtime validation
//  *    - Safe fallbacks for browser APIs
//  * 
//  * 3. Browser Compatibility:
//  *    - Feature detection before usage
//  *    - Graceful degradation
//  *    - Polyfill-friendly design
//  *    - Cross-browser support
//  * 
//  * 4. Performance:
//  *    - Memoization for expensive operations
//  *    - Debouncing and throttling
//  *    - Performance measurement tools
//  *    - Efficient algorithms
//  * 
//  * 5. Development Experience:
//  *    - Comprehensive debugging utilities
//  *    - Performance benchmarking
//  *    - Utility testing functions
//  *    - Development-only features
//  * 
//  * Usage Examples:
//  * ```tsx
//  * // Common utilities
//  * import { formatCurrency, validateEmail, debounce } from '@/utils';
//  * 
//  * const price = formatCurrency(29.99, 'USD');
//  * const isValid = validateEmail('<EMAIL>');
//  * const debouncedSearch = debounce(searchFunction, 300);
//  * 
//  * // Environment utilities
//  * import { env, config, getFeatureFlag, logger } from '@/utils';
//  * 
//  * const apiUrl = env.VITE_API_URL;
//  * const appName = config.app.name;
//  * const showFeature = getFeatureFlag('NEW_DASHBOARD');
//  * logger.info('Application started');
//  * 
//  * // Browser utilities
//  * import { browserUtils, domUtils } from '@/utils';
//  * 
//  * const isMobile = browserUtils.isMobile();
//  * const browserInfo = browserUtils.getBrowserInfo();
//  * 
//  * domUtils.ready(() => {
//  *   console.log('DOM is ready');
//  * });
//  * 
//  * // Storage utilities
//  * import { storageUtils } from '@/utils';
//  * 
//  * storageUtils.local.set('user', { id: 1, name: 'John' });
//  * const user = storageUtils.local.get('user');
//  * 
//  * // Error handling
//  * import { errorUtils } from '@/utils';
//  * 
//  * const { data, error } = await errorUtils.safeAsync(async () => {
//  *   return await fetchUserData();
//  * });
//  * 
//  * if (error) {
//  *   errorUtils.logError(error, { userId: user.id });
//  * }
//  * 
//  * // Performance monitoring
//  * import { perfUtils } from '@/utils';
//  * 
//  * const { result, duration } = perfUtils.measure('expensive-operation', () => {
//  *   return performExpensiveCalculation();
//  * });
//  * 
//  * // Development utilities
//  * import { devUtils } from '@/utils';
//  * 
//  * // In development console
//  * devUtils?.logUtilities();
//  * devUtils?.testUtilities();
//  * devUtils?.benchmark();
//  * ```
//  */